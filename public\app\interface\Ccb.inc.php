<?php
//include_once("/etc/steelconf/debug.php") ;    //X:\steelconf_v3\out\out.txt
//DebugInfo_News("AAAAssssss");

// libing update start for include fileds 2015/08/11
//include_once( "../../sys.conf.php" );
//include_once( "HttpClient.class.php" );
include_once("../../sys.conf.php");
session_start();

if(!class_exists('HttpClient')){
	if(defined('FRONT_APP_DIR')){
		include_once( FRONT_APP_DIR."/interface/HttpClient.class.php" );
	}else{
		include_once( APP_DIR."./interface/HttpClient.class.php" );
	}
	
}
// libing update end for include fileds 2015/08/11

class Ccb {

  private static $httpRetNg = '8';                //http响应失败标记
  private static $httpRetOk = '9';                //http响应成功标记
  private static $MCH_SIT_NO1 = 'MCH_SIT_01';                //商户的一般席位号  （商户收取手续费等）
  private static $MCH_SIT_NO2 = 'MCH_SIT_02';                //商户的监管席位号 (用于实现第三方支付监管)
  private $_dao;                                    //数据访问对象

  public function __construct($dao){
    $this->_dao = $dao;
  }
  //会员资金账户记录
  private function FinanceLog($Mid, $MCH_NO, $SiteNo, $MoneyTitle, $Money, $MoneyType, $OrderID, $FinanceDesc, $ComTransType){

    $AccMoneyBefore=0;
    $AccMoneyAfter=0;
    $MoneyBefore=0;
    $MoneyAfter=0;
    $MoneyDJBefore=0;
    $MoneyDJAfter=0;
    $FinacnceExist=0; //sm_user_finance中是否存在该会员的保证金记录
    $current = $this->_dao->getRow( "SELECT * FROM sm_user_finance WHERE MCH_NO='".$MCH_NO."' AND SiteNo='".$SiteNo."'", 0);
    if (!empty($current)){
      $FinacnceExist = 1;
      $AccMoneyBefore = $current['AccMoney'];
      $AccMoneyAfter = $current['AccMoney'];
      $MoneyBefore = $current['Money'];
      $MoneyAfter = $current['Money'];
      $MoneyDJBefore = $current['MoneyDJ'];
      $MoneyDJAfter = $current['MoneyDJ'];
    }
    switch ($MoneyType) {
        case 1://入金
            $MoneyAfter = $MoneyBefore + $Money;        //虚拟账户增加资金
//            //财务到账是大宗物资系统之外的操作（资金监管系统的功能），不应在大宗物资进行相关更新处理。
//            if ($ComTransType != '1'){    //使用E商贸通监管的会员，不需要操作财务账户
//              $AccMoneyAfter = $AccMoneyBefore - $Money;  //财务账户减少资金
//            }
            break;
        case 2://出金
            $MoneyAfter = $MoneyBefore - $Money;      //虚拟账户减少资金
//            //财务到账是大宗物资系统之外的操作（资金监管系统的功能），不应在大宗物资进行相关更新处理。
//            if ($ComTransType != '1'){    //使用E商贸通监管的会员，不需要操作财务账户
//              $AccMoneyAfter = $AccMoneyBefore + $Money;  //财务账户增加资金
//            }
            break;
        case 3://冻结
            $MoneyAfter = $MoneyBefore - $Money;
            $MoneyDJAfter = $MoneyDJBefore + $Money;
            break;
        case 4://解冻
            $MoneyAfter = $MoneyBefore + $Money;
            $MoneyDJAfter = $MoneyDJBefore - $Money;
            break;
        case 7://交易费入账
            $MoneyAfter = $MoneyBefore + $Money;    //往商户席位号增加交易服务费
            break;
        case 8://交易费出账
            $MoneyAfter = $MoneyBefore - $Money;    //从会员席位号扣除交易服务费
            break;
//财务到账是大宗物资系统之外的操作（资金监管系统的功能），不应在大宗物资进行相关更新处理。
//        case 9://财务到账
//            if ($ComTransType != '1'){    //使用E商贸通监管的会员，不需要操作财务账户
//              $AccMoneyAfter = $AccMoneyBefore + $Money;  //财务账户到账注入资金
//            }
//            break;
    }
    $this->_dao->execute( "INSERT INTO sm_user_financelog SET Mid='".$Mid."', MCH_NO='".$MCH_NO."', SiteNo='".$SiteNo
            ."', MoneyTitle='".$MoneyTitle."', Money='".$Money."', MoneyType='".$MoneyType."', OrderID='".$OrderID
            ."', FinanceDesc='".$FinanceDesc."', AccMoneyBefore='".$AccMoneyBefore."', AccMoneyAfter='".$AccMoneyAfter."', MoneyBefore='".$MoneyBefore."', MoneyAfter='".$MoneyAfter
              ."', MoneyDJBefore='".$MoneyDJBefore."', MoneyDJAfter='".$MoneyDJAfter."', OperIP='".getfromip()."', CreateDate=NOW(), CreateUser='".$_SESSION['duserid']."'");
    if ($FinacnceExist == 1){
      $this->_dao->execute( "UPDATE sm_user_finance SET AccMoney='".$AccMoneyAfter."', Money='".$MoneyAfter."', MoneyDJ='".$MoneyDJAfter."' WHERE MCH_NO='".$MCH_NO."' AND SiteNo='".$SiteNo."'");
    }else{
      $this->_dao->execute( "INSERT INTO sm_user_finance SET Mid='".$Mid."', MCH_NO='".$MCH_NO."', SiteNo='".$SiteNo."', AccMoney='".$AccMoneyAfter."', Money='".$MoneyAfter."', MoneyDJ='".$MoneyDJAfter."'");
    }
  }

/*
      //判断返回值
      if ($ret == "9"){
        //成功时，获取返回的报文
        $rowReturn = $this->_dao->getRow("SELECT * FROM ccb_log_detail WHERE id = '".$PriKey."'");
        $content = base64_decode($rowReturn['ContentPost']);

        echo $this->GetFieldValue($content, "MBR_NAME");

        return "";
      }else{
        //失败
        return $ret;
      }
*/
  public function GetAllSingleValue($xml){

    $dom = new DOMDocument("1.0", "gb2312");
    $dom->loadXML($xml);
    $dom->formatOutput = true;
    //列表数据数组
    $listdata = array();

    //读取head节点
    $headnode =  $dom->getElementsByTagName("message")->item(0)->getElementsByTagName("head")->item(0);
    //读取head节点下的节点
    for ($i = 0; $i < $headnode->childNodes->length; $i ++){
      $node = $headnode->childNodes->item($i);
      if ($node->nodeName == "field"){
        $listdata[$node->attributes->getNamedItem("name")->nodeValue] = $node->nodeValue;
      }
    }

    //读取body节点
    $bodynode =  $dom->getElementsByTagName("message")->item(0)->getElementsByTagName("body")->item(0);
    //读取body节点下的节点
    for ($i = 0; $i < $bodynode->childNodes->length; $i ++){
      $node = $bodynode->childNodes->item($i);
      if ($node->nodeName == "field"){
        $listdata[$node->attributes->getNamedItem("name")->nodeValue] = $node->nodeValue;
      }
    }

    //返回列表数据
    return $listdata;

  }

  public function GetListData($xml){

    $dom = new DOMDocument("1.0", "gb2312");
    $dom->loadXML($xml);
    $dom->formatOutput = true;
    //列表数据数组
    $listdata = array();
    //读取body节点
    $bodynode =  $dom->getElementsByTagName("message")->item(0)->getElementsByTagName("body")->item(0);
    if ($bodynode->getElementsByTagName("field-list")->length > 0){
      //读取body节点下的field-list节点(根field-list节点除外)
      for ($i = 0; $i < $bodynode->getElementsByTagName("field-list")->item(0)->getElementsByTagName("field-list")->length; $i ++){
        $fieldlist = $bodynode->getElementsByTagName("field-list")->item(0)->getElementsByTagName("field-list")->item($i);
        //单行数据数组
        $rowdata = array();
        for ($j = 0; $j < $fieldlist->getElementsByTagName("field")->length; $j ++){
          $field = $fieldlist->getElementsByTagName("field")->item($j);
          $rowdata[$field->attributes->getNamedItem("name")->nodeValue] = $field->nodeValue;
        }
        $listdata[$fieldlist->attributes->getNamedItem("name")->nodeValue] = $rowdata;
        unset($rowdata);
      }
      //返回列表数据
      return $listdata;
    }else{
      //返回空数组
      return array();
    }

  }

  public function GetFieldValue($xml, $fieldName){

    $dom = new DOMDocument("1.0", "gb2312");
    $dom->loadXML($xml);
    $dom->formatOutput = true;

    //读取head节点
    $headnode =  $dom->getElementsByTagName("message")->item(0)->getElementsByTagName("head")->item(0);
    //读取head节点下的节点
    for ($i = 0; $i < $headnode->childNodes->length; $i ++){
      $node = $headnode->childNodes->item($i);
      if ($node->nodeName == "field" && $node->attributes->getNamedItem("name")->nodeValue == $fieldName){
        return $node->nodeValue;
      }
    }

    //读取body节点
    $bodynode =  $dom->getElementsByTagName("message")->item(0)->getElementsByTagName("body")->item(0);
    //读取body节点下的节点
    for ($i = 0; $i < $bodynode->childNodes->length; $i ++){
      $node = $bodynode->childNodes->item($i);
      if ($node->nodeName == "field" && $node->attributes->getNamedItem("name")->nodeValue == $fieldName){
        return $node->nodeValue;
      }
    }

    //未找到对应field，返回空串
    return "";

  }

  public function GetXml($arrHead, $arrBody, $arrFieldListBranch, $fieldListName){
    //=========== 生成xml start ===========
    $dom = new DOMDocument("1.0", "gb2312");
    $dom->formatOutput = true;

    //message
    $message = $dom->createElement("message");
    $dom->appendChild($message);

    //head
    $head = $dom->createElement("head");
    $message->appendChild($head);
    //遍历
    foreach($arrHead as $key=>$value){
      //head field
      $field = $dom->createElement("field");
      $head->appendChild($field);
      //create text node
      if ($value != ""){
        $text = $dom->createCDATASection($value);
        $field->appendChild($text);
      }
      //create Attribute
      $attribute = $dom->createAttribute("name");
      $attribute->value = $key;
      $field->appendChild($attribute);
    }

    //body
    $body = $dom->createElement("body");
    $message->appendChild($body);
    //遍历
    foreach($arrBody as $key=>$value){
      //body field
      $field = $dom->createElement("field");
      $body->appendChild($field);
      //create text node
      if ($value != ""){
        $text = $dom->createCDATASection($value);
        $field->appendChild($text);
      }
      //create Attribute
      $attribute = $dom->createAttribute("name");
      $attribute->value = $key;
      $field->appendChild($attribute);
    }

    //判断是否有明细记录
    if (!empty($arrFieldListBranch)){
      //fieldlistroot
      $fieldlistroot = $dom->createElement("field-list");
      $body->appendChild($fieldlistroot);
      //create Attribute
      $attribute = $dom->createAttribute("name");
      $attribute->value = $fieldListName;
      $fieldlistroot->appendChild($attribute);

      foreach($arrFieldListBranch as $key=>$arrBranchFields){
        //fieldlistbranch
        $fieldlistbranch = $dom->createElement("field-list");
        $fieldlistroot->appendChild($fieldlistbranch);
        //create Attribute
        $attribute = $dom->createAttribute("name");
        $attribute->value = $key;
        $fieldlistbranch->appendChild($attribute);
        //添加fieldlistbranch下级的field
        foreach($arrBranchFields as $branchkey=>$branchvalue){
          //fieldlistbranchfield
          $fieldlistbranchfield = $dom->createElement("field");
          $fieldlistbranch->appendChild($fieldlistbranchfield);
          //create text node
          if ($branchvalue != ""){
            $text = $dom->createCDATASection($branchvalue);
            $fieldlistbranchfield->appendChild($text);
          }
          //create Attribute
          $attribute = $dom->createAttribute("name");
          $attribute->value = $branchkey;
          $fieldlistbranchfield->appendChild($attribute);
        }
      }
    }
    $xml = $dom->saveXML();
    return $xml;
  }

  //1.会员信息传输
  //head无自定义参数，仅需设置body参数
  public function f_3FC001($paraBody){

    //根据席位号，反查会员信息
    $minfo = $this->_dao->getRow( "SELECT * FROM sm_user_ccb a inner join sys_company b ON a.MID=b.ID WHERE a.SPOT_SIT_NO ='".$paraBody["SPOT_SIT_NO"]."' LIMIT 1", 0);
    if (empty($minfo)){
      return array("resp_code" => "E000005", "resp_msg" => "席位号不存在", "PriKey" => $PriKey);
    }

    //生成GUID
    $GUID = $this->_dao->getOne("SELECT REPLACE( UUID() ,'-','') AS GUID", 0);
    //产生主键ID
    $this->_dao->execute( "INSERT INTO ccb_log SET GUID='".$GUID."', ComTransType='".$minfo['ComTransType']."'");
    $PriKey = $this->_dao->insert_id();
    //商户系统流水号，唯一标识一笔交易 Char(12)
    $chanl_flow_no =  substr("000000000000".$PriKey, -12);

    //head
    $arrHeadField = array(
                          "version"=>"100",
                          "type"=>"0200",
                          "chanl_no"=>"30",
                          "chanl_sub_no"=>"3001",
                          "chanl_date"=> date("Ymd"),   //yyyymmdd
                          "chanl_time"=> date("His"),   //HHMMSS
                          "ectip_date"=>"",
                          "chanl_flow_no"=> $chanl_flow_no,
                          "ectip_flow_no"=>"",
                          "chanl_trad_no"=>"3FC001",
                          "term_inf"=> getfromip(),
                          "resp_code"=>"",
                          "resp_msg"=>"",
                          "page"=>"",
                          "maxrow"=>"",
                          "locstr"=>"",
                          "tot_rec"=>""
                          );
    //body
    $arrBodyField = array(
                          "MCH_NO"=> $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'],  //Y 商户编号
                          "MBR_CERT_TYPE"=> $paraBody["MBR_CERT_TYPE"],           //Y 会员证件类型
                          "MBR_CERT_NO"=> $paraBody["MBR_CERT_NO"],               //Y 会员证件号码
                          "SPOT_SIT_NO"=> $paraBody["SPOT_SIT_NO"],               //实时记账模式席位号
                          "FUTURE_SIT_NO"=> $paraBody["FUTURE_SIT_NO"],           //日终清算模式席位号
                          "MBR_NAME"=> $paraBody["MBR_NAME"],                     //Y 会员名称
                          "MBR_SPE_ACCT_NO"=> $paraBody["MBR_SPE_ACCT_NO"],       //Y 会员指定账号
                          "MBR_SPE_ACCT_BKID"=> $paraBody["MBR_SPE_ACCT_BKID"],   //Y 会员账号行别  0-建行；1-非建行
                          "MBR_CONTACT"=> $paraBody["MBR_CONTACT"],               //会员联系人
                          "MBR_PHONE_NUM"=> $paraBody["MBR_PHONE_NUM"],           //会员联系方式
                          "MBR_ADDR"=> $paraBody["MBR_ADDR"],                     //会员地址
                          "MBR_ANNUAL_FEE_AMT"
                                => $paraBody["MBR_ANNUAL_FEE_AMT"],               //会员年费金额
                          "MBR_INOUT_AMT_SVC_AMT"
                                => $paraBody["MBR_INOUT_AMT_SVC_AMT"],            //会员出入金手续费金额
                          "MBR_INOUT_AMT_SVC_DRAWEE"
                                => $paraBody["MBR_INOUT_AMT_SVC_DRAWEE"],         //Y 会员出入金手续费付费方
                          "MBR_INOUT_AMT_SVC_RCV_STY"
                                => $paraBody["MBR_INOUT_AMT_SVC_RCV_STY"],        //Y 会员出入金手续费收取方式
                          "SIGNED_DATE"=> $paraBody["SIGNED_DATE"],               //Y 签约日期
                          "DIS_SGN_DATE"=> $paraBody["DIS_SGN_DATE"],             //解约日期
                          "MBR_STS"=> $paraBody["MBR_STS"],                       //Y 会员状态
                          "RMRK"=> $paraBody["RMRK"]                              //备注
                          );
    //生成xml
    $xml = $this->GetXml($arrHeadField, $arrBodyField);
    $base64xml = base64_encode($xml);
    //=========== 生成xml end ===========

    //echo $xml;

    //对GUID+CusNo进行MD5
    $CusNo = $GLOBALS['danbaoSV'][$minfo['ComTransType']]['CusNo'];
    $Sign = strtoupper(md5($GUID.$CusNo));
    //插入日志主表
    $this->_dao->execute( "UPDATE ccb_log SET CusNo ='".$CusNo."', Sign='".$Sign."', SiteNo='".$paraBody["SPOT_SIT_NO"]
                        ."', SyncType='1', InterFaceType='1', OperDate=NOW(), CcbPostDate=NULL, CcbReturnDate=NULL, IP='".getfromip()."', Title='".base64_encode("会员信息传输[3FC001]")."', Status=0"
                        ." WHERE id =".$PriKey);
    //插入日志内容表
    $this->_dao->execute( "INSERT INTO ccb_log_detail SET ID='".$PriKey."', ContentTypePost='1',  ContentPost='".$base64xml."', FileName='', ReturnInfor=''");
    //【1】.提交http请求
    $arr=array('PriKey' => $PriKey, 'Sign' => $Sign, 'SyncType' => 1, 'CusNo' => base64_encode($CusNo));

    $client = new HttpClient($GLOBALS['danbaoSV'][$minfo['ComTransType']]['site']);
    $client->setDebug(false);
    $client->setMaxRedirects(20);
    if (!$client->post($GLOBALS['danbaoSV'][$minfo['ComTransType']]['url'], $arr)) {
      return array("resp_code" => "E000001", "resp_msg" => "网络连接错误", "PriKey" => $PriKey);
    }else{
      //判断http响应值
      if ($client->getContent() == self::$httpRetOk){
        //从ccb_log_detail表中读取返回的结果
        $logdetail = $this->_dao->getRow( "SELECT * FROM ccb_log_detail WHERE ID='".$PriKey."'", 0);
        if( empty( $logdetail ) ){
          return array("resp_code" => "E000002", "resp_msg" => "读取返回数据错误", "PriKey" => $PriKey);
        }
        //4.解析xml内容
        $content = base64_decode($logdetail['ContentReturn']);
        if ($content == ""){
          return array("resp_code" => "E000003", "resp_msg" => "解析返回数据错误", "PriKey" => $PriKey);
        }
        $arrSingle = $this->GetAllSingleValue($content);
        $arrList = $this->GetListData($content);
        //更新ccb_log_detail
        $this->_dao->execute( "UPDATE ccb_log_detail SET ReturnInfor ='".base64_encode($arrSingle["resp_msg"])."' WHERE ID =".$PriKey);
        return array("resp_code" => $arrSingle["resp_code"]
                   , "resp_msg" => $arrSingle["resp_msg"]
                   , "PriKey" => $PriKey
                   , "arrSingle" => $arrSingle
                   , "arrList" => $arrList);
      }else{
        return array("resp_code" => "E000004", "resp_msg" => "接口服务器返回错误", "PriKey" => $PriKey);
      }
    }

  }
  //2.会员签约信息查询（商户-银行）
  //head无自定义参数，仅需设置body参数
  public function f_3FC014($paraBody){

    if ($paraBody["FUNC_CODE"] == "1"){
      //根据席位号，反查会员信息
      $minfo = $this->_dao->getRow( "SELECT * FROM sm_user_ccb a inner join sys_company b ON a.MID=b.ID WHERE a.SPOT_SIT_NO ='".$paraBody["SIT_NO"]."' LIMIT 1", 0);
    }else if ($paraBody["FUNC_CODE"] == "2"){
      //根据证件号，反查会员信息
      $minfo = $this->_dao->getRow( "SELECT * FROM sm_user_ccb a inner join sys_company b ON a.MID=b.ID WHERE a.MBR_CERT_TYPE ='".$paraBody["CERT_TYPE"]."' AND a.MBR_CERT_NO ='".$paraBody["CERT_NO"]."' LIMIT 1", 0);
    }
    if (empty($minfo)){
      return array("resp_code" => "E000005", "resp_msg" => "席位号不存在", "PriKey" => $PriKey);
    }

    //生成GUID
    $GUID = $this->_dao->getOne("SELECT REPLACE( UUID() ,'-','') AS GUID", 0);
    //产生主键ID
    $this->_dao->execute( "INSERT INTO ccb_log SET GUID='".$GUID."', ComTransType='".$minfo['ComTransType']."'");
    $PriKey = $this->_dao->insert_id();
    //商户系统流水号，唯一标识一笔交易 Char(12)
    $chanl_flow_no =  substr("000000000000".$PriKey, -12);

    //head
    $arrHeadField = array(
                          "version"=>"100",
                          "type"=>"0200",
                          "chanl_no"=>"30",
                          "chanl_sub_no"=>"3001",
                          "chanl_date"=> date("Ymd"),   //yyyymmdd
                          "chanl_time"=> date("His"),   //HHMMSS
                          "ectip_date"=>"",
                          "chanl_flow_no"=> $chanl_flow_no,
                          "ectip_flow_no"=>"",
                          "chanl_trad_no"=>"3FC014",
                          "term_inf"=> getfromip(),
                          "resp_code"=>"",
                          "resp_msg"=>"",
                          "page"=>"",
                          "maxrow"=>"",
                          "locstr"=>"",
                          "tot_rec"=>""
                          );
    //body
    $arrBodyField = array(
                          "MCH_NO"=> $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'],                     //Y 商户编号
                          "FUNC_CODE"=> $paraBody["FUNC_CODE"],         //Y 功能号  1-按席位号查询; 2-按证件查询
                          "SIT_NO"=> $paraBody["SIT_NO"],               //席位号
                          "CERT_TYPE"=> $paraBody["CERT_TYPE"],         //证件类型
                          "CERT_NO"=> $paraBody["CERT_NO"]              //证件号码
                          );
    //生成xml
    $xml = $this->GetXml($arrHeadField, $arrBodyField);
    $base64xml = base64_encode($xml);
    //=========== 生成xml end ===========

    //echo $xml;

    //对GUID+CusNo进行MD5
    $CusNo = $GLOBALS['danbaoSV'][$minfo['ComTransType']]['CusNo'];
    $Sign = strtoupper(md5($GUID.$CusNo));
    //插入日志主表
    $this->_dao->execute( "UPDATE ccb_log SET CusNo ='".$CusNo."', Sign='".$Sign."', SiteNo='".$paraBody["SIT_NO"]
                        ."', SyncType='1', InterFaceType='1', OperDate=NOW(), CcbPostDate=NULL, CcbReturnDate=NULL, IP='".getfromip()."', Title='".base64_encode("会员签约信息查询（商户->银行）[3FC014]")."', Status=0"
                        ." WHERE id =".$PriKey);
    //插入日志内容表
    $this->_dao->execute( "INSERT INTO ccb_log_detail SET ID='".$PriKey."', ContentTypePost='1',  ContentPost='".$base64xml."', FileName='', ReturnInfor=''");
    //【1】.提交http请求
    $arr=array('PriKey' => $PriKey, 'Sign' => $Sign, 'SyncType' => 1, 'CusNo' => base64_encode($CusNo));

    $client = new HttpClient($GLOBALS['danbaoSV'][$minfo['ComTransType']]['site']);
    $client->setDebug(false);
    $client->setMaxRedirects(20);
    if (!$client->post($GLOBALS['danbaoSV'][$minfo['ComTransType']]['url'], $arr)) {
      return array("resp_code" => "E000001", "resp_msg" => "网络连接错误", "PriKey" => $PriKey);
    }else{
      //判断http响应值
      if ($client->getContent() == self::$httpRetOk){
        //从ccb_log_detail表中读取返回的结果
        $logdetail = $this->_dao->getRow( "SELECT * FROM ccb_log_detail WHERE ID='".$PriKey."'", 0);
        if( empty( $logdetail ) ){
          return array("resp_code" => "E000002", "resp_msg" => "读取返回数据错误", "PriKey" => $PriKey);
        }
        //4.解析xml内容
        $content = base64_decode($logdetail['ContentReturn']);
        if ($content == ""){
          return array("resp_code" => "E000003", "resp_msg" => "解析返回数据错误", "PriKey" => $PriKey);
        }
        $arrSingle = $this->GetAllSingleValue($content);
        $arrList = $this->GetListData($content);
        //更新ccb_log_detail
        $this->_dao->execute( "UPDATE ccb_log_detail SET ReturnInfor ='".base64_encode($arrSingle["resp_msg"])."' WHERE ID =".$PriKey);
        return array("resp_code" => $arrSingle["resp_code"]
                   , "resp_msg" => $arrSingle["resp_msg"]
                   , "PriKey" => $PriKey
                   , "arrSingle" => $arrSingle
                   , "arrList" => $arrList);
      }else{
        return array("resp_code" => "E000004", "resp_msg" => "接口服务器返回错误", "PriKey" => $PriKey);
      }
    }

  }
  //3.账户余额查询
  //head无自定义参数，仅需设置body参数
  public function f_3FC006($paraBody){

    if ($paraBody["FUNC_CODE"] == "1"){
      //根据席位号，反查会员信息
      $minfo = $this->_dao->getRow( "SELECT * FROM sm_user_ccb a inner join sys_company b ON a.MID=b.ID WHERE a.SPOT_SIT_NO ='".$paraBody["SIT_NO"]."' LIMIT 1", 0);
    }else if ($paraBody["FUNC_CODE"] == "2"){
      //商户查询时，查大宗资金监管平台
      //$minfo['ComTransType'] = 3;
      $minfo['ComTransType'] = $paraBody["ComTransType"] == "" ? 3 : $paraBody["ComTransType"];
    }
    if (empty($minfo)){
      return array("resp_code" => "E000005", "resp_msg" => "席位号不存在", "PriKey" => $PriKey);
    }

    //生成GUID
    $GUID = $this->_dao->getOne("SELECT REPLACE( UUID() ,'-','') AS GUID", 0);
    //产生主键ID
    $this->_dao->execute( "INSERT INTO ccb_log SET GUID='".$GUID."', ComTransType='".$minfo['ComTransType']."'");
    $PriKey = $this->_dao->insert_id();
    //商户系统流水号，唯一标识一笔交易 Char(12)
    $chanl_flow_no =  substr("000000000000".$PriKey, -12);

    //head
    $arrHeadField = array(
                          "version"=>"100",
                          "type"=>"0200",
                          "chanl_no"=>"30",
                          "chanl_sub_no"=>"3001",
                          "chanl_date"=> date("Ymd"),   //yyyymmdd
                          "chanl_time"=> date("His"),   //HHMMSS
                          "ectip_date"=>"",
                          "chanl_flow_no"=> $chanl_flow_no,
                          "ectip_flow_no"=>"",
                          "chanl_trad_no"=>"3FC006",
                          "term_inf"=> getfromip(),
                          "resp_code"=>"",
                          "resp_msg"=>"",
                          "page"=>"",
                          "maxrow"=>"",
                          "locstr"=>"",
                          "tot_rec"=>""
                          );
    //body
    $arrBodyField = array(
                          "FUNC_CODE"=> $paraBody["FUNC_CODE"],         //Y 功能号  1-会员查询；2-商户查询
                          "MCH_NO"=> $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'],                     //Y 商户编号
                          "SIT_NO"=> $paraBody["SIT_NO"],               //席位号  功能号=“1”会员时输入
                          "MCH_SIT_TYP"=> $paraBody["MCH_SIT_TYP"]      //商户席位类型 功能号=“2”商户时输入：1-收益账户，2-浮动盈亏账户
                          );
    //生成xml
    $xml = $this->GetXml($arrHeadField, $arrBodyField);
    $base64xml = base64_encode($xml);
    //=========== 生成xml end ===========

    //echo $xml;

    //对GUID+CusNo进行MD5
    $CusNo = $GLOBALS['danbaoSV'][$minfo['ComTransType']]['CusNo'];
    $Sign = strtoupper(md5($GUID.$CusNo));
    //插入日志主表
    $this->_dao->execute( "UPDATE ccb_log SET CusNo ='".$CusNo."', Sign='".$Sign."', SiteNo='".$paraBody["SIT_NO"]
                        ."', SyncType='1', InterFaceType='1', OperDate=NOW(), CcbPostDate=NULL, CcbReturnDate=NULL, IP='".getfromip()."', Title='".base64_encode("账户余额查询[3FC006]")."', Status=0"
                        ." WHERE id =".$PriKey);
    //插入日志内容表
    $this->_dao->execute( "INSERT INTO ccb_log_detail SET ID='".$PriKey."', ContentTypePost='1',  ContentPost='".$base64xml."', FileName='', ReturnInfor=''");
    //【1】.提交http请求
    $arr=array('PriKey' => $PriKey, 'Sign' => $Sign, 'SyncType' => 1, 'CusNo' => base64_encode($CusNo));

    $client = new HttpClient($GLOBALS['danbaoSV'][$minfo['ComTransType']]['site']);
    $client->setDebug(false);
    $client->setMaxRedirects(20);
    if (!$client->post($GLOBALS['danbaoSV'][$minfo['ComTransType']]['url'], $arr)) {
      return array("resp_code" => "E000001", "resp_msg" => "网络连接错误", "PriKey" => $PriKey);
    }else{
      //判断http响应值
      if ($client->getContent() == self::$httpRetOk){
        //从ccb_log_detail表中读取返回的结果
        $logdetail = $this->_dao->getRow( "SELECT * FROM ccb_log_detail WHERE ID='".$PriKey."'", 0);
        if( empty( $logdetail ) ){
          return array("resp_code" => "E000002", "resp_msg" => "读取返回数据错误", "PriKey" => $PriKey);
        }
        //4.解析xml内容
        $content = base64_decode($logdetail['ContentReturn']);
        if ($content == ""){
          return array("resp_code" => "E000003", "resp_msg" => "解析返回数据错误", "PriKey" => $PriKey);
        }
        $arrSingle = $this->GetAllSingleValue($content);
        $arrList = $this->GetListData($content);
        //更新ccb_log_detail
        $this->_dao->execute( "UPDATE ccb_log_detail SET ReturnInfor ='".base64_encode($arrSingle["resp_msg"])."' WHERE ID =".$PriKey);
        return array("resp_code" => $arrSingle["resp_code"]
                   , "resp_msg" => $arrSingle["resp_msg"]
                   , "PriKey" => $PriKey
                   , "arrSingle" => $arrSingle
                   , "arrList" => $arrList);
      }else{
        return array("resp_code" => "E000004", "resp_msg" => "接口服务器返回错误", "PriKey" => $PriKey);
      }
    }

  }
  //4.账户明细查询
  //head定义明细的“查询第几页、每页返回的最大记录笔数”等信息
  public function f_3FC007($paraHead, $paraBody){

    if ($paraBody["FUNC_CODE"] == "1"){
      //根据席位号，反查会员信息
      $minfo = $this->_dao->getRow( "SELECT * FROM sm_user_ccb a inner join sys_company b ON a.MID=b.ID WHERE a.SPOT_SIT_NO ='".$paraBody["SIT_NO"]."' LIMIT 1", 0);
    }else if ($paraBody["FUNC_CODE"] == "2"){
      //商户查询时，查大宗资金监管平台
      //$minfo['ComTransType'] = 3;
      $minfo['ComTransType'] = $paraBody["ComTransType"] == "" ? 3 : $paraBody["ComTransType"];
    }
    if (empty($minfo)){
      return array("resp_code" => "E000005", "resp_msg" => "席位号不存在", "PriKey" => $PriKey);
    }

    //生成GUID
    $GUID = $this->_dao->getOne("SELECT REPLACE( UUID() ,'-','') AS GUID", 0);
    //产生主键ID
    $this->_dao->execute( "INSERT INTO ccb_log SET GUID='".$GUID."', ComTransType='".$minfo['ComTransType']."'");
    $PriKey = $this->_dao->insert_id();
    //商户系统流水号，唯一标识一笔交易 Char(12)
    $chanl_flow_no =  substr("000000000000".$PriKey, -12);

    //head
    $arrHeadField = array(
                          "version"=>"100",
                          "type"=>"0200",
                          "chanl_no"=>"30",
                          "chanl_sub_no"=>"3001",
                          "chanl_date"=> date("Ymd"),   //yyyymmdd
                          "chanl_time"=> date("His"),   //HHMMSS
                          "ectip_date"=>"",
                          "chanl_flow_no"=> $chanl_flow_no,
                          "ectip_flow_no"=>"",
                          "chanl_trad_no"=>"3FC007",
                          "term_inf"=> getfromip(),
                          "resp_code"=>"",
                          "resp_msg"=>"",
                          "page"=> $paraHead["page"],         //查询页号
                          "maxrow"=> $paraHead["maxrow"],     //每页最大返回笔数
                          "locstr"=> $paraHead["locstr"],     //定位串
                          "tot_rec"=>""
                          );
    //body
    $arrBodyField = array(
                          "FUNC_CODE"=> $paraBody["FUNC_CODE"],         //Y 功能号 1-会员；2-商户
                          "MCH_NO"=> $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'],                     //商户编号
                          "SIT_NO"=> $paraBody["SIT_NO"],               //席位号  功能号=“1”会员时输入
                          "MCH_SIT_TYP"=> $paraBody["MCH_SIT_TYP"],     //商户席位类型 功能号=“2”商户时输入：1-收益账户，2-浮动盈亏账户
                          "STRT_DT"=> $paraBody["STRT_DT"],             //Y 起始日期
                          "END_DT"=> $paraBody["END_DT"],               //Y 终止日期
                          "INQ_AMT_TYP"=> $paraBody["INQ_AMT_TYP"],     //Y 查询资金类别 0-全部；1-出入金
                          "MCH_INQ_TM"=> $paraBody["MCH_INQ_TM"]        //商户查询时间 送入该栏位则只查询当日该时间点之后发生的出入金记录。时间以银行为准。
                          );
    //生成xml
    $xml = $this->GetXml($arrHeadField, $arrBodyField);
    $base64xml = base64_encode($xml);
    //=========== 生成xml end ===========

    //echo $xml;

    //对GUID+CusNo进行MD5
    $CusNo = $GLOBALS['danbaoSV'][$minfo['ComTransType']]['CusNo'];
    $Sign = strtoupper(md5($GUID.$CusNo));
    //插入日志主表
    $this->_dao->execute( "UPDATE ccb_log SET CusNo ='".$CusNo."', Sign='".$Sign."', SiteNo='".$paraBody["SIT_NO"]
                        ."', SyncType='1', InterFaceType='1', OperDate=NOW(), CcbPostDate=NULL, CcbReturnDate=NULL, IP='".getfromip()."', Title='".base64_encode("账户明细查询[3FC007]")."', Status=0"
                        ." WHERE id =".$PriKey);
    //插入日志内容表
    $this->_dao->execute( "INSERT INTO ccb_log_detail SET ID='".$PriKey."', ContentTypePost='1',  ContentPost='".$base64xml."', FileName='', ReturnInfor=''");
    //【1】.提交http请求
    $arr=array('PriKey' => $PriKey, 'Sign' => $Sign, 'SyncType' => 1, 'CusNo' => base64_encode($CusNo));

    $client = new HttpClient($GLOBALS['danbaoSV'][$minfo['ComTransType']]['site']);
    $client->setDebug(false);
    $client->setMaxRedirects(20);
    if (!$client->post($GLOBALS['danbaoSV'][$minfo['ComTransType']]['url'], $arr)) {
      return array("resp_code" => "E000001", "resp_msg" => "网络连接错误", "PriKey" => $PriKey);
    }else{
      //判断http响应值
      if ($client->getContent() == self::$httpRetOk){
        //从ccb_log_detail表中读取返回的结果
        $logdetail = $this->_dao->getRow( "SELECT * FROM ccb_log_detail WHERE ID='".$PriKey."'", 0);
        if( empty( $logdetail ) ){
          return array("resp_code" => "E000002", "resp_msg" => "读取返回数据错误", "PriKey" => $PriKey);
        }
        //4.解析xml内容
        $content = base64_decode($logdetail['ContentReturn']);
        if ($content == ""){
          return array("resp_code" => "E000003", "resp_msg" => "解析返回数据错误", "PriKey" => $PriKey);
        }
        $arrSingle = $this->GetAllSingleValue($content);
        $arrList = $this->GetListData($content);
        //更新ccb_log_detail
        $this->_dao->execute( "UPDATE ccb_log_detail SET ReturnInfor ='".base64_encode($arrSingle["resp_msg"])."' WHERE ID =".$PriKey);
        return array("resp_code" => $arrSingle["resp_code"]
                   , "resp_msg" => $arrSingle["resp_msg"]
                   , "PriKey" => $PriKey
                   , "arrSingle" => $arrSingle
                   , "arrList" => $arrList);
      }else{
        return array("resp_code" => "E000004", "resp_msg" => "接口服务器返回错误", "PriKey" => $PriKey);
      }
    }

  }
  //5.会员入金
  //head无自定义参数，仅需设置body参数
  public function f_3FC002($paraBody){

    //根据席位号，反查会员信息
    $minfo = $this->_dao->getRow( "SELECT * FROM sm_user_ccb a inner join sys_company b ON a.MID=b.ID WHERE a.SPOT_SIT_NO ='".$paraBody["IN_AMT_SIT_NO"]."' LIMIT 1", 0);
    if (empty($minfo)){
      return array("resp_code" => "E000005", "resp_msg" => "席位号不存在", "PriKey" => $PriKey);
    }

    //生成GUID
    $GUID = $this->_dao->getOne("SELECT REPLACE( UUID() ,'-','') AS GUID", 0);
    //产生主键ID
    $this->_dao->execute( "INSERT INTO ccb_log SET GUID='".$GUID."', ComTransType='".$minfo['ComTransType']."'");
    $PriKey = $this->_dao->insert_id();
    //商户系统流水号，唯一标识一笔交易 Char(12)
    $chanl_flow_no =  substr("000000000000".$PriKey, -12);

    //head
    $arrHeadField = array(
                          "version"=>"100",
                          "type"=>"0200",
                          "chanl_no"=>"30",
                          "chanl_sub_no"=>"3001",
                          "chanl_date"=> date("Ymd"),   //yyyymmdd
                          "chanl_time"=> date("His"),   //HHMMSS
                          "ectip_date"=>"",
                          "chanl_flow_no"=> $chanl_flow_no,
                          "ectip_flow_no"=>"",
                          "chanl_trad_no"=>"3FC002",
                          "term_inf"=> getfromip(),
                          "resp_code"=>"",
                          "resp_msg"=>"",
                          "page"=>"",
                          "maxrow"=>"",
                          "locstr"=>"",
                          "tot_rec"=>""
                          );
    //body
    $arrBodyField = array(
                          "MCH_NO"=> $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'],                       //Y 商户编号
                          "CURR_COD"=> $paraBody["CURR_COD"],             //Y 币别  默认01-人民币
                          "TX_AMT"=> $paraBody["TX_AMT"],                 //Y 交易金额
                          "IN_AMT_SIT_NO"=> $paraBody["IN_AMT_SIT_NO"],   //Y 入金席位号
                          "RMRK"=> $paraBody["RMRK"]                      //备注
                          );
    //生成xml
    $xml = $this->GetXml($arrHeadField, $arrBodyField);
    $base64xml = base64_encode($xml);
    //=========== 生成xml end ===========

    //echo $xml;

    //对GUID+CusNo进行MD5
    $CusNo = $GLOBALS['danbaoSV'][$minfo['ComTransType']]['CusNo'];
    $Sign = strtoupper(md5($GUID.$CusNo));
    //插入日志主表
    $this->_dao->execute( "UPDATE ccb_log SET CusNo ='".$CusNo."', Sign='".$Sign."', SiteNo='".$paraBody["IN_AMT_SIT_NO"]
                        ."', SyncType='1', InterFaceType='1', OperDate=NOW(), CcbPostDate=NULL, CcbReturnDate=NULL, IP='".getfromip()."', Title='".base64_encode("会员入金[3FC002]")."', Status=0"
                        ." WHERE id =".$PriKey);
    //插入日志内容表
    $this->_dao->execute( "INSERT INTO ccb_log_detail SET ID='".$PriKey."', ContentTypePost='1',  ContentPost='".$base64xml."', FileName='', ReturnInfor=''");
    //【1】.提交http请求
    $arr=array('PriKey' => $PriKey, 'Sign' => $Sign, 'SyncType' => 1, 'CusNo' => base64_encode($CusNo));

    $client = new HttpClient($GLOBALS['danbaoSV'][$minfo['ComTransType']]['site']);
    $client->setDebug(false);
    $client->setMaxRedirects(20);
    if (!$client->post($GLOBALS['danbaoSV'][$minfo['ComTransType']]['url'], $arr)) {
      return array("resp_code" => "E000001", "resp_msg" => "网络连接错误", "PriKey" => $PriKey);
    }else{
      //判断http响应值
      if ($client->getContent() == self::$httpRetOk){
        //从ccb_log_detail表中读取返回的结果
        $logdetail = $this->_dao->getRow( "SELECT * FROM ccb_log_detail WHERE ID='".$PriKey."'", 0);
        if( empty( $logdetail ) ){
          return array("resp_code" => "E000002", "resp_msg" => "读取返回数据错误", "PriKey" => $PriKey);
        }
        //4.解析xml内容
        $content = base64_decode($logdetail['ContentReturn']);
        if ($content == ""){
          return array("resp_code" => "E000003", "resp_msg" => "解析返回数据错误", "PriKey" => $PriKey);
        }
        $arrSingle = $this->GetAllSingleValue($content);
        $arrList = $this->GetListData($content);
        //更新ccb_log_detail
        $this->_dao->execute( "UPDATE ccb_log_detail SET ReturnInfor ='".base64_encode($arrSingle["resp_msg"])."' WHERE ID =".$PriKey);
        if ($arrSingle["resp_code"] == "000000000000"){
          //会员资金账户记录
          //function FinanceLog($Mid, $SiteNo, $MoneyTitle, $Money, $MoneyType, $OrderID, $FinanceDesc){
          $this->FinanceLog($minfo['ID'], $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'], $paraBody["IN_AMT_SIT_NO"], "会员入金", $paraBody["TX_AMT"], "1", "", $paraBody["RMRK"], $minfo['ComTransType']);
        }
        return array("resp_code" => $arrSingle["resp_code"]
                   , "resp_msg" => $arrSingle["resp_msg"]
                   , "PriKey" => $PriKey
                   , "arrSingle" => $arrSingle
                   , "arrList" => $arrList);
      }else{
        return array("resp_code" => "E000004", "resp_msg" => "接口服务器返回错误", "PriKey" => $PriKey);
      }
    }

  }
  //6.会员出金
  //head无自定义参数，仅需设置body参数
  public function f_3FC022($paraBody){

    //根据席位号，反查会员信息
    $minfo = $this->_dao->getRow( "SELECT * FROM sm_user_ccb a inner join sys_company b ON a.MID=b.ID WHERE a.SPOT_SIT_NO ='".$paraBody["OUT_AMT_SIT_NO"]."' LIMIT 1", 0);
    if (empty($minfo)){
      return array("resp_code" => "E000005", "resp_msg" => "席位号不存在", "PriKey" => $PriKey);
    }

    //生成GUID
    $GUID = $this->_dao->getOne("SELECT REPLACE( UUID() ,'-','') AS GUID", 0);
    //产生主键ID
    $this->_dao->execute( "INSERT INTO ccb_log SET GUID='".$GUID."', ComTransType='".$minfo['ComTransType']."'");
    $PriKey = $this->_dao->insert_id();
    //商户系统流水号，唯一标识一笔交易 Char(12)
    $chanl_flow_no =  substr("000000000000".$PriKey, -12);

    //head
    $arrHeadField = array(
                          "version"=>"100",
                          "type"=>"0200",
                          "chanl_no"=>"30",
                          "chanl_sub_no"=>"3001",
                          "chanl_date"=> date("Ymd"),   //yyyymmdd
                          "chanl_time"=> date("His"),   //HHMMSS
                          "ectip_date"=>"",
                          "chanl_flow_no"=> $chanl_flow_no,
                          "ectip_flow_no"=>"",
                          "chanl_trad_no"=>"3FC022",
                          "term_inf"=> getfromip(),
                          "resp_code"=>"",
                          "resp_msg"=>"",
                          "page"=>"",
                          "maxrow"=>"",
                          "locstr"=>"",
                          "tot_rec"=>""
                          );
    //body
    $arrBodyField = array(
                          "MCH_NO"=> $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'],                         //Y 商户编号
                          "FLOW_NO"=> $paraBody["FLOW_NO"],                 //Y 出金申请流水号
                          "DRAWEE_ACCT_NO"=> $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_Super_Account'],   //Y 付款账号   (商户资金池结算账号) MCH_Super_Account  MCH_Common_Account
                          "PAYEE_ACCT_NO"=> $paraBody["PAYEE_ACCT_NO"],     //Y 收款帐号
                          "CURR_COD"=> $paraBody["CURR_COD"],               //Y 币别
                          "TX_AMT"=> $paraBody["TX_AMT"],                   //Y 交易金额
                          "OUT_AMT_SIT_NO"=> $paraBody["OUT_AMT_SIT_NO"],   //Y 出金席位号
                          "AUDIT_STS"=> $paraBody["AUDIT_STS"],             //Y 审批结果
                          "RMRK"=> $paraBody["RMRK"]                        //备注
                          );
    //生成xml
    $xml = $this->GetXml($arrHeadField, $arrBodyField);
    $base64xml = base64_encode($xml);
    //=========== 生成xml end ===========

    //echo $xml;

    //对GUID+CusNo进行MD5
    $CusNo = $GLOBALS['danbaoSV'][$minfo['ComTransType']]['CusNo'];
    $Sign = strtoupper(md5($GUID.$CusNo));
    //插入日志主表
    $this->_dao->execute( "UPDATE ccb_log SET CusNo ='".$CusNo."', Sign='".$Sign."', SiteNo='".$paraBody["OUT_AMT_SIT_NO"]
                        ."', SyncType='1', InterFaceType='1', OperDate=NOW(), CcbPostDate=NULL, CcbReturnDate=NULL, IP='".getfromip()."', Title='".base64_encode("会员出金[3FC022]")."', Status=0"
                        ." WHERE id =".$PriKey);
    //插入日志内容表
    $this->_dao->execute( "INSERT INTO ccb_log_detail SET ID='".$PriKey."', ContentTypePost='1',  ContentPost='".$base64xml."', FileName='', ReturnInfor=''");
    //【1】.提交http请求
    $arr=array('PriKey' => $PriKey, 'Sign' => $Sign, 'SyncType' => 1, 'CusNo' => base64_encode($CusNo));

    $client = new HttpClient($GLOBALS['danbaoSV'][$minfo['ComTransType']]['site']);
    $client->setDebug(false);
    $client->setMaxRedirects(20);
    if (!$client->post($GLOBALS['danbaoSV'][$minfo['ComTransType']]['url'], $arr)) {
      return array("resp_code" => "E000001", "resp_msg" => "网络连接错误", "PriKey" => $PriKey);
    }else{
      //判断http响应值
      if ($client->getContent() == self::$httpRetOk){
        //从ccb_log_detail表中读取返回的结果
        $logdetail = $this->_dao->getRow( "SELECT * FROM ccb_log_detail WHERE ID='".$PriKey."'", 0);
        if( empty( $logdetail ) ){
          return array("resp_code" => "E000002", "resp_msg" => "读取返回数据错误", "PriKey" => $PriKey);
        }
        //4.解析xml内容
        $content = base64_decode($logdetail['ContentReturn']);
        if ($content == ""){
          return array("resp_code" => "E000003", "resp_msg" => "解析返回数据错误", "PriKey" => $PriKey);
        }
        $arrSingle = $this->GetAllSingleValue($content);
        $arrList = $this->GetListData($content);
        //更新ccb_log_detail
        $this->_dao->execute( "UPDATE ccb_log_detail SET ReturnInfor ='".base64_encode($arrSingle["resp_msg"])."' WHERE ID =".$PriKey);
        if ($arrSingle["resp_code"] == "000000000000"){
          //会员资金账户记录
          //function FinanceLog($Mid, $SiteNo, $MoneyTitle, $Money, $MoneyType, $OrderID, $FinanceDesc){
          $this->FinanceLog($minfo['ID'], $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'], $paraBody["OUT_AMT_SIT_NO"], "会员出金", $paraBody["TX_AMT"], "2", "", $paraBody["RMRK"], $minfo['ComTransType']);
        }
        return array("resp_code" => $arrSingle["resp_code"]
                   , "resp_msg" => $arrSingle["resp_msg"]
                   , "PriKey" => $PriKey
                   , "arrSingle" => $arrSingle
                   , "arrList" => $arrList);
      }else{
        return array("resp_code" => "E000004", "resp_msg" => "接口服务器返回错误", "PriKey" => $PriKey);
      }
    }

  }
  //冻结解冻 bzjdjjd
  public function bzjdjjd($id){

    //return 8;

    $row = $this->_dao->getRow( "SELECT * FROM sm_user_financeguarantee WHERE OperStatus <> 9 AND ID='".$id."'", 0);
    if( empty( $row ) ){
      $retData["resp_code"] = "E000007";
      $retData["resp_msg"] = "不能重复操作冻结解冻记录";
      return $retData;
    }
    $ccbparams = array(
                          "FUNC_CODE"=> $row["MoneyFuncCode"],                     //Y 功能号 0-冻结；1-解冻
                          //"MCH_NO"=> self::$MCH_NO,                                 //Y 商户编号
                          "BUYER_SIT_NO"=> $row["SIT_NO_BUYER"],               //Y 买方席位号
                          "SELLER_SIT_NO"=> $row["SIT_NO_SELLER"],             //Y 卖方席位号
                          "CTRT_NO"=> $row["OrderNO"],                         //Y 合同编号
                          "CTRT_AMT"=> $row["OrderMoney"],                       //Y 合同金额
                          "BUYER_GUAR_PAY_AMT"=> $row["BUYER_GUAR_PAY_AMT"],   //N 买方保证付款金额
                          "SELLER_GUAR_PAY_AMT"=> $row["SELLER_GUAR_PAY_AMT"], //N 卖方保证付款金额
                          "BUYER_SVC_AMT"=> $row["BUYER_SVC_AMT"],             //N 买方手续费金额
                          "SELLER_SVC_AMT"=> $row["SELLER_SVC_AMT"],           //N 卖方手续费金额
                          "CURR_COD"=> $row["CURR_COD"],                       //Y 币别
                          "RMRK"=> $row["FinanceDesc"],                               //N 备注
                          "FIN_FLG"=> $row["FIN_FLG"],                         //N 融资标志
                          "FIN_AMT"=> $row["FIN_AMT"]                          //Y 融资金额
                          );
    $retData = $this->f_3FC009($ccbparams);
    if ($retData["resp_code"] == "000000000000"){
      $ret = 9;
    }else{
      $ret = 8;
    }
    //记录保证金交易接口操作日志表
    $data =  "PID = '".$id."'";
    $data .=  ",MoneyFuncCode = '".$row["MoneyFuncCode"]."'";
    $data .=  ",MoneyType = '".$row["MoneyType"]."'";
    $data .=  ",MCH_NO = '".$row["MCH_NO"]."'";
    $data .=  ",SIT_NO_BUYER = '".$row["SIT_NO_BUYER"]."'";
    $data .=  ",SIT_NO_SELLER = '".$row["SIT_NO_SELLER"]."'";
    $data .=  ",OrderNO = '".$row["OrderNO"]."'";
    $data .=  ",OrderMoney = '".$row["OrderMoney"]."'";
    $data .=  ",OrderID = '".$row["OrderID"]."'";
    $data .=  ",BUYER_GUAR_PAY_AMT = '".$row["BUYER_GUAR_PAY_AMT"]."'";
    $data .=  ",SELLER_GUAR_PAY_AMT = '".$row["SELLER_GUAR_PAY_AMT"]."'";
    $data .=  ",BUYER_SVC_AMT = '".$row["BUYER_SVC_AMT"]."'";
    $data .=  ",SELLER_SVC_AMT = '".$row["SELLER_SVC_AMT"]."'";
    $data .=  ",CURR_COD = '".$row["CURR_COD"]."'";
    $data .=  ",FinanceDesc = '".$row["FinanceDesc"]."'";
    $data .=  ",FIN_FLG = '".$row["FIN_FLG"]."'";
    $data .=  ",FIN_AMT = '".$row["FIN_AMT"]."'";
    $data .=  ",CreateDate = NOW()";
    $data .=  ",CreateUser = '".$_SESSION['SYS_TRUENAME']."'";
    $data .=  ",OperIP = '".getfromip()."'";
    $data .=  ",OperStatus = '".$ret."'";
    $this->_dao->execute("insert into sm_user_financeguarantee_log_face_res set $data");
    //修改保证金交易表状态
    $this->_dao->execute("update sm_user_financeguarantee set OperStatus = '".$ret."' WHERE ID='".$id."'" );
    //返回
    return $retData;
  }
  //7.冻结解冻
  public function f_3FC009($paraBody){

    //根据席位号，反查会员信息
    $minfo = $this->_dao->getRow( "SELECT * FROM sm_user_ccb a inner join sys_company b ON a.MID=b.ID WHERE a.SPOT_SIT_NO ='".$paraBody["BUYER_SIT_NO"]."' LIMIT 1", 0);
  	if ($paraBody["BUYER_SIT_NO"] == self::$MCH_SIT_NO1){
  		//对于商户的一般席位号，不需要判断是否存在
  	}else if (empty($minfo)){
      return array("resp_code" => "E000005", "resp_msg" => "席位号不存在".$paraBody["BUYER_SIT_NO"], "PriKey" => $PriKey);
    }
    //卖方信息
    $minfoSeller = $this->_dao->getRow( "SELECT * FROM sm_user_ccb a inner join sys_company b ON a.MID=b.ID WHERE a.SPOT_SIT_NO ='".$paraBody["SELLER_SIT_NO"]."' LIMIT 1", 0);
    if ($paraBody["SELLER_SIT_NO"] == self::$MCH_SIT_NO1){
  		//对于商户的一般席位号，不需要判断是否存在
  	}else if (empty($minfoSeller)){
      return array("resp_code" => "E000005", "resp_msg" => "席位号不存在".$paraBody["SELLER_SIT_NO"], "PriKey" => $PriKey);
    }
    
  	if ($paraBody["BUYER_SIT_NO"] == self::$MCH_SIT_NO1){
  		//买方为商户的一般席位号时，交易类型根据卖方的信息获取
  		$minfo['ComTransType'] = $minfoSeller['ComTransType'];	
  	}
  	if ($paraBody["SELLER_SIT_NO"] == self::$MCH_SIT_NO1){
  		//卖方为商户的一般席位号时，交易类型根据买方的信息获取
  		$minfoSeller['ComTransType'] = $minfo['ComTransType'];	
  	}    
    
    //生成GUID
    $GUID = $this->_dao->getOne("SELECT REPLACE( UUID() ,'-','') AS GUID", 0);
    //产生主键ID
    $this->_dao->execute( "INSERT INTO ccb_log SET GUID='".$GUID."', ComTransType='".$minfo['ComTransType']."'");
    $PriKey = $this->_dao->insert_id();
    //商户系统流水号，唯一标识一笔交易 Char(12)
    $chanl_flow_no =  substr("000000000000".$PriKey, -12);

    //head
    $arrHeadField = array(
                          "version"=>"100",
                          "type"=>"0200",
                          "chanl_no"=>"30",
                          "chanl_sub_no"=>"3001",
                          "chanl_date"=> date("Ymd"),   //yyyymmdd
                          "chanl_time"=> date("His"),   //HHMMSS
                          "ectip_date"=>"",
                          "chanl_flow_no"=> $chanl_flow_no,
                          "ectip_flow_no"=>"",
                          "chanl_trad_no"=>"3FC009",
                          "term_inf"=> getfromip(),
                          "resp_code"=>"",
                          "resp_msg"=>"",
                          "page"=>"",
                          "maxrow"=>"",
                          "locstr"=>"",
                          "tot_rec"=>""
                          );
    //body
    $arrBodyField = array(
                          "FUNC_CODE"=> $paraBody["FUNC_CODE"],                     //Y 功能号 0-冻结；1-解冻
                          "MCH_NO"=> $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'],                                 //Y 商户编号
                          "BUYER_SIT_NO"=> $paraBody["BUYER_SIT_NO"],               //Y 买方席位号
                          "SELLER_SIT_NO"=> $paraBody["SELLER_SIT_NO"],             //Y 卖方席位号
                          "CTRT_NO"=> $paraBody["CTRT_NO"],                         //Y 合同编号
                          "CTRT_AMT"=> $paraBody["CTRT_AMT"],                       //Y 合同金额
                          "BUYER_GUAR_PAY_AMT"=> $paraBody["BUYER_GUAR_PAY_AMT"],   //N 买方保证付款金额
                          "SELLER_GUAR_PAY_AMT"=> $paraBody["SELLER_GUAR_PAY_AMT"], //N 卖方保证付款金额
                          "BUYER_SVC_AMT"=> $paraBody["BUYER_SVC_AMT"],             //N 买方手续费金额
                          "SELLER_SVC_AMT"=> $paraBody["SELLER_SVC_AMT"],           //N 卖方手续费金额
                          "CURR_COD"=> $paraBody["CURR_COD"],                       //Y 币别
                          "RMRK"=> $paraBody["RMRK"],                               //N 备注
                          "FIN_FLG"=> $paraBody["FIN_FLG"],                         //N 融资标志
                          "FIN_AMT"=> $paraBody["FIN_AMT"]                          //Y 融资金额
                          );
    //生成xml
    // $xml = $this->GetXml($arrHeadField, $arrBodyField);
    // $base64xml = base64_encode($xml);
    $base64xml = "";
    //=========== 生成xml end ===========

    //echo $xml;

    //对GUID+CusNo进行MD5
    $CusNo = $GLOBALS['danbaoSV'][$minfo['ComTransType']]['CusNo'];
    $Sign = strtoupper(md5($GUID.$CusNo));
    //插入日志主表
    $this->_dao->execute( "UPDATE ccb_log SET CusNo ='".$CusNo."', Sign='".$Sign."', SiteNo='".$paraBody["BUYER_SIT_NO"].",".$paraBody["SELLER_SIT_NO"]
                        ."', SyncType='1', InterFaceType='1', OperDate=NOW(), CcbPostDate=NULL, CcbReturnDate=NULL, IP='".getfromip()."', Title='".base64_encode("冻结解冻[3FC009]")."', Status=0"
                        ." WHERE id =".$PriKey);
    //插入日志内容表
    $this->_dao->execute( "INSERT INTO ccb_log_detail SET ID='".$PriKey."', ContentTypePost='1',  ContentPost='".$base64xml."', FileName='', ReturnInfor=''");
    //【1】.提交http请求
    // $arr=array('PriKey' => $PriKey, 'Sign' => $Sign, 'SyncType' => 1, 'CusNo' => base64_encode($CusNo));

    // $client = new HttpClient($GLOBALS['danbaoSV'][$minfo['ComTransType']]['site']);
    // $client->setDebug(false);
    // $client->setMaxRedirects(20);
    // if (!$client->post($GLOBALS['danbaoSV'][$minfo['ComTransType']]['url'], $arr)) {
    //   return array("resp_code" => "E000001", "resp_msg" => "网络连接错误", "PriKey" => $PriKey);
    // }else{
    //   //判断http响应值
    //   if ($client->getContent() == self::$httpRetOk){
    //     //从ccb_log_detail表中读取返回的结果
    //     $logdetail = $this->_dao->getRow( "SELECT * FROM ccb_log_detail WHERE ID='".$PriKey."'", 0);
    //     if( empty( $logdetail ) ){
    //       return array("resp_code" => "E000002", "resp_msg" => "读取返回数据错误", "PriKey" => $PriKey);
    //     }
    //     //4.解析xml内容
    //     $content = base64_decode($logdetail['ContentReturn']);
    //     if ($content == ""){
    //       return array("resp_code" => "E000003", "resp_msg" => "解析返回数据错误", "PriKey" => $PriKey);
    //     }
    //     $arrSingle = $this->GetAllSingleValue($content);
    //     $arrList = $this->GetListData($content);
    //     //更新ccb_log_detail
    //     $this->_dao->execute( "UPDATE ccb_log_detail SET ReturnInfor ='".base64_encode($arrSingle["resp_msg"])."' WHERE ID =".$PriKey);
    //     if ($arrSingle["resp_code"] == "000000000000"){
    //       //会员资金账户记录
    //       //function FinanceLog($Mid, $SiteNo, $MoneyTitle, $Money, $MoneyType, $OrderID, $FinanceDesc){
    //       if ($paraBody["FUNC_CODE"] == "0"){
    //         //冻结
    //         if (doubleval($paraBody["BUYER_GUAR_PAY_AMT"]) + doubleval($paraBody["BUYER_SVC_AMT"]) != 0){
    //           //买方保证金冻结
    //           $this->FinanceLog($minfo['ID'], $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'], $paraBody["BUYER_SIT_NO"], "买方保证金冻结", doubleval($paraBody["BUYER_GUAR_PAY_AMT"]) + doubleval($paraBody["BUYER_SVC_AMT"]), "3", $paraBody["CTRT_NO"], $paraBody["RMRK"], $minfo['ComTransType']);
    //         }
    //         if (doubleval($paraBody["SELLER_GUAR_PAY_AMT"]) + doubleval($paraBody["SELLER_SVC_AMT"]) != 0){
    //           //卖方保证金冻结
    //           $this->FinanceLog($minfoSeller['ID'], $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'], $paraBody["SELLER_SIT_NO"], "卖方保证金冻结", doubleval($paraBody["SELLER_GUAR_PAY_AMT"]) + doubleval($paraBody["SELLER_SVC_AMT"]), "3", $paraBody["CTRT_NO"], $paraBody["RMRK"], $minfoSeller['ComTransType']);
    //         }
    //       }else if ($paraBody["FUNC_CODE"] == "1"){
    //         //解冻
    //         if (doubleval($paraBody["BUYER_GUAR_PAY_AMT"]) + doubleval($paraBody["BUYER_SVC_AMT"]) != 0){
    //           //买方保证金解冻
    //           $this->FinanceLog($minfo['ID'], $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'], $paraBody["BUYER_SIT_NO"], "买方保证金解冻", doubleval($paraBody["BUYER_GUAR_PAY_AMT"]) + doubleval($paraBody["BUYER_SVC_AMT"]), "4", $paraBody["CTRT_NO"], $paraBody["RMRK"], $minfo['ComTransType']);
    //         }
    //         if (doubleval($paraBody["SELLER_GUAR_PAY_AMT"]) + doubleval($paraBody["SELLER_SVC_AMT"]) != 0){
    //           //卖方保证金解冻
    //           $this->FinanceLog($minfoSeller['ID'], $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'], $paraBody["SELLER_SIT_NO"], "卖方保证金解冻", doubleval($paraBody["SELLER_GUAR_PAY_AMT"]) + doubleval($paraBody["SELLER_SVC_AMT"]), "4", $paraBody["CTRT_NO"], $paraBody["RMRK"], $minfoSeller['ComTransType']);
    //         }
    //         //扣除交易服务费
    //         if (doubleval($paraBody["BUYER_SVC_AMT"]) != 0){
    //           //买方交易服务费出账
    //           $this->FinanceLog($minfo['ID'], $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'], $paraBody["BUYER_SIT_NO"], "买方交易服务费出账", doubleval($paraBody["BUYER_SVC_AMT"]), "8", $paraBody["CTRT_NO"], $paraBody["RMRK"], $minfo['ComTransType']);
    //           //商户交易服务费入账
    //           $this->FinanceLog('0', $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'], self::$MCH_SIT_NO1, "买方交易服务费入账", doubleval($paraBody["BUYER_SVC_AMT"]), "7", $paraBody["CTRT_NO"], "席位号：".$paraBody["BUYER_SIT_NO"], $minfo['ComTransType']);
    //         }
    //         if (doubleval($paraBody["SELLER_SVC_AMT"]) != 0){
    //           //卖方交易服务费出账
    //           $this->FinanceLog($minfoSeller['ID'], $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'], $paraBody["SELLER_SIT_NO"], "卖方交易服务费出账", doubleval($paraBody["SELLER_SVC_AMT"]), "8", $paraBody["CTRT_NO"], $paraBody["RMRK"], $minfoSeller['ComTransType']);
    //           //商户交易服务费入账
    //           $this->FinanceLog('0', $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'], self::$MCH_SIT_NO1, "卖方交易服务费入账", doubleval($paraBody["SELLER_SVC_AMT"]), "7", $paraBody["CTRT_NO"], "席位号：".$paraBody["SELLER_SIT_NO"], $minfoSeller['ComTransType']);
    //         }
    //       }
    //     }
    //     return array("resp_code" => $arrSingle["resp_code"]
    //                , "resp_msg" => $arrSingle["resp_msg"]
    //                , "PriKey" => $PriKey
    //                , "arrSingle" => $arrSingle
    //                , "arrList" => $arrList);
    //   }else{
    //     return array("resp_code" => "E000004", "resp_msg" => "接口服务器返回错误", "PriKey" => $PriKey);
    //   }
    // }

  }
  //8.会员席位资金冻结解冻明细查询
  //head定义明细的“查询第几页、每页返回的最大记录笔数”等信息
  public function f_3FC031($paraHead, $paraBody){

    if ($paraBody["SIT_NO"] != ""){
      //根据席位号，反查会员信息
      $minfo = $this->_dao->getRow( "SELECT * FROM sm_user_ccb a inner join sys_company b ON a.MID=b.ID WHERE a.SPOT_SIT_NO ='".$paraBody["SIT_NO"]."' LIMIT 1", 0);
    }else{
      //商户查询时，查大宗资金监管平台
      //$minfo['ComTransType'] = 3;
      $minfo['ComTransType'] = $paraBody["ComTransType"] == "" ? 3 : $paraBody["ComTransType"];
    }
    if (empty($minfo)){
      return array("resp_code" => "E000005", "resp_msg" => "席位号不存在", "PriKey" => $PriKey);
    }

    //生成GUID
    $GUID = $this->_dao->getOne("SELECT REPLACE( UUID() ,'-','') AS GUID", 0);
    //产生主键ID
    $this->_dao->execute( "INSERT INTO ccb_log SET GUID='".$GUID."', ComTransType='".$minfo['ComTransType']."'");
    $PriKey = $this->_dao->insert_id();
    //商户系统流水号，唯一标识一笔交易 Char(12)
    $chanl_flow_no =  substr("000000000000".$PriKey, -12);

    //head
    $arrHeadField = array(
                          "version"=>"100",
                          "type"=>"0200",
                          "chanl_no"=>"30",
                          "chanl_sub_no"=>"3001",
                          "chanl_date"=> date("Ymd"),   //yyyymmdd
                          "chanl_time"=> date("His"),   //HHMMSS
                          "ectip_date"=>"",
                          "chanl_flow_no"=> $chanl_flow_no,
                          "ectip_flow_no"=>"",
                          "chanl_trad_no"=>"3FC031",
                          "term_inf"=> getfromip(),
                          "resp_code"=>"",
                          "resp_msg"=>"",
                          "page"=> $paraHead["page"],         //查询页号
                          "maxrow"=> $paraHead["maxrow"],     //每页最大返回笔数
                          "locstr"=> $paraHead["locstr"],     //定位串
                          "tot_rec"=>""
                          );
    //body
    $arrBodyField = array(
                          "MCH_NO"=> $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'],                     //商户编号
                          // ******************
                          "MCH_NAME"=> "钢之家1",                     //商户名称（v1.0.4文档未要求此字段，但是系统会提示缺少该字段）  钢之家1
                          // ******************
                          "SIT_NO"=> $paraBody["SIT_NO"],               //席位号  不输入可查询商户下所有席位
                          "FUND_TYP"=> $paraBody["FUND_TYP"],           //资金类型 不输入可查询席位下所有资金类型的冻结明细，1-交易保证金 2-缴费保证金 3-无负债备抵
                          "STRT_DT"=> $paraBody["STRT_DT"],             //Y 起始日期
                          "END_DT"=> $paraBody["END_DT"]               //Y 终止日期
                          );
    //生成xml
    $xml = $this->GetXml($arrHeadField, $arrBodyField);
    $base64xml = base64_encode($xml);
    //=========== 生成xml end ===========

    //echo $xml;

    //对GUID+CusNo进行MD5
    $CusNo = $GLOBALS['danbaoSV'][$minfo['ComTransType']]['CusNo'];
    $Sign = strtoupper(md5($GUID.$CusNo));
    //插入日志主表
    $this->_dao->execute( "UPDATE ccb_log SET CusNo ='".$CusNo."', Sign='".$Sign."', SiteNo='".$paraBody["SIT_NO"]
                        ."', SyncType='1', InterFaceType='1', OperDate=NOW(), CcbPostDate=NULL, CcbReturnDate=NULL, IP='".getfromip()."', Title='".base64_encode("会员席位资金冻结解冻明细查询[3FC031]")."', Status=0"
                        ." WHERE id =".$PriKey);
    //插入日志内容表
    $this->_dao->execute( "INSERT INTO ccb_log_detail SET ID='".$PriKey."', ContentTypePost='1',  ContentPost='".$base64xml."', FileName='', ReturnInfor=''");
    //【1】.提交http请求
    $arr=array('PriKey' => $PriKey, 'Sign' => $Sign, 'SyncType' => 1, 'CusNo' => base64_encode($CusNo));

    $client = new HttpClient($GLOBALS['danbaoSV'][$minfo['ComTransType']]['site']);
    $client->setDebug(false);
    $client->setMaxRedirects(20);
    if (!$client->post($GLOBALS['danbaoSV'][$minfo['ComTransType']]['url'], $arr)) {
      return array("resp_code" => "E000001", "resp_msg" => "网络连接错误", "PriKey" => $PriKey);
    }else{
      //判断http响应值
      if ($client->getContent() == self::$httpRetOk){
        //从ccb_log_detail表中读取返回的结果
        $logdetail = $this->_dao->getRow( "SELECT * FROM ccb_log_detail WHERE ID='".$PriKey."'", 0);
        if( empty( $logdetail ) ){
          return array("resp_code" => "E000002", "resp_msg" => "读取返回数据错误", "PriKey" => $PriKey);
        }
        //4.解析xml内容
        $content = base64_decode($logdetail['ContentReturn']);
        if ($content == ""){
          return array("resp_code" => "E000003", "resp_msg" => "解析返回数据错误", "PriKey" => $PriKey);
        }
        $arrSingle = $this->GetAllSingleValue($content);
        $arrList = $this->GetListData($content);
        //更新ccb_log_detail
        $this->_dao->execute( "UPDATE ccb_log_detail SET ReturnInfor ='".base64_encode($arrSingle["resp_msg"])."' WHERE ID =".$PriKey);
        return array("resp_code" => $arrSingle["resp_code"]
                   , "resp_msg" => $arrSingle["resp_msg"]
                   , "PriKey" => $PriKey
                   , "arrSingle" => $arrSingle
                   , "arrList" => $arrList);
      }else{
        return array("resp_code" => "E000004", "resp_msg" => "接口服务器返回错误", "PriKey" => $PriKey);
      }
    }

  }
  //自定义接口 start
  //1.资金支付（例如：财务管理系统-->大宗资金监管）
  //head无自定义参数，仅需设置body参数
  public function f_DZ0001($paraBody){

  }

  //2.财务到账（例如：财务管理系统-->大宗资金监管） 财务到账接口仅供大宗资金监管会员使用
  //head无自定义参数，仅需设置body参数
  public function f_DZ0002($paraBody){

    //根据席位号，反查会员信息
    $minfo = $this->_dao->getRow( "SELECT * FROM sm_user_ccb a inner join sys_company b ON a.MID=b.ID WHERE a.SPOT_SIT_NO ='".$paraBody["SIT_NO"]."' LIMIT 1", 0);
    if (empty($minfo)){
      return array("resp_code" => "E000005", "resp_msg" => "席位号不存在", "PriKey" => $PriKey);
    }

    //大宗资金监管平台专用接口
    if ($minfo['ComTransType'] != '3'){
      return array("resp_code" => "E000006", "resp_msg" => "财务到账接口仅供大宗资金监管会员使用", "PriKey" => $PriKey);
    }

    //生成GUID
    $GUID = $this->_dao->getOne("SELECT REPLACE( UUID() ,'-','') AS GUID", 0);
    //产生主键ID
    $this->_dao->execute( "INSERT INTO ccb_log SET GUID='".$GUID."', ComTransType='".$minfo['ComTransType']."'");
    $PriKey = $this->_dao->insert_id();
    //商户系统流水号，唯一标识一笔交易 Char(12)
    $chanl_flow_no =  substr("000000000000".$PriKey, -12);

    //head
    $arrHeadField = array(
                          "version"=>"100",
                          "type"=>"0200",
                          "chanl_no"=>"30",
                          "chanl_sub_no"=>"3001",
                          "chanl_date"=> date("Ymd"),   //yyyymmdd
                          "chanl_time"=> date("His"),   //HHMMSS
                          "ectip_date"=>"",
                          "chanl_flow_no"=> $chanl_flow_no,
                          "ectip_flow_no"=>"",
                          "chanl_trad_no"=>"DZ0002",
                          "term_inf"=> getfromip(),
                          "resp_code"=>"",
                          "resp_msg"=>"",
                          "page"=>"",
                          "maxrow"=>"",
                          "locstr"=>"",
                          "tot_rec"=>""
                          );
    //body
    $arrBodyField = array(
                          "MCH_NO"=> $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'],  //Y 商户编号
                          "SIT_NO"=> $paraBody["SIT_NO"],                 //席位号
                          "TX_AMT"=> $paraBody["TX_AMT"],                 //到账金额
                          "RMRK"=> $paraBody["RMRK"]                      //备注
                          );
    //生成xml
    $xml = $this->GetXml($arrHeadField, $arrBodyField);
    $base64xml = base64_encode($xml);
    //=========== 生成xml end ===========

    //echo $xml;

    //对GUID+CusNo进行MD5
    $CusNo = $GLOBALS['danbaoSV'][$minfo['ComTransType']]['CusNo'];
    $Sign = strtoupper(md5($GUID.$CusNo));
    //插入日志主表
    $this->_dao->execute( "UPDATE ccb_log SET CusNo ='".$CusNo."', Sign='".$Sign."', SiteNo='".$paraBody["SIT_NO"]
                        ."', SyncType='1', InterFaceType='1', OperDate=NOW(), CcbPostDate=NULL, CcbReturnDate=NULL, IP='".getfromip()."', Title='".base64_encode("财务到账[DZ0002]")."', Status=0"
                        ." WHERE id =".$PriKey);
    //插入日志内容表
    $this->_dao->execute( "INSERT INTO ccb_log_detail SET ID='".$PriKey."', ContentTypePost='1',  ContentPost='".$base64xml."', FileName='', ReturnInfor=''");
    //【1】.提交http请求
    $arr=array('PriKey' => $PriKey, 'Sign' => $Sign, 'SyncType' => 1, 'CusNo' => base64_encode($CusNo));

    $client = new HttpClient($GLOBALS['danbaoSV'][$minfo['ComTransType']]['site']);
    $client->setDebug(false);
    $client->setMaxRedirects(20);
    if (!$client->post($GLOBALS['danbaoSV'][$minfo['ComTransType']]['url'], $arr)) {
      return array("resp_code" => "E000001", "resp_msg" => "网络连接错误", "PriKey" => $PriKey);
    }else{
      //判断http响应值
      if ($client->getContent() == self::$httpRetOk){
        //从ccb_log_detail表中读取返回的结果
        $logdetail = $this->_dao->getRow( "SELECT * FROM ccb_log_detail WHERE ID='".$PriKey."'", 0);
        if( empty( $logdetail ) ){
          return array("resp_code" => "E000002", "resp_msg" => "读取返回数据错误", "PriKey" => $PriKey);
        }
        //4.解析xml内容
        $content = base64_decode($logdetail['ContentReturn']);
        if ($content == ""){
          return array("resp_code" => "E000003", "resp_msg" => "解析返回数据错误", "PriKey" => $PriKey);
        }
        $arrSingle = $this->GetAllSingleValue($content);
        $arrList = $this->GetListData($content);
        //更新ccb_log_detail
        $this->_dao->execute( "UPDATE ccb_log_detail SET ReturnInfor ='".base64_encode($arrSingle["resp_msg"])."' WHERE ID =".$PriKey);
        return array("resp_code" => $arrSingle["resp_code"]
                   , "resp_msg" => $arrSingle["resp_msg"]
                   , "PriKey" => $PriKey
                   , "arrSingle" => $arrSingle
                   , "arrList" => $arrList);
      }else{
        return array("resp_code" => "E000004", "resp_msg" => "接口服务器返回错误", "PriKey" => $PriKey);
      }
    }

  }


  //自定义接口 end


  //银行---->商户接口 start
  public function ccbSrv($params) {

    //print_R($this->_dao);
    //return;
    //echo getfromip()."__".date("Y-m-d H:i:s");
    //$arr=array('PriKey' => $PriKey, 'Sign' => $Sign, 'SyncType' => 1, 'CusNo' => base64_encode(self::$CusNo));

    //获取请求的参数
    $PriKey = $_POST["PriKey"];
    $Sign = $_POST["Sign"];
    $SyncType = $_POST["SyncType"];
    $CusNo = base64_decode($_POST["CusNo"]);

//    $PriKey = $_GET["PriKey"];
//    $Sign = $_GET["Sign"];
//    $SyncType = $_GET["SyncType"];
//    $CusNo = base64_decode($_GET["CusNo"]);

    //读取请求记录
    $ccb_log = $this->_dao->getRow( "SELECT * FROM ccb_log WHERE ID='".$PriKey."'", 0);
    if(empty($ccb_log)){
      echo self::$httpRetNg;
      return;
    }

    $GUID = $ccb_log["GUID"];
    //校验请求的参数   Sign = 对GUID+CusNo进行MD5
    if ($Sign != strtoupper(md5($GUID.$CusNo))){
      echo self::$httpRetNg;
      return;
    }

    //读取请求记录
    $ccb_log_detail = $this->_dao->getRow( "SELECT * FROM ccb_log_detail WHERE ID='".$PriKey."'", 0);
    if(empty($ccb_log_detail)){
      echo self::$httpRetNg;
      return;
    }
    //解析请求的内容
    $ContentPost = base64_decode($ccb_log_detail["ContentPost"]);
    //echo ($ContentPost );
    //echo XML::GetFieldValue($ContentPost, "chanl_trad_no");
    $arrSingle = $this->GetAllSingleValue($ContentPost);

    //读取请求内容，根据参数调用对应的函数。
    if ($arrSingle["chanl_trad_no"] == "3FC012"){     //会员交易流水查询
      $this->f_3FC012($PriKey, $arrSingle);
    }else if ($arrSingle["chanl_trad_no"] == "3FC019"){     //合同状态变更通知
      $this->f_3FC019($PriKey, $arrSingle);
    }else{
      echo self::$httpRetNg;
      return;
    }
    //http返回值
    echo self::$httpRetOk;

    return;

  }

  //sv:1.会员交易流水查询
  private function f_3FC012($PriKey, $arrSingle){
  //public function f_3FC012($PriKey, $arrSingle){

    //初始化
    $resp_code = "000000000000";
    $resp_msg = "SUCCESS";
    //商户系统流水号，唯一标识一笔交易 Char(12)
    $chanl_flow_no =  substr("000000000000".$PriKey, -12);
    //head
    $page = $arrSingle['page'] == '' ? 1 : $arrSingle['page'];        //查询页号
    $per = $arrSingle['maxrow'] == '' ? 9999999 : $arrSingle['maxrow'];  //每页最大返回笔数
    $locstr = $arrSingle['locstr'];       //定位串  多页查询使用，返回报文有定位串表示还有后续数据，下次请求报文必须将上次报文返回的定位串
    //body
    $MCH_NO = $arrSingle['MCH_NO'];       //商户编号
    $SIT_NO = $arrSingle['SIT_NO'];              //席位号
    $PAY_STS = $arrSingle['PAY_STS'];     //付款状态  0-待确认付款，1-已主动付款(只包括会员付款)，2-终止交易，3-通知强制付款，4-已强制付款，5-所有状态
    $CTRT_NO = $arrSingle['CTRT_NO'];             //合同编号 输入合同号表示只查一笔合同信息。不输合同号表示查询全部。
    $BEGIN_DATE = $arrSingle['BEGIN_DATE'];             //开始日期 YYYYmmdd
    $END_DATE = $arrSingle['END_DATE'];             //结束日期 YYYYmmdd

    if (!($MCH_NO == '' || $SIT_NO == '' || $PAY_STS == '')){
      $where = '';
      $where .= " AND (MCH_NO='".$MCH_NO."')";    //表中需要增加MCH_NO字段？
      $where .= " AND (BUYER_SIT_NO='".$SIT_NO."')";
      if( $PAY_STS != "5" ){
        $where .= " AND (PAY_STS='".$PAY_STS."')";
      }
      if( $CTRT_NO != "" ){
        $where .= " AND (CTRT_NO='".$CTRT_NO."')";
      }
      if( $BEGIN_DATE != "" ){
        $BEGIN_DATE = substr($BEGIN_DATE,0,4).'-'.substr($BEGIN_DATE,4,2).'-'.substr($BEGIN_DATE,6,2);
        $where .= " AND (CTRT_TIME >= '".$BEGIN_DATE." 00:00:00')";
      }
      if( $END_DATE != "" ){
        $END_DATE = substr($END_DATE,0,4).'-'.substr($END_DATE,4,2).'-'.substr($END_DATE,6,2);
        $where .= " AND (CTRT_TIME <= '".$END_DATE." 23:59:59')";
      }
      //会员查询
      $total = $this->_dao->getOne("SELECT Count(ID) FROM sm_user_transationflow WHERE 1 ".$where, 0);
      //按页查询
      $start = ($page - 1) * $per;
      $sm_user_transationflows = $this->_dao->query( "SELECT * FROM sm_user_transationflow WHERE 1 ".$where." ORDER BY ID Desc LIMIT $start, $per", 0);
      //$dz_user_ccb = $this->_dao->getRow( "SELECT * FROM dz_user_ccb WHERE MCH_NO='".$MCH_NO."' AND SPOT_SIT_NO='".$SIT_NO."'", 0);
      $i = 0;
      foreach( $sm_user_transationflows as $sm_user_transationflow ){
        //FieldList
        $arrCTRT_TIME = explode(" ", $sm_user_transationflow['CTRT_TIME']);
        $arrFieldListBranch[$i++] = array(
                                        "MCH_NAME" => $sm_user_transationflow['MCH_NAME'],   //商户名称
                                        "BUYER_SIT_NO" => $sm_user_transationflow['BUYER_SIT_NO'],   //买方席位号
                                        "SELLER_SIT_NO" => $sm_user_transationflow['SELLER_SIT_NO'],   //卖方席位号
                                        "BUYER_NAME" => $sm_user_transationflow['BUYER_NAME'],   //买方会员名称
                                        "SELLER_NAME" => $sm_user_transationflow['SELLER_NAME'],   //卖方会员名称
                                        "CTRT_NO" => $sm_user_transationflow['CTRT_NO'],   //合同编号
                                        "PAY_PRD_NO" => $sm_user_transationflow['PAY_PRD_NO'],   //付款期次
                                        "CTRT_AMT" => $sm_user_transationflow['CTRT_AMT'],   //交易金额
                                        "BUYER_SVC_AMT" => $sm_user_transationflow['BUYER_SVC_AMT'],   //买方手续费金额
                                        "SELLER_SVC_AMT" => $sm_user_transationflow['SELLER_SVC_AMT'],   //卖方手续费金额
                                        "BUYER_GUAR_PAY_AMT" => $sm_user_transationflow['BUYER_GUAR_PAY_AMT'],   //买方保证付款金额
                                        "SELLER_GUAR_PAY_AMT" => $sm_user_transationflow['SELLER_GUAR_PAY_AMT'],   //卖方保证付款金额
                                        "CTRT_TIME" => str_replace('-','',$arrCTRT_TIME[0]).str_replace(':','',$arrCTRT_TIME[1]),       //合同时间
                                        "CTRT_REMARK" => $sm_user_transationflow['CTRT_REMARK'],   //交易用途
                                        "PAY_STS" => $sm_user_transationflow['PAY_STS'],   //付款状态
                                        "FIN_FLG" => $sm_user_transationflow['FIN_FLG'],   //融资标志
                                        "BUYER_PAY_UNFRZ_AMT" => $sm_user_transationflow['BUYER_PAY_UNFRZ_AMT'],   //买方付款解冻金额
                                        "SELLER_PAY_UNFRZ_AMT" => $sm_user_transationflow['SELLER_PAY_UNFRZ_AMT'],   //卖方付款解冻金额
                                        "BUYER_SVC_UNFRZ_AMT" => $sm_user_transationflow['BUYER_SVC_UNFRZ_AMT'],   //买方手续费解冻金额
                                        "SELLER_SVC_UNFRZ_AMT" => $sm_user_transationflow['SELLER_SVC_UNFRZ_AMT'],   //卖方手续费解冻金额
                                        );
      }
    }else{
      $resp_code = "000000000004";
      $resp_msg = "查询参数有误";
    }

    //返回执行结果
    //head
    $arrHeadField = array(
                          "version"=>"100",
                          "type"=>"0210",
                          "chanl_no"=>"30",
                          "chanl_sub_no"=>"3001",
                          "chanl_date"=> date("Ymd"),   //yyyymmdd
                          "chanl_time"=> date("His"),   //HHMMSS
                          "ectip_date"=> "",
                          "chanl_flow_no"=> $chanl_flow_no,
                          "ectip_flow_no"=> "",
                          "chanl_trad_no"=> "3FC012",
                          "term_inf"=> $arrSingle['term_inf'],
                          "resp_code"=> $resp_code,
                          "resp_msg"=> $resp_msg,
                          "page"=> $arrSingle['page'],
                          "maxrow"=> $arrSingle['maxrow'],
                          "locstr"=> $arrSingle['locstr'],
                          "tot_rec"=> $total    //总记录数: 多页查询时使用，应答方填写，表示本次查询结果共有多少条记录
                          );
    //根据商户编号，检索商户表
    //$shanghu_biao  = $this->_dao->getRow( "SELECT * FROM 商户表 WHERE MCH_NO='".$MCH_NO."'", 0);
    $arrBodyField = array(
//                          "MCH_NO"=> $MCH_NO,                     //商户编号
//                          "MCH_NAME"=> "中国大宗物资网"                     //商户名称
                          );

    //生成xml
    $xml = $this->GetXml($arrHeadField, $arrBodyField, $arrFieldListBranch, "ARRAY_"."3FC012");
    $base64xml = base64_encode($xml);
    //更新日志内容表
    $this->_dao->execute( "UPDATE ccb_log_detail SET ContentTypeReturn='1', ContentReturn='".$base64xml."', ReturnInfor='".base64_encode($resp_msg)."' WHERE ID ='".$PriKey."'");
    $this->_dao->execute( "UPDATE ccb_log SET Status = 9, CcbPostDate=NOW(),  CcbReturnDate=NOW() WHERE ID ='".$PriKey."'");

    return $xml;

  }

  //sv:2.合同状态变更通知
  private function f_3FC019($PriKey, $arrSingle){
  //public function f_3FC019($PriKey, $arrSingle){
    //初始化
    $resp_code = "000000000000";
    $resp_msg = "SUCCESS";
    //商户系统流水号，唯一标识一笔交易 Char(12)
    $chanl_flow_no =  substr("000000000000".$PriKey, -12);
    //body
    $MCH_NO = $arrSingle['MCH_NO'];       //商户编号
    $BUYER_SIT_NO = $arrSingle['BUYER_SIT_NO'];              //买方席位号
    $SELLER_SIT_NO = $arrSingle['SELLER_SIT_NO'];              //卖方席位号
    $CTRT_NO = $arrSingle['CTRT_NO'];             //合同编号
    $PAY_PRD_NO = $arrSingle['PAY_PRD_NO'];              //付款期次 默认值=1
    $TX_AMT = $arrSingle['TX_AMT'];              //合同金额 如交易付款时输入相应合同金额，如果单收手续费，则该栏位输0
    $BUYER_SVC_AMT = $arrSingle['BUYER_SVC_AMT'];              //买方手续费金额
    $SELLER_SVC_AMT = $arrSingle['SELLER_SVC_AMT'];              //卖方手续费金额
    $PAY_STS = $arrSingle['PAY_STS'];     //合同状态  0-待确认付款，1-已主动付款，2-终止交易 3-通知强制付款，4-已强制付款

    if (!($MCH_NO == '' || $BUYER_SIT_NO == '' || $SELLER_SIT_NO == '' || $CTRT_NO == '' || $PAY_PRD_NO == '' || $PAY_STS == '')){
      //判断记录是否存在
      $sm_user_transationflow = $this->_dao->getRow( "SELECT * FROM sm_user_transationflow"
                        ."  WHERE MCH_NO ='".$MCH_NO."' AND BUYER_SIT_NO ='".$BUYER_SIT_NO
                        ."' AND SELLER_SIT_NO ='".$SELLER_SIT_NO."' AND CTRT_NO ='".$CTRT_NO."' AND PAY_PRD_NO ='".$PAY_PRD_NO."'", 0);
      if(empty($sm_user_transationflow)){
        $resp_code = "000000000005";
        $resp_msg = "要更新的记录不存在";
      }else{
        //更新付款状态
       $this->_dao->execute( "UPDATE sm_user_transationflow SET PAY_STS ='".$PAY_STS
                          ."' WHERE MCH_NO ='".$MCH_NO."' AND BUYER_SIT_NO ='".$BUYER_SIT_NO
                          ."' AND SELLER_SIT_NO ='".$SELLER_SIT_NO."' AND CTRT_NO ='".$CTRT_NO."' AND PAY_PRD_NO ='".$PAY_PRD_NO."'");
      }
    }else{
      $resp_code = "000000000004";
      $resp_msg = "请求参数有误";
    }

    //返回执行结果
    //head
    $arrHeadField = array(
                          "version"=>"100",
                          "type"=>"0210",
                          "chanl_no"=>"30",
                          "chanl_sub_no"=>"3001",
                          "chanl_date"=> date("Ymd"),   //yyyymmdd
                          "chanl_time"=> date("His"),   //HHMMSS
                          "ectip_date"=> "",
                          "chanl_flow_no"=> $chanl_flow_no,
                          "ectip_flow_no"=> "",
                          "chanl_trad_no"=> "3FC019",
                          "term_inf"=> $arrSingle['term_inf'],
                          "resp_code"=> $resp_code,
                          "resp_msg"=> $resp_msg,
                          "page"=> "",
                          "maxrow"=> "",
                          "locstr"=> "",
                          "tot_rec"=> ""    //总记录数: 多页查询时使用，应答方填写，表示本次查询结果共有多少条记录
                          );
    //根据商户编号，检索商户表
    //$shanghu_biao  = $this->_dao->getRow( "SELECT * FROM 商户表 WHERE MCH_NO='".$MCH_NO."'", 0);
    $arrBodyField = array(
//                          "MCH_NO"=> $MCH_NO,                     //商户编号
//                          "MCH_NAME"=> "中国大宗物资网"                     //商户名称
                          );

    //生成xml
    $xml = $this->GetXml($arrHeadField, $arrBodyField);
    $base64xml = base64_encode($xml);
    //更新日志内容表
    $this->_dao->execute( "UPDATE ccb_log_detail SET ContentTypeReturn='1', ContentReturn='".$base64xml."', ReturnInfor='".base64_encode($resp_msg)."' WHERE ID ='".$PriKey."'");
    $this->_dao->execute( "UPDATE ccb_log SET Status = 9, CcbPostDate=NOW(),  CcbReturnDate=NOW() WHERE ID ='".$PriKey."'");

    return $xml;
  }
  //银行---->商户接口 end



/*
*
*	 add  by nicc start 2013/9/3
*
*/

  //非交易费用扣收

  //head无自定义参数，仅需设置body参数
  public function f_3FC029($paraBody){

    //根据席位号，反查会员信息
    $minfo = $this->_dao->getRow( "SELECT * FROM sm_user_ccb a inner join sys_company b ON a.MID=b.ID WHERE a.SPOT_SIT_NO ='".$paraBody["SIT_NO"]."' LIMIT 1", 0);
    if (empty($minfo)){
      return array("resp_code" => "E000005", "resp_msg" => "席位号不存在", "PriKey" => $PriKey);
    }

    //生成GUID
    $GUID = $this->_dao->getOne("SELECT REPLACE( UUID() ,'-','') AS GUID", 0);
    //产生主键ID
    $this->_dao->execute( "INSERT INTO ccb_log SET GUID='".$GUID."', ComTransType='".$minfo['ComTransType']."'");
    $PriKey = $this->_dao->insert_id();
    //商户系统流水号，唯一标识一笔交易 Char(12)
    $chanl_flow_no =  substr("000000000000".$PriKey, -12);

    //head
    $arrHeadField = array(
                          "version"=>"100",
                          "type"=>"0200",
                          "chanl_no"=>"30",
                          "chanl_sub_no"=>"3001",
                          "chanl_date"=> date("Ymd"),   //yyyymmdd
                          "chanl_time"=> date("His"),   //HHMMSS
                          "ectip_date"=>"",
                          "chanl_flow_no"=> $chanl_flow_no,
                          "ectip_flow_no"=>"",
                          "chanl_trad_no"=>"3FC029",
                          "term_inf"=> getfromip(),
                          "resp_code"=>"",
                          "resp_msg"=>"",
                          "page"=>"",
                          "maxrow"=>"",
                          "locstr"=>"",
                          "tot_rec"=>""
                          );
    //body
    $arrBodyField = array(
						  "FUNC_CODE"=>$paraBody["FUNC_CODE"],				//功能号  1 收费 2 退费
                          "MCH_NO"=> $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'],                         //Y 商户编号
                          "SIT_NO"=> $paraBody["SIT_NO"],                 //Y 出金申请流水号
                          "TX_AMT"=> $paraBody["TX_AMT"],                   //Y 交易金额
                          "RMRK"=> $paraBody["RMRK"]                        //备注
                          );
    //生成xml
    $xml = $this->GetXml($arrHeadField, $arrBodyField);
    $base64xml = base64_encode($xml);
    //=========== 生成xml end ===========
 //return array("resp_code" => "E000004", "resp_msg" => $xml, "PriKey" => $PriKey);

    //对GUID+CusNo进行MD5
    $CusNo = $GLOBALS['danbaoSV'][$minfo['ComTransType']]['CusNo'];
    $Sign = strtoupper(md5($GUID.$CusNo));
    //插入日志主表
    $this->_dao->execute( "UPDATE ccb_log SET CusNo ='".$CusNo."', Sign='".$Sign."', SiteNo='".$paraBody["SIT_NO"]
                        ."', SyncType='1', InterFaceType='1', OperDate=NOW(), CcbPostDate=NULL, CcbReturnDate=NULL, IP='".getfromip()."', Title='".base64_encode("扣收费用[3FC029]")."', Status=0"
                        ." WHERE id =".$PriKey);
    //插入日志内容表
    $this->_dao->execute( "INSERT INTO ccb_log_detail SET ID='".$PriKey."', ContentTypePost='1',  ContentPost='".$base64xml."', FileName='', ReturnInfor=''");
    //【1】.提交http请求
    $arr=array('PriKey' => $PriKey, 'Sign' => $Sign, 'SyncType' => 1, 'CusNo' => base64_encode($CusNo));

    $client = new HttpClient($GLOBALS['danbaoSV'][$minfo['ComTransType']]['site']);
    $client->setDebug(false);
    $client->setMaxRedirects(20);
    if (!$client->post($GLOBALS['danbaoSV'][$minfo['ComTransType']]['url'], $arr)) {
      return array("resp_code" => "E000001", "resp_msg" => "网络连接错误", "PriKey" => $PriKey);
    }else{
      
	  
	  //判断http响应值
      if ($client->getContent() == self::$httpRetOk){
        //从ccb_log_detail表中读取返回的结果
        $logdetail = $this->_dao->getRow( "SELECT * FROM ccb_log_detail WHERE ID='".$PriKey."'", 0);
        if( empty( $logdetail ) ){
          return array("resp_code" => "E000002", "resp_msg" => "读取返回数据错误", "PriKey" => $PriKey);
        }
		
        //4.解析xml内容
        $content = base64_decode($logdetail['ContentReturn']);
        if ($content == ""){
          return array("resp_code" => "E000003", "resp_msg" => "解析返回数据错误", "PriKey" => $PriKey);
        }
        $arrSingle = $this->GetAllSingleValue($content);
        $arrList = $this->GetListData($content);
        //更新ccb_log_detail
        $this->_dao->execute( "UPDATE ccb_log_detail SET ReturnInfor ='".base64_encode($arrSingle["resp_msg"])."' WHERE ID =".$PriKey);
        if ($arrSingle["resp_code"] == "000000000000"){
          //会员资金账户记录
          echo "sss";
		  //function FinanceLog($Mid, $SiteNo, $MoneyTitle, $Money, $MoneyType, $OrderID, $FinanceDesc){
          //$this->FinanceLog($minfo['ID'], $GLOBALS['danbaoSV'][$minfo['ComTransType']]['MCH_NO'], $paraBody["OUT_AMT_SIT_NO"], "会员出金", $paraBody["TX_AMT"], "2", "", $paraBody["RMRK"], $minfo['ComTransType']);
        }
        return array("resp_code" => $arrSingle["resp_code"]
                   , "resp_msg" => $arrSingle["resp_msg"]
                   , "PriKey" => $PriKey
                   , "arrSingle" => $arrSingle
                   , "arrList" => $arrList);
      }else{
        return array("resp_code" => "E000004", "resp_msg" => "接口服务器返回错误", "PriKey" => $PriKey);
      }
    }

  }




/*
*
*	 nicc end 
*
*/


}
?>
