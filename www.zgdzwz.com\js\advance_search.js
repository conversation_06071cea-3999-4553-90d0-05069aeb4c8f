document.write("<script type='text/javascript' src='sys.conf.js'></script>");

function advance_search( pmobj, evt ){
   if( pmobj.value != '' ){
	   var url = "./ajax.php?view=advancesearch&pm=" + pmobj.value;
       var ajax = new Ajax( url, p_advance_search, '' );
   }
}

function advance_search2( pm, evt ){
   if( pm!= '' ){
	   var url = "./ajax.php?view=advancesearch&pm=" + pm;
       var ajax = new Ajax( url, p_advance_search, '' );
   }
}

function p_advance_search( returnstr ){
	//alert(returnstr);
	if(returnstr.split( '|-|' )[0] == "no" || (returnstr.split( '|-|' )[1] == 6 || returnstr.split( '|-|' )[1] == 7 || returnstr.split( '|-|' )[1] == 8 || returnstr.split( '|-|' )[1] == 9))
	{
		document.getElementById( 'advancesearch_gg' ).innerHTML = "";
        checkR(gc_vid);
		return;
	}
    if( returnstr != '' && returnstr != 'no' ){
	    var tmp = returnstr.split( '|-|' );
		var czs = tmp[0].split( ',' );
		var gg_strs = tmp[1];
		var pztype = tmp[2];
        var vid = tmp[3];
        checkR(vid);
        
        if(pztype==6 || pztype==7 || pztype==8 || pztype==9)
        {
            document.getElementById( 'advancesearch_gg' ).innerHTML = "";
            return;
        }
        if( gg_strs.length != 0 ){
            gg_strs = gg_strs.split( ',' );
        }
        /*if( czs.length > 0 ){
            var czdiv = document.getElementById( 'advancd_search_czs' );
            var str = "<table align='center' border=1 cellspacing=0 cellpadding=1 bordercolorlight=#CCCCCC bordercolordark=#FFFFFF>";
            for( var i = 0; i < czs.length; ++i ){
                if( i % 5 == 0 ){
                    str += "<tr class='alertdivtr' style='background-color:#F2F2F2;' >";
                }
                str += "<td class='alertdivtd' ><a href='javascript:void(0);' onclick='hiddenczs(this)'>" + czs[i] + "</a></td>";
                if( i % 5 == 4 || i == czs.length - 1 ){
                    str += '</tr>';
                }
            }
            str += "</table>";
            czdiv.innerHTML = str;
        }*/
        
        if( vid == tks_vid ){//����ʯ
            document.getElementById( "advancesearch_res_cz" ).style.display = 'none';
            gg_strs = "Fe%,SiO2%,AL2O3%,P%,S%,H2O%".split( ',' );
            var str = '';
            if( gg_strs.length > 0 && gg_strs != 0){
              for( var i = 0; i < gg_strs.length; ++i ){
                  switch(i){
                        case 0:
                            var name = 'tks_fe';
                            str += gg_strs[0] + ":" + "<input type='text' style='margin:4px;width:60px;' class='inputText2' name='" + name +  "1'/>-<input type='text' style='width:60px;' class='inputText2' name='" + name +  "2'/> ";
                            break;
                        case 1:
                            var name = "tks_si";
                            str += gg_strs[1] + ":" + "<input type='text' style='margin:4px;width:60px;' class='inputText2' name='" + name +  "1'/>-<input type='text' style='width:60px;' class='inputText2' name='" + name +  "2'/> ";
                            break;
                        case 2:
                            var name = "tks_al";
                            str += gg_strs[2] + ":" + "<input type='text' style='margin:4px;width:60px;' class='inputText2' name='" + name +  "1'/>-<input type='text' style='width:60px;' class='inputText2' name='" + name +  "2'/> ";
                            break;
                        case 3:
                            var name = "attr_b";
                            str += "</br> &nbsp;&nbsp;"+gg_strs[3] + ":" + "<input type='text' style='margin:4px;width:60px;' class='inputText2' name='" + name +  "1'/>-<input type='text' style='width:60px;' class='inputText2' name='" + name +  "2'/> ";
                            break;
                        case 4:
                            var name = "attr_c";
                            str += gg_strs[4] + ":" + "<input type='text' style='margin:4px;width:60px;' class='inputText2' name='" + name +  "1'/>-<input type='text' style='width:60px;' class='inputText2' name='" + name +  "2'/> ";
                            break;
                        case 5:
                            var name = "attr_d";
                            str += gg_strs[5] + ":" + "<input type='text' style='margin:4px;width:60px;' class='inputText2' name='" + name +  "1'/>-<input type='text' style='width:60px;' class='inputText2' name='" + name +  "2'/> ";
                            break;
                    }
                }
                document.getElementById( 'advancesearch_gg' ).innerHTML = str;
            }
        }else if( vid == meitan_vid ){//ú̿
            document.getElementById( "advancesearch_res_cz" ).style.display = 'none';
            gg_strs = "�ӷ���,�ҷ�,ȫ���".split( ',' );
            var str = '';
            if( gg_strs.length > 0 && gg_strs != 0){
              for( var i = 0; i < gg_strs.length; ++i ){
                  switch(i){
                        case 0:
                            var name = 'mt_hff';
                            str += gg_strs[0] + ":" + "<input type='text' style='margin:4px;width:60px;' class='inputText2' name='" + name +  "'/>";
                            break;
                        case 1:
                            var name = "mt_hf";
                            str += gg_strs[1] + ":" + "<input type='text' style='margin:4px;width:60px;' class='inputText2' name='" + name +  "'/>";
                            break;
                        case 2:
                            var name = "mt_qlf";
                            str += gg_strs[2] + ":" + "<input type='text' style='margin:4px;width:60px;' class='inputText2' name='" + name +  "'/>";
                            break;
                    }
                }
                document.getElementById( 'advancesearch_gg' ).innerHTML = str;
            }
        }else if( vid == jiaotan_vid ){//��̿
            document.getElementById( "advancesearch_res_cz" ).style.display = 'none';
            gg_strs = "�ӷ���,�ҷ�,CSR".split( ',' );
            var str = '';
            if( gg_strs.length > 0 && gg_strs != 0){
              for( var i = 0; i < gg_strs.length; ++i ){
                  switch(i){
                        case 0:
                            var name = 'jt_hff';
                            str += gg_strs[0] + ":" + "<input type='text' style='margin:4px;width:60px;' class='inputText2' name='" + name +  "'/>";
                            break;
                        case 1:
                            var name = "jt_hf";
                            str += gg_strs[1] + ":" + "<input type='text' style='margin:4px;width:60px;' class='inputText2' name='" + name +  "'/>";
                            break;
                        case 2:
                            var name = "csr";
                            str += gg_strs[2] + ":" + "<input type='text' style='margin:4px;width:60px;' class='inputText2' name='" + name +  "'/>";
                            break;
                    }
                }
                document.getElementById( 'advancesearch_gg' ).innerHTML = str;
            }
        }else if( vid == shuini_vid ){//ˮ��
            document.getElementById( "advancesearch_res_cz" ).style.display = 'none';
            gg_strs = "���".split( ',' );
            var str = '';
            if( gg_strs.length > 0 && gg_strs != 0){
              for( var i = 0; i < gg_strs.length; ++i ){
                  switch(i){
                        case 0:
                            var name = 'sn_gg';
                            str += gg_strs[0] + ":" + "<input type='text' style='margin:4px;width:60px;' class='inputText2' name='" + name +  "'/>";
                            break;
                    }
                }
                document.getElementById( 'advancesearch_gg' ).innerHTML = str;
            }
        }else if( vid == jinshu_vid ){//����
            document.getElementById( "advancesearch_res_cz" ).style.display = 'none';
            gg_strs = "���,ǿ��,п������".split( ',' );
            var str = '';
            if( gg_strs.length > 0 && gg_strs != 0){
              for( var i = 0; i < gg_strs.length; ++i ){
                  switch(i){
                        case 0:
                            var name = 'js_gg';
                            str += gg_strs[0] + ":" + "<input type='text' style='margin:4px;width:60px;' class='inputText2' name='" + name +  "'/>";
                            break;
                        case 1:
                            var name = "js_qd";
                            str += gg_strs[1] + ":" + "<input type='text' style='margin:4px;width:60px;' class='inputText2' name='" + name +  "'/>";
                            break;
                        case 2:
                            var name = "js_xczl";
                            str += gg_strs[2] + ":" + "<input type='text' style='margin:4px;width:60px;' class='inputText2' name='" + name +  "'/>";
                            break;
                    }
                }
                document.getElementById( 'advancesearch_gg' ).innerHTML = str;
            }
        }else if( vid == hw_vid ){//����
            document.getElementById( "advancesearch_res_cz" ).style.display = 'none';
            gg_strs = "����,���,����,����".split( ',' );
            var str = '';
            if( gg_strs.length > 0 && gg_strs != 0){
                for( var i = 0; i < gg_strs.length; ++i ){
                    switch(i){
                        case 0:
                            var name = 'hw_cz';
                            str += gg_strs[0] + ":" + "<input type='text' style='margin:4px;width:60px;' class='inputText2' name='" + name +  "'/>";
                            break;
                        case 1:
                            var name = "hw_gg";
                            str += gg_strs[1] + ":" + "<input type='text' style='margin:4px;width:60px;' class='inputText2' name='" + name +  "'/>";
                            break;
                        case 2:
                            var name = "hw_kl";
                            str += gg_strs[2] + ":" + "<input type='text' style='margin:4px;width:60px;' class='inputText2' name='" + name +  "'/>";
                            break;
                        case 3:
                            var name = "hw_qf";
                            str += gg_strs[3] + ":" + "<input type='text' style='margin:4px;width:60px;' class='inputText2' name='" + name +  "'/>";
                            break;
                    }
                }
                document.getElementById( 'advancesearch_gg' ).innerHTML = str;
            }
        }else if( vid == gc_vid ){//�ֲ�
            document.getElementById( "advancesearch_res_cz" ).style.display = 'inline';
            gg_strs = "���,����,����".split( ',' );
            var str = '';
            if( gg_strs.length > 0 && gg_strs != 0){
              for( var i = 0; i < gg_strs.length; ++i ){
                  switch(i){
                        case 0:
                            var name = 'hd2';
                            str +=  gg_strs[0] + ":" + "<input type='text' style='margin:4px;' class='inputText2' name='" + name +  "1'/>-<input type='text' class='inputText2' name='" + name +  "2'/> ";
                            break;
                        case 1:
                            var name = "kd2";
                            str += gg_strs[1] + ":" + "<input type='text' style='margin:4px;' class='inputText2' name='" + name +  "1'/>-<input type='text' class='inputText2' name='" + name +  "2'/> ";
                            break;
                        case 2:
                            var name = "cd2";
                            str += gg_strs[2] + ":" + "<input type='text' style='margin:4px;' class='inputText2' name='" + name +  "1'/>-<input type='text' class='inputText2' name='" + name +  "2'/> ";
                            break;
                    }
                }
                document.getElementById( 'advancesearch_gg' ).innerHTML = str;
            }        }else{//����
            document.getElementById( "advancesearch_res_cz" ).style.display = 'inline';
            document.getElementById( 'advancesearch_gg' ).innerHTML = '';
            var str = '';
            if( gg_strs.length > 0 && gg_strs != 0){
              for( var i = 0; i < gg_strs.length; ++i ){
                  switch(i){
                        case 0:
                            var name = 'hd';
                            str += gg_strs[0] + ":" + "<input type='text' style='margin:4px;' class='inputText2' name='" + name +  "1'/>-<input type='text' class='inputText2' name='" + name +  "2'/> ";
                            break;
                        case 1:
                            var name = "kd";
                            str += gg_strs[1] + ":" + "<input type='text' style='margin:4px;' class='inputText2' name='" + name +  "1'/>-<input type='text' class='inputText2' name='" + name +  "2'/> ";
                            break;
                        case 2:
                            var name = "cd";
                            str += gg_strs[2] + ":" + "<input type='text' style='margin:4px;' class='inputText2' name='" + name +  "1'/>-<input type='text' class='inputText2' name='" + name +  "2'/> ";
                            break;
                        case 3:
                            var name = "res_ggd";
                            str += gg_strs[3] + ":" + "<input type='text' style='margin:4px;' class='inputText2' name='" + name +  "1'/>-<input type='text' class='inputText2' name='" + name +  "2'/> ";
                            break;
                    }
                }
                document.getElementById( 'advancesearch_gg' ).innerHTML = str;
            }
            //var arr = {"9"};
            /*if(pztype==6 || pztype==7 || pztype==8 || pztype==9)
            {
                document.getElementById( 'advancesearch_gg' ).innerHTML = '';
            }*/
            
        }//���� end
        
	}else{
	    document.getElementById( 'advancd_search_czs' ).innerHTML = '';
	}
}

function hiddenczs( objlink ){
    document.getElementById( "advancesearch_res_cz" ).value = objlink.innerHTML;
	document.getElementById( 'advancd_search_czs' ).style.display = 'none';
	appendbugselect();
}

function hiddengcs( objlink ){
    document.getElementById( "gc" ).value = objlink.innerHTML;
	document.getElementById( 'advancd_search_gcs' ).style.display = 'none';
	appendbugselect();
}


function hiddencitys( objlink ){
    document.getElementById( "city" ).value = objlink.innerHTML;
	document.getElementById( 'advancd_search_citys' ).style.display = 'none';
	appendbugselect();
}

function hiddenAllsearchDiv(){
    document.getElementById( 'advancd_search_citys' ).style.display = 'none';  
	document.getElementById( 'advancd_search_gcs' ).style.display = 'none';
	document.getElementById( 'advancd_search_czs' ).style.display = 'none';
	appendbugselect();
}

function hiddenAllsearchDiv2(evt){//alert(evt);
	//var gcdiv = document.getElementById( 'advancd_search_gcs' );
	//alert(gcdiv.contains(evt.srcElement));
	//alert(window.event.srcElement);
	setTimeout(alert(11),5000);
    document.getElementById( 'advancd_search_citys' ).style.display = 'none';  
	document.getElementById( 'advancd_search_gcs' ).style.display = 'none';
	document.getElementById( 'advancd_search_czs' ).style.display = 'none';
	//appendbugselect();
}

function showczs( evt ){
    if( evt == '' ){
	    evt = window.event;
	}else{
	    evt = evt;
	}
    var czdiv = document.getElementById( 'advancd_search_czs' );
	czdiv.style.display = 'block';
	document.getElementById( 'advancd_search_gcs' ).style.display = 'none';
	document.getElementById( 'advancd_search_citys' ).style.display = 'none';
	//czdiv.style.left = evt.x+document.body.scrollLeft;
	//czdiv.style.top = evt.y+document.body.scrollTop - 6;
	czdiv.style.left = document.documentElement.scrollLeft+clinetX+"px";
	czdiv.style.top = document.documentElement.scrollLeft+clinetX+"px";
	appendbugselect();
}

function showgcs( evt ){
    if( evt == '' ){
	    evt = window.event;
	}else{
	    evt = evt;
	}
    var gcdiv = document.getElementById( 'advancd_search_gcs' );
	gcdiv.style.display = 'block';
	document.getElementById( 'advancd_search_czs' ).style.display = 'none';
	document.getElementById( 'advancd_search_citys' ).style.display = 'none';
	if(browser()=="IE")
	{
		gcdiv.style.left = document.documentElement.scrollLeft+evt.clientX-100+"px";
		gcdiv.style.top = document.documentElement.scrollTop+evt.clientY+40+"px";
	}
	if(browser()!="IE")
	{
		gcdiv.style.left = document.body.scrollLeft+evt.clientX-100+"px";
		gcdiv.style.top = document.body.scrollTop+evt.clientY+40+"px";
	}
	//gcdiv.style.left = "100px";//evt.x+document.body.scrollLeft;
	//gcdiv.style.top = "100px";//evt.y+document.body.scrollTop - 6;
	//removebugselect();
}

var bugselect = '';
function showcitys( evt ){
    if( evt == '' ){
	    evt = window.event;
	}else{
	    evt = evt;
	}
    var gcdiv = document.getElementById( 'advancd_search_citys' );
	document.getElementById( 'advancd_search_gcs' ).style.display = 'none';
	document.getElementById( 'advancd_search_czs' ).style.display = 'none';
	//removebugselect();
	gcdiv.style.display = 'block';
	//gcdiv.style.left = evt.x+document.body.scrollLeft - 35;
	//gcdiv.style.top = evt.y+document.body.scrollTop - 6;
	if(browser()=="IE")
	{
		gcdiv.style.left = document.documentElement.scrollLeft+evt.clientX-100+"px";
		gcdiv.style.top = document.documentElement.scrollTop+evt.clientY+40+"px";
	}
	if(browser()!="IE")
	{
		gcdiv.style.left = document.body.scrollLeft+evt.clientX-100+"px";
		gcdiv.style.top = document.body.scrollTop+evt.clientY+40+"px";
	}
}

//�Ƴ�BUG select
function removebugselect(){
	if( document.getElementById( "advancesearch_res_order" ) ){
        bugselect = document.getElementById( "advancesearch_res_order" ).parentNode.removeChild( document.getElementById( "advancesearch_res_order" ) );
	}
}

//��ԭbugselect
function appendbugselect(){
    if( bugselect != '' ){
	    document.getElementById( "advancesearch_res_type" ).parentNode.insertBefore( bugselect, document.getElementById( "advancesearch_res_type" ) );
	}
}

function browser(){
    var userAgent = navigator.userAgent; //ȡ���������userAgent�ַ���
    var isOpera = userAgent.indexOf("Opera") > -1; //�ж��Ƿ�Opera�����
    var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera; //�ж��Ƿ�IE�����
    var isFF = userAgent.indexOf("Firefox") > -1; //�ж��Ƿ�Firefox�����
    var isSafari = userAgent.indexOf("Safari") > -1; //�ж��Ƿ�Safari�����
	var isChrome = userAgent.indexOf("Chrome") > -1
    /*if (isIE) {
        var IE5 = IE55 = IE6 = IE7 = IE8 = false;
        var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
        reIE.test(userAgent);
        var fIEVersion = parseFloat(RegExp["$1"]);
        IE55 = fIEVersion == 5.5;
        IE6 = fIEVersion == 6.0;
        IE7 = fIEVersion == 7.0;
        IE8 = fIEVersion == 8.0;
        if (IE55) {
            return "IE55";
        }
        if (IE6) {
            return "IE6";
        }
        if (IE7) {
            return "IE7";
        }
        if (IE8) {
            return "IE8";
        }
    }//isIE end*/
	if(isIE){
		return "IE";
	}
    if (isFF) {
        return "FF";
    }
    if (isOpera) {
        return "Opera";
    }
	if (isChrome) {
        return "Chrome";
    }
}

function checkR( v ){
    //console.log(v);
    var bo=document.getElementsByName("vid");
    for(var i=0;i<bo.length;i++){
        if(bo[i].value == v ){
            bo[i].checked=true;
            return;
        }
    }
    
    bo[0].checked=true;
}

function checkinput(){
    if ( ( document.form1s.tks_fe1.value.indexOf("%") > -1) || 
         ( document.form1s.tks_fe2.value.indexOf("%") > -1) || 
         ( document.form1s.tks_si1.value.indexOf("%") > -1) || 
         ( document.form1s.tks_si2.value.indexOf("%") > -1) || 
         ( document.form1s.tks_al1.value.indexOf("%") > -1) || 
         ( document.form1s.tks_al2.value.indexOf("%") > -1) || 
         ( document.form1s.attr_b1.value.indexOf("%") > -1) || 
         ( document.form1s.attr_b2.value.indexOf("%") > -1) || 
         ( document.form1s.attr_c1.value.indexOf("%") > -1) || 
         ( document.form1s.attr_c2.value.indexOf("%") > -1) ||
         ( document.form1s.attr_d1.value.indexOf("%") > -1) || 
         ( document.form1s.attr_d2.value.indexOf("%") > -1) 
         ) {
		alert("Fe,SiO2,AL2O3,P,S,H2O,���ܺ�%��������");
		return false;
	}
}