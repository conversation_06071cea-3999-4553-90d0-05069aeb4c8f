<{include file="../header.html" }>
<link type="text/css" rel="stylesheet" href="css/orderDetail.css" />
<script type="text/javascript" src="js/tytabs.jquery.min.js"></script>
<script type="text/javascript">
<!--
$(document).ready(function(){
	$("#tabsholder").tytabs({
	tabinit:"1",
	fadespeed:"fast"
	});
});   
-->
</script>
<style>
div.loading{   
	position: fixed;
	top: 50%;
	left: 50%;
	width: 220px;
	height: 48px;
	padding: 19px 0 0 36px;
	margin: -33px 0 0 -128px;
	border: 1px solid #d6d6d6;
	background: #fff;
	z-index: 999;
	line-height: 30px;
	-webkit-box-shadow: 0 0 3px #ccc;
	-moz-box-shadow: 0 0 3px #ccc;
	box-shadow: 0 0 3px #ccc;
}
div.loading img{
	float: left;
	vertical-align: middle;
}
div.loading span{
	float: left;
	margin-left: 10px;
	vertical-align: middle;
}

</style>
<!--add by xiakang for yunqian started 2015/05/29-->
<style type="text/css">
<!--
div.progressBar-two dl,
div.progressBar-two dt,
div.progressBar-two dd{
	width: 88px;
}
-->
</style>
<!--add by xiakang for ended yunqian 2015/05/29-->
<script>
//Added for quanxian sheding  by hzp started  2015/1/13
var sid='';
function selectht(id){
	  sid=id;
	  //alert(sid);
	 var param1 = "action=GetUserState";
     var ajax = new Ajax("member.php",GetState2,param1);
	
}
function GetState2(returnstr){
        var par=returnstr.split(";");
		//alert(returnstr);
         var pos= par[0].indexOf("1");
         if((parseInt(par[1])==1)||pos>=0){
		 	//	 str =window.showModalDialog("member.php?view=selectmodle&id="+sid,1,"dialogWidth=500px;dialogHeight=300px");
         // if(str==undefined || str==null) return;
         // else{
		//alert(str);
	      // window.open(str+"&hth="+sid);
	    // }
		 window.open("member.php?view=selectmodle&id="+sid,"newwindow","height=300,width=500,top=200,left=500");
	   }
	   else{
		alert("对不起，您没有操作权限");
		 document.getElementById("loading").style.display="none";
		 window.location.reload();
	  }
	 }
	 //Added for quanxian sheding  by hzp ended  2015/1/13
	 //订单取消验证
//Added for quanxian sheding  by hzp started  2015/1/13
var ht_id='';
function htoff(htid){
 // alert(htid);
  var param1 = "action=GetUserState";
  ht_id=htid;
  var ajax = new Ajax("member.php",htGetState,param1);
}
function htGetState(returnstr){
        var par=returnstr.split(";");
		 //alert(returnstr);
         var pos= par[0].indexOf("4");
         if((parseInt(par[1])==1)||pos>=0){
		 window.open("member.php?view=htoff&tid="+ht_id,"_blank");
	     }
	   else{
		alert("对不起，您没有操作权限");
		 document.getElementById("loading").style.display="none";
		 window.location.reload();
	  }
	 }
//Added for quanxian sheding  by hzp ended  2015/1/13
</script>
<script src="./js/ajax.js?"+Math.random() ></script>
<script> 

document.write("<s"+"cript type='text/javascript' src='/js/ajaxRequest.js?"+Math.random()+"'></scr"+"ipt>"); 
document.write("<s"+"cript type='text/javascript' src='/js/ajaxfrom.js?"+Math.random()+"'></scr"+"ipt>"); 
</script> 
<div class="loading" id="loading" style="display:none;">
	<img src="img/loading.gif" />
    <span>正在提交中，请稍后...</span>
</div>
<div id="container" class="container"><!--------------------------  container  ------------------------------>

	<div class="location"><!--  当前位置  -->
    	<span style="margin-right: -3px;">当前位置：</span>
        <span><a href="/" target="">网站首页</a></span> > 
        <span><a href="bizorder.php?view=myorder&type=1" target="">我的订单</a></span> > 
        <span>验收单</span>
    </div><!--  当前位置 end  <{$zy.OrderNo}>--><!--$ht.PayType-->
    <div class="clear"></div>


<!--UPdate by xiakang for yunqian started 2015/05/22-->
<{if $ht.contractTye=="0"}>
	
    <div class="progressBar wrap mt30"><!--  进度条  -->
    	<ul>
        	<dl>
            	<span>1</span>
            	<dt>资源发布</dt>
                <dd><{$fbdate|date_format:"%Y-%m-%d"}><br /><{$fbdate|date_format:"%H:%M:%S"}></dd>
            </dl>
        	<dl <{if $zy.Status == 1}>class=""<{/if}>>
            	<span>2</span>
            	<dt>草约生成</dt>
                <dd><{$zy.CreateDate|date_format:"%Y-%m-%d"}><br /><{$zy.CreateDate|date_format:"%H:%M:%S"}></dd>
            </dl>
        	<dl <{if $qrdate.Status=='' && $zy.Status == 1}>class="on"<{/if}>><!--    class='on' 即表示当前步骤    -->
            	<span>3</span>
            	<dt>订单洽谈</dt>
                <dd><{$qrdate.CreateDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.CreateDate|date_format:"%H:%M:%S"}></dd>
            </dl>
        	<dl <{if $qrdate.Status == 5 || $qrdate.Status == 11 }>class="on"<{/if}>>
            	<span>4</span>
            	<dt>电子合同</dt>
                <dd><{if $ht.PayType=="1" && $qrdate.FkDate!="0000-00-00 00:00:00"}><{$qrdate.FkDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.FkDate|date_format:"%H:%M:%S"}><{/if}>
				<{if $ht.PayType=="4" && $qrdate.FhDate!="0000-00-00 00:00:00"}><{$qrdate.FhDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.FhDate|date_format:"%H:%M:%S"}><{/if}>

				</dd>
            </dl>

<{if $ht.PayType=="1"}>
			<dl <{if  $qrdate.Status == 5 || $qrdate.Status == 11 }>class="on"<{/if}>>
            	<span>5</span>
            	<dt>付款</dt>
                <dd><{if $qrdate.FkDate!="0000-00-00 00:00:00"}><{$qrdate.FkDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.FkDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>


        	<dl <{if   $qrdate.Status == 12 }>class="on"<{/if}>>
            	<span>6</span>
            	<dt>发货</dt>
                <dd><{if $qrdate.FhDate!="0000-00-00 00:00:00"}><{$qrdate.FhDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.FhDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>

        	<dl <{if   $qrdate.Status == 7 || $qrdate.Status == 13}>class="on"<{/if}>>
            	<span>7</span>
            	<dt>收货</dt>
                <dd><{if $qrdate.YsDate!="0000-00-00 00:00:00"}><{$qrdate.YsDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.YsDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>
			
        	<dl <{if   $qrdate.Status == 8  || $qrdate.Status == 9}>class="on"<{/if}>>
            	<span>8</span>
            	<dt>结算</dt>
                <dd><{if $qrdate.JsDate!="0000-00-00 00:00:00"}><{$qrdate.JsDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.JsDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>
			
        	<dl <{if   $qrdate.Status == 14 || $qrdate.Status == 15 }>class="on"<{/if}>>
            	<span>9</span>
            	<dt>付尾款</dt>
                <dd><{if $qrdate.SwkDate!="0000-00-00 00:00:00"}><{$qrdate.SwkDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.SwkDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>
<{else}>

			<dl <{if  $qrdate.Status == 5 }>class="on"<{/if}>>
            	<span>5</span>
            	<dt>发货</dt>
                <dd><{if $qrdate.FhDate!="0000-00-00 00:00:00"}><{$qrdate.FhDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.FhDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>


        	<dl <{if   $qrdate.Status == 7 ||  $qrdate.Status == 13 }>class="on"<{/if}>>
            	<span>6</span>
            	<dt>收货</dt>
                <dd><{if $qrdate.YsDate!="0000-00-00 00:00:00"}><{$qrdate.YsDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.YsDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>

        	<dl <{if   $qrdate.Status == 8 || $qrdate.Status == 9}>class="on"<{/if}>>
            	<span>7</span>
            	<dt>结算</dt>
                <dd><{if $qrdate.JsDate!="0000-00-00 00:00:00"}><{$qrdate.JsDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.JsDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>
			
        	<dl <{if   $qrdate.Status == 14 ||  $qrdate.Status == 15}>class="on"<{/if}>>
            	<span>8</span>
				<dt>付款</dt>
            	<dd><{if $qrdate.SwkDate!="0000-00-00 00:00:00"}><{$qrdate.SwkDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.SwkDate|date_format:"%H:%M:%S"}><{/if}></dd>
                <dd></dd>
            </dl>

<{/if}>
        </ul>
    </div><!--  进度条 end  -->
<!-----------------------------------------------------------------------------------------><!-----------------------------------------------------------------------------------------><!----------------------------------------------------------------------------------------->
<{else}>
	<div class="progressBar progressBar-two wrap mt30"><!--  进度条  -->
    	<ul>
        	<dl>
            	<span>1</span>
            	<dt>资源发布</dt>
                <dd><{$fbdate|date_format:"%Y-%m-%d"}><br /><{$fbdate|date_format:"%H:%M:%S"}></dd>
            </dl>
        	<dl <{if $zy.Status == 1}>class=""<{/if}>>
            	<span>2</span>
            	<dt>草约生成</dt>
                <dd><{$zy.CreateDate|date_format:"%Y-%m-%d"}><br /><{$zy.CreateDate|date_format:"%H:%M:%S"}></dd>
            </dl>
        	<dl <{if $qrdate.Status=='' && $zy.Status == 1}>class="on"<{/if}>><!--    class='on' 即表示当前步骤    -->
            	<span>3</span>
            	<dt>订单洽谈</dt>
                <dd><{$qrdate.CreateDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.CreateDate|date_format:"%H:%M:%S"}></dd>
            </dl>
			<dl <{if $qrdate.Status == 5 || $qrdate.Status == 16 }>class="on"<{/if}>><!--    class='on' 即表示当前步骤    -->
            	<span>4</span>
            	<dt>合同签署</dt>
	<!--Update by xiakang for yunqian started 2015/05/29-->
		<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && ($yqsp.shipper_sign_status =="2" && $yqsp.consignee_sign_status =="2")}>
                <dd><{$yqsp.shipper_sign_time|date_format:"%Y-%m-%d"}><br /><{$yqsp.shipper_sign_time|date_format:"%H:%M:%S"}></dd>
				<{/if}>
		<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Consignee && ($yqsp.shipper_sign_status =="2" && $yqsp.consignee_sign_status =="2")}>
                <dd><{$yqsp.consignee_sign_time|date_format:"%Y-%m-%d"}><br /><{$yqsp.consignee_sign_time|date_format:"%H:%M:%S"}></dd>
		  <{/if}>
	<!--Update by xiakang for yunqian ended 2015/05/29-->
            </dl>
        	<dl <{if $qrdate.Status == 16 || $qrdate.Status == 17}>class="on"<{/if}>>
            	<span>5</span>
            	<dt>电子合同</dt>
                <dd><{if $ht.PayType=="1" && $qrdate.FkDate!="0000-00-00 00:00:00"}><{$qrdate.FkDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.FkDate|date_format:"%H:%M:%S"}><{/if}>
				<{if $ht.PayType=="4" && $qrdate.FhDate!="0000-00-00 00:00:00"}><{$qrdate.FhDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.FhDate|date_format:"%H:%M:%S"}><{/if}>

				</dd>
            </dl>

<{if $ht.PayType=="1"}>
			<dl <{if $qrdate.Status == 17 || $qrdate.Status == 11 }>class="on"<{/if}>>
            	<span>6</span>
            	<dt>付款</dt>
                <dd><{if $qrdate.FkDate!="0000-00-00 00:00:00"}><{$qrdate.FkDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.FkDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>


        	<dl <{if   $qrdate.Status == 12 }>class="on"<{/if}>>
            	<span>7</span>
            	<dt>发货</dt>
                <dd><{if $qrdate.FhDate!="0000-00-00 00:00:00"}><{$qrdate.FhDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.FhDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>

        	<dl <{if   $qrdate.Status == 7 || $qrdate.Status == 13}>class="on"<{/if}>>
            	<span>8</span>
            	<dt>收货</dt>
                <dd><{if $qrdate.YsDate!="0000-00-00 00:00:00"}><{$qrdate.YsDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.YsDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>
			
        	<dl <{if   $qrdate.Status == 8  || $qrdate.Status == 9}>class="on"<{/if}>>
            	<span>9</span>
            	<dt>结算</dt>
                <dd><{if $qrdate.JsDate!="0000-00-00 00:00:00"}><{$qrdate.JsDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.JsDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>
			
        	<dl <{if   $qrdate.Status == 14 || $qrdate.Status == 15 }>class="on"<{/if}>>
            	<span>10</span>
            	<dt>付尾款</dt>
                <dd><{if $qrdate.SwkDate!="0000-00-00 00:00:00"}><{$qrdate.SwkDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.SwkDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>
<{else}>

			<dl <{if  $qrdate.Status == 17 }>class="on"<{/if}>>
            	<span>6</span>
            	<dt>发货</dt>
                <dd><{if $qrdate.FhDate!="0000-00-00 00:00:00"}><{$qrdate.FhDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.FhDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>


        	<dl <{if   $qrdate.Status == 7 ||  $qrdate.Status == 13 }>class="on"<{/if}>>
            	<span>7</span>
            	<dt>收货</dt>
                <dd><{if $qrdate.YsDate!="0000-00-00 00:00:00"}><{$qrdate.YsDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.YsDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>

        	<dl <{if   $qrdate.Status == 8 || $qrdate.Status == 9}>class="on"<{/if}>>
            	<span>8</span>
            	<dt>结算</dt>
                <dd><{if $qrdate.JsDate!="0000-00-00 00:00:00"}><{$qrdate.JsDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.JsDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>
			
        	<dl <{if   $qrdate.Status == 14 ||  $qrdate.Status == 15}>class="on"<{/if}>>
            	<span>9</span>
				<dt>付款</dt>
            	<dd><{if $qrdate.SwkDate!="0000-00-00 00:00:00"}><{$qrdate.SwkDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.SwkDate|date_format:"%H:%M:%S"}><{/if}></dd>
                <dd></dd>
            </dl>

<{/if}>
        </ul>
    </div><!--  进度条 end  -->
<{/if}>
<!--UPdate by xiakang for yunqian ended 2015/05/22-->
    <div class="clear"></div>

    <div class="tijiaoBJ mt10 oh bd_d6" style="<{if $ht.Status=='10'}>display:none;<{/if}>">
<{if $ht.Status != "21" && $ht.Status != "22" && $zy.Status!=3 && $zy.Status!=4}>
<div id="qrdiv2"  style="margin-top:10px;display:none">
<img src="images/btn_qrshd_gray.png" type="button" style="cursor:pointer;float:right;" width="92" height="26"  />
</div>
<div id="qrdiv0"  style="margin-top:10px;">
<input type="hidden" name="htid" id="htid" value="<{$ht.ID}>">
<input type="hidden" name="ddid" id="ddid" value="<{$params.id}>">
<label class="fr">


<{if $ht.PayType == "2" || $ht.PayType == "1"}>


<!-------收发货-------------------------------------------------------------------------------------------->

			<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && $ht.Status=="13"}>
			<img src="images/btn_qrys.png" type="button" style="cursor:pointer;" width="80" height="26" onclick="qrys(this);"/><!--确认验收-->
			<{/if}>

			<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Consignee && ($ht.Status=="6" || $ht.Status=="7")}>
            <{if $orderstatus > 0}><!--add by hezpeng for 多次发货 started 2016/12/02 orderstatus>0有未确认的收货单，可以再次收货-->
			<img src="images/btn_qrshd.png" type="button" style="cursor:pointer;" width="92" height="26" onclick="qrsh(this);" /><!-- -->
			<{else}>
            <img src="images/btn_qrshd_gray.png" type="button" style="cursor:pointer;" width="92" height="26" />
			<{/if}>
			<{/if}>

			<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Consignee && ($ht.Status=="11" || $ht.Status=="12")}>
			<img src="images/btn_qrshd_gray.png" type="button" style="cursor:pointer;" width="92" height="26" />
			<{/if}>
<!-------收发货-------------------------------------------------------------------------------------------->

<{/if}>

<{if $ht.PayType == "3" || $ht.PayType == "4"}>


			<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && $ht.Status=="13"}>
			<img src="images/btn_qrys.png" type="button" style="cursor:pointer;" width="80" height="26" onclick="qrys(this);"/><!--确认验收-->
			<{/if}>

			<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Consignee && ($ht.Status=="6" || $ht.Status=="7")}>
            <{if $orderstatus > 0}><!--add by hezpeng for 多次发货 started 2016/12/02 orderstatus>0有未确认的收货单，可以再次收货-->
			<img src="images/btn_qrshd.png" type="button" style="cursor:pointer;" width="92" height="26" onclick="qrsh(this);" /><!--收货-->
            <{else}>
            <img src="images/btn_qrshd_gray.png" type="button" style="cursor:pointer;" width="92" height="26" />
			<{/if}>
			<{/if}>
<!--Update by xiakang for yunqian started 2015/06/02-->
      <{if $ht.contractTye=="0"}>	
			<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Consignee &&  ($ht.Status=="5" || ($ht.IsConfirmSo == "0" && $ht.IsConfirmBo == "1"))}>
			<img src="images/btn_qrshd_gray.png" type="button" style="cursor:pointer;" width="92" height="26" />
			<{/if}>
      <{else}>
            <{if $smarty.session.SYS_COMPANYID == $ht.Mid_Consignee &&  ($ht.Status=="17" || ($ht.IsConfirmSo == "0" && $ht.IsConfirmBo == "1"))}>
			<img src="images/btn_qrshd_gray.png" type="button" style="cursor:pointer;" width="92" height="26" />
			<{/if}>
	  <{/if}>
<!--Update by xiakang for yunqian started 2015/06/02-->



<{/if}>

</label>
</div>
<{/if}>
    </div>
    <div class="clear"></div>

   
	<div class="total bd_d6 bg_f8 mt10 oh"><!--  总计  -->
        <ul>
        	<li class="fl">
            	<span>订单编号：<{$zy.OrderNo}></span>
                <span>订单日期：<{$zy.CreateDate}></span>
				<span>订单状态：<{$htstatus[$ht.Status]}></span>
            </li>
 <!--update by xiakang for yunqian started 2015/06/10-->
	<{if $ht.contractTye=="0"}>	
		<li class="fl" ><{if $ht.Status > 4 && $ht.Status != 21 && $ht.Status != 22 }>
			<{if $ht.Status != "10"}>
			<!--member.php?view=htoff&tid=<{$ht.ID}>  target="_blank"-->
			<a href="javascript:void(0)"  onclick="htoff('<{$ht.ID}>')">
			<img src="img/btn_ddqx.png"></a> <{/if}>
			<a href="javascript:selectht('<{$ht.ID}>')"><img src="img/btn_xzht.png"></a		
			<{/if}>
		</li>
	<{/if}>
    <{if $ht.contractTye=="1"}>	
		<li class="fl" >
		<{if $ht.Status > 4 && $ht.Status != 21 && $ht.Status != 22 }>
			<{if $ht.Status != "10"}>
			<a href="javascript:void(0)"  onclick="htoff('<{$ht.ID}>')">
			<img src="img/btn_ddqx.png"></a> 
			<{/if}>
		  <{if $ht.Status=="5"}>
			<a href="bizorder.php?action=yqckht&id=<{$ht.ID}>"><img src="img/btn_ckht.png"></a>
		  <{else}>
		    <a href="bizorder.php?action=yqckht&id=<{$ht.ID}>"><img src="img/btn_ckht.png"></a>
		    <a href="bizorder.php?action=yqxzht&id=<{$ht.ID}>"><img src="img/btn_xzht.png"></a>
		   <{/if}>
		<{/if}>

			</li>
	<{/if}>
<!--update by xiakang for yunqian ended 2015/06/10-->
            <li class="fr">
                <span style="margin-top: 3px;">订单总数量<b class="num"><{$zy.Tweight}></b>吨，总金额：<b class="price">￥<{$zy.Tmoney}></b>元</span>
            </li>
    	</ul>
    </div><!--   总计 end  -->
	
	<div class="lianjie mt10 oh"> 
		<a href="bizorder.php?view=orderdetail&id=<{$params.id}>" target="_blank">洽谈订单</a> |
		<a href="bizorder.php?view=payment&id=<{$params.id}>" target="_blank" >订单支付</a> |
    	<a href="bizorder.php?view=fhysd&id=<{$params.id}>" target="_blank" >发货单</a> |
		
        <a href="bizorder.php?view=shysd&id=<{$params.id}>" target="_blank" class="on">验收单</a> |
        
        <{if $ht.Status == "10" ||  $ht.Status=="14" ||  $ht.Status=="15" }><a href="bizorder.php?view=account&id=<{$params.id}>" target="_blank">结算单</a><{else}><a>结算单</a><{/if}>


        <{if $ht.Status == "10" ||  $ht.Status=="8" || $ht.Status > 13 }><a href="pdf.php?action=receive&id=<{$ht.BID}>" target="_blank">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;打印验收单</a><{/if}>
		<{if $ht.Status == "10" ||  $ht.Status=="8" || $ht.Status > 13 }><a href="pdf.php?action=shd&id=<{$ht.BID}>" target="_blank">&nbsp;&nbsp;&nbsp;打印收货单</a><{/if}>

      

    </div>
    <div class="clear"></div>
	
    <div class="clear"></div>
    
    <div class="buyerInfo bd_d6 mt10 oh"><!--   买家信息  -->
    	<p>
        	<span class="fl">采购方</span>
           	<span class="fr">供应方</span>
        </p>
        <div class="clear"></div>
        
        <table class="fl">
            <tbody>
            	<tr>
                	<th>公司名称：</th>
                    <td><{$buycp.ComName}></td>
                </tr>

            </tbody>
        </table>
        
        <table class="fr">
        	<tbody>
            	<tr>
    				<th>公司名称：</th>
                    <td><{$sellcp.ComName}></td>
                </tr>

			</tbody>
		</table>
    
    </div><!--  buyerInfo end  -->
    <div class="clear"></div>


<{if $ht.Status=="7" ||$ht.Status=="13"}>
<{foreach from=$zydetail  item=v key=k }>
    <div class="fahuoContact mt10 oh" ><!--  发货联系人信息  -->
    
        <div class="order1 bd_d6 oh"  id="showfhxx_<{$v.oid}>" style="display:block;" >

            <div class="title sameT1">
                <h1>发货单信息</h1>
            </div>
			<div class="clear"></div>
            
            <div class="ordercontent">
                <table> 
					<tr>
						<th>发货单号：</th>
                        <td ><{$v.FhNum}></td>
						<th>发货日期：</th>
                        <td ><{$v.FhDate}></td>
                        <th>车船号：</th>
                        <td ><{$v.Carnum}></td>
                        
                    </tr>
					<{if $ht.Delivery!=1}>
                    <tr>
						<th>运输公司：</th>
                        <td ><{$yscom2[$v.Yscom]}></td>
						<th>联系人：</th>
                        <td ><{$v.Fhlxr}></td>
                        <th>联系电话：</th>
                        <td ><{$v.Fhphone}></td>
                      
                        
                    </tr>
					<{/if}>
					<tr>
					<th>备注：</th>
                     <td colspan="5" id="Fhbz<{$v.oid}>"><{$v.bz}></td>
					</tr>
				</table>              
            </div>

<div class="ordercontent2" style="clear:both;border-left: 0px solid #d6d6d6;">
				<table style="padding-top:10px;"> <!--合同状态 5  12 确认发货-->
					
					<!--Updated by quanjw for meijiao start 2015/1/20 增加 class和煤炭焦炭tr-->
					<!--Added by quanjw for meijiao start 2015/2/3 将radio放到table里面，美化-->
					<tr>
					<td>资源类型</td>
						<td colspan='11'>
						<{foreach from = $ZiYuType item=type_v key=type_k}>
							<input name="bigpz" type="radio" value="<{$type_k}>"  onclick="show_shd();" <{if $type_k==0}>checked<{/if}> ><{$type_v}>
					 	<{/foreach}>
					<!--Added by shizg for uploadpicture started 2016/10/26-->
						<div align="right">
						<input type="button" name="piclistbutton" value="照片列表" onclick="window.open('bizorder.php?view=shpicturelists&Oid=<{$v.oid}>&ShorFh=2&xiugai=0&Status=<{$v.Status}>','','scrollbars=no,width=650,height=550,left=800,top=100')">
						</div >
					<!--Added by shizg for uploadpicture ended 2016/10/26-->

						</td>
					</tr>
					<!--Added by quanjw for meijiao end 2015/2/3-->
					<tr class="tr_Vid_<{$MEITAN_VID}>">
						<td>品名</td>
						<td>挥发分</td>
						<td>灰分</td>
						<td>全硫分</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
				
					<tr class="tr_Vid_<{$JIAOTAN_VID}>">
						<td>品名</td>
						<td>灰分</td>
						<td>全硫分</td>
						<td>CSR</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
					
						<tr class="tr_Vid_0">
						<td>品名</td>
						<td>材质</td>
						<td>规格</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
					<tr class="tr_Vid_<{$SHUINI_VID}>">
						<td>品名</td>
						<td>规格</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
					<!--Added by quanjw for jinshuzhipin start 2015/2/26-->
					<tr class="tr_Vid_<{$JINSHU_VID}>">
						<td>品名</td>
						<td>规格</td>
						<td>强度</td>
						<td>锌层重量</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
                    <tr class="tr_Vid_<{$HW_VID}>">
						<td>品名</td>
						<td>材质</td>
						<td>规格</td>
                        <td>抗拉</td>
                        <td>屈服</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
				
				<!--Updated by quanjw for meijiao end 2015/1/20-->
<{foreach from=$pzzlist[$v.oid] item=v2 key=k2}>
<{assign var=fhs value=$x+$y}>
										<!--Updated by quanjw for meijiao start 2015/1/20-->
                    <!--Updated by quanjw for jinshuzhipin start 2015/2/26-->
                    <!--<tr class="<{if $v2.Vid == $MEITAN_VID}>tr_Vid_<{$v2.Vid}><{else}><{if $v2.Vid == $JIAOTAN_VID}>tr_Vid_<{$v2.Vid}><{else}><{if $v2.Vid == $SHUINI_VID}>tr_Vid_<{$v2.Vid}><{else}>tr_Vid_0<{/if}><{/if}><{/if}>">-->
                    <tr class="<{if $v2.Vid == $MEITAN_VID || $v2.Vid == $JIAOTAN_VID || $v2.Vid == $SHUINI_VID || $v2.Vid == $JINSHU_VID || $v2.Vid== $HW_VID}>tr_Vid_<{$v2.Vid}><{else}>tr_Vid_0<{/if}>">
                    <!--Updated by quanjw for jinshuzhipin end 2015/2/26-->    
                    <!--updated by quanjw for meijiao end 2015/1/20-->   
                        <td id=""><{$v2.pinming}></td>
                        <!--Updated by quanjw for meijiao start 2015/2/9-->
                        <{if $v2.Vid == $MEITAN_VID}>
                        <td id=""><{$v2.caizhi}></td>
                        <td id="" ><{$v2.guige}></td>
                         <td id="" ><{$v2.cd}></td> 
                        <{elseif $v2.Vid == $JIAOTAN_VID}>
                        <td id="" ><{$v2.guige}></td> 
                        <td id=""><{$v2.caizhi}></td>
                        <td id="" ><{$v2.cd}></td>
                        <{elseif $v2.Vid == $SHUINI_VID}>
                        <td id="" ><{$v2.guige}></td> 
                        <{elseif $v2.Vid == $JINSHU_VID}>
                        <td id="" ><{$v2.guige}></td>
                        <td id="" ><{$v2.strength}></td>
                        <td id="" ><{$v2.xincengWeight}></td>
                        <{elseif $v2.Vid == $HW_VID}>
                        <td id=""><{$v2.caizhi}></td>
                        <td id="" ><{$v2.guige}></td>
                        <td id="" ><{$v2.yongtu}></td>
                        <td id="" ><{$v2.strength}></td>
                        <{else}>
                        <td id=""><{$v2.caizhi}></td>
                        <td id="" ><{$v2.guige}></td> 
                        <{/if}>
                         <!--Updated by quanjw for meijiao end 2015/2/9-->
                        <td id=""><{if $v2.Factory==''}><{$v2.OriginCode}><{else}><{$v2.Factory}><{/if}></td>
                        <td id=""><{$v2.Fhjz}></td>
                        <td id=""><{$v2.Fhjs}></td>
                        <td id=""><{$v2.Fhsl}></td>
						<td id=""><{if $v2.MfgDate == '0000-00-00'}><{else}><{$v2.MfgDate}><{/if}></td>
						<td id=""><{$v2.zbs}></td>
						<td id=""><{$v2.FhNum}></td>
						
                    </tr>
<{/foreach}>
					<!--Updated by quanjw for meijiao start 2015/1/20 colspan 10改成12-->
					<tr><td colspan=12 style="text-align:right;float;right;padding-right:30px;"><b>小计：</b><b class="price"><{$v.Fhsl}></b>吨</td></tr>
					<!--Updated by quanjw for meijiao end 2015/1/20-->
                </table>
</div>
    	</div>

</div>
<{/foreach}>
<div class="tijiaoBJ mt10 oh bd_d6" >
	<label class="fr"  style="padding-right:30px;"><b>合计：</b><b class="price"><{$Allfh}></b>吨</label>
</div>
<{/if}>
<div style="padding-bottom:20px;"></div>
<!---------------------------------------------收货信息------------------------------------------------------------------------->
<form name="shxxform" id="shxxform" action="bizorder.php?action=shxx" method="post" >
<{foreach from=$zydetail  item=v key=k }>
    <div class="fahuoContact mt10 oh" style="padding-bottom:20px;"><!--  信息  -->
       <{if $k=="0"}><input type="hidden" id="firstid" value="<{$v.oid}>"><{/if}>
<!--<{if $k=="1" && $smarty.session.SYS_COMPANYID == $ht.Mid_Consignee && ($ht.Status==7 || $ht.Status==6)}><input type="radio" value="1" onclick="changefhxx()">验收信息同上<{/if}>--> 
        <div class="order1 bd_d6 oh"  id="showshxx_<{$v.oid}>" style="display:block;" >

            <div class="title sameT1">
                <h1>收货单信息</h1>
                
                <{if $zy.Status!=3 && $zy.Status!=4}>
                    <{if $smarty.session.SYS_COMPANYID == $ht.Mid_Consignee && ($ht.Status==7 || $ht.Status==6)}>
                        <!--Added for quanxian sheding  by hzp started  2014/12/25-->
                        <{if $IsModify>0}>
                        <!--Added for quanxian sheding  by hzp ended  2014/12/25-->
                            <!--多次发货start--><!--add by hezpeng for 多次发货 started 2016/12/02 status=2双方已确认的发货单，不能再次修改-->
                            <{if ($v.Status2=='1' || $v.Status2=='0')  && $ht.AllowMultiFh=='1'}>
                                <a href=javascript:void(0) onclick='editshxx_plus(<{$v.oid}>)' id="lxr">展开修改>>></a>
                            <{elseif $v.Status2=='2' && $ht.AllowMultiFh=='1'}>
                                <a>双方已确认</a>
                            <{elseif $v.Status2=='3' && $ht.AllowMultiFh=='1'}>
                                <a><font color="#ff0000">数量不一致，已回退</font></a>
                            <!--多次发货end-->
                            <{else}>
                                <!--单次发货start-->
                                <a href=javascript:void(0) onclick='editshxx_plus(<{$v.oid}>)' id="lxr">展开修改>>></a>
                                <!--单次发货end-->
                            <{/if}>
                        <{/if}>
                    <{/if}>
                <{/if}>
            </div>
			<div class="clear"></div>
            
            <div class="ordercontent">
                <table>
                    <tr>
						<th>发货单号：</th>
                        <td ><{$v.FhNum}></td>
						<th>收货日期：</th>
                        <td ><{if $v.ShDate=='0000-00-00'}><{$nowdate}><{else}><{$v.ShDate}><{/if}></td>
						<th>车船号：</th>
                        <td><{$v.Carnum}></td>
                        
                        </tr>
						<{if $ht.Delivery!=1}>
						<tr>
							<th>运输公司:</th>
							<td ><{$yscom2[$v.Yscom]}></td>
						  <th>验收人：</th>
						  <td ><{$v.Shlxr}></td>
						  <th>联系电话：</th>
                        <td><{$v.Shphone}></td>
						 
						</tr>
						<{/if}>
						<tr>
						<th>验收备注：</th>
                        <td  colspan="5"><{$v.Shbz}></td>
                    </tr>
                </table>            
            </div>
			<div class="ordercontent2" style="clear:both;border-left: 0px solid #d6d6d6;">
				
				<table style="padding-top:10px;"> <!--合同状态 5  12 确认发货-->
					<!--Updated by quanjw for meijiao start 2015/1/20 增加 class和煤炭焦炭tr-->
					<!--Added by quanjw for meijiao start 2015/2/3 将radio放到table里面，美化-->
					<tr>
					<td>资源类型</td>
						<td colspan='11'>
						<div align="left">
						<{foreach from = $ZiYuType item=type_v key=type_k}>
							<input name="bigpz2" type="radio" value="<{$type_k}>" id="<{$v.oid}><{$type_k}>" onclick="show_fhd(<{$v.oid}><{$type_k}>);" <{if $type_k==0}>checked<{/if}> ><{$type_v}>
					 	<{/foreach}>
						</div >
					<!--Added by shizg for uploadpicture started 2016/10/26-->
						<div align="right">
						<input type="button" name="piclistbutton" value="照片列表" onclick="window.open('bizorder.php?view=shpicturelists&Oid=<{$v.oid}>&ShorFh=1&xiugai=0&Status=<{$v.Status2}>','','scrollbars=no,width=650,height=550,left=800,top=100')">
						</div >
					<!--Added by shizg for uploadpicture ended 2016/10/26-->
						</td>
                    </tr>
					<!--Added by quanjw for meijiao end 2015/2/3-->
					<tr class="fhd_tr_Vid_<{$MEITAN_VID}>">
						<td>品名</td>
						<td>挥发分</td>
						<td>灰分</td>
						<td>全硫分</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
				
					<tr class="fhd_tr_Vid_<{$JIAOTAN_VID}>">
						<td>品名</td>
						<td>灰分</td>
						<td>全硫分</td>
						<td>CSR</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
					
						<tr class="fhd_tr_Vid_0">
						<td>品名</td>
						<td>材质</td>
						<td>规格</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
					<tr class="fhd_tr_Vid_<{$SHUINI_VID}>">
						<td>品名</td>
						<td>规格</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
					<!--Added by quanjw for jinshuzhipin start 2015/2/26-->
					<tr class="fhd_tr_Vid_<{$JINSHU_VID}>">
						<td>品名</td>
						<td>规格</td>
						<td>强度</td>
						<td>锌层重量</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
                    <tr class="fhd_tr_Vid_<{$HW_VID}>">
						<td>品名</td>
						<td>材质</td>
						<td>规格</td>
                        <td>抗拉</td>
                        <td>屈服</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
				
				<!--Updated by quanjw for meijiao end 2015/1/20-->
<{foreach from=$pzzlist[$v.oid] item=v2 key=k2}>
					<!--Updated by quanjw for jinshuzhipin start 2015/2/26-->
                    <!--<tr class="<{if $v2.Vid == $MEITAN_VID}>fhd_tr_Vid_<{$v2.Vid}><{else}><{if $v2.Vid == $JIAOTAN_VID}>fhd_tr_Vid_<{$v2.Vid}><{else}><{if $v2.Vid == $SHUINI_VID}>fhd_tr_Vid_<{$v2.Vid}><{else}>fhd_tr_Vid_0<{/if}><{/if}><{/if}>">-->
                    <tr class="<{if $v2.Vid == $MEITAN_VID || $v2.Vid == $JIAOTAN_VID || $v2.Vid == $SHUINI_VID || $v2.Vid == $JINSHU_VID || $v2.Vid== $HW_VID}>fhd_tr_Vid_<{$v2.Vid}><{else}>fhd_tr_Vid_0<{/if}>">
                    <!--Updated by quanjw for jinshuzhipin end 2015/2/26--> 
                        <td id=""><{$v2.pinming}></td>
                        <!--Updated by quanjw for meijiao start 2015/1/20-->
                        <{if $v2.Vid == $MEITAN_VID}>
                        <td id=""><{$v2.caizhi}></td>
                        <td id="" ><{$v2.guige}></td>
                         <td id="" ><{$v2.cd}></td>
                        <{elseif $v2.Vid == $JIAOTAN_VID}>
                        <td id="" ><{$v2.guige}></td>
                        <td id=""><{$v2.caizhi}></td>
                        <td id="" ><{$v2.cd}></td>
                        <{elseif $v2.Vid == $SHUINI_VID}>
                        <td id="" ><{$v2.guige}></td> 
                        <{elseif $v2.Vid == $JINSHU_VID}>
                        <td id="" ><{$v2.guige}></td>
                        <td id="" ><{$v2.strength}></td>
                        <td id="" ><{$v2.xincengWeight}></td>
                        <{elseif $v2.Vid == $HW_VID}>
                        <td id=""><{$v2.caizhi}></td>
                        <td id="" ><{$v2.guige}></td>
                        <td id="" ><{$v2.yongtu}></td>
                        <td id="" ><{$v2.strength}></td>
                        <{else}>
                        <td id=""><{$v2.caizhi}></td>
                        <td id="" ><{$v2.guige}></td> 
                        <{/if}>
                         <!--Updated by quanjw for meijiao end 2015/1/20-->
                        <td id=""><{if $v2.Factory==''}><{$v2.OriginCode}><{else}><{$v2.Factory}><{/if}></td>
                        <td id=""><{$v2.Shjz}></td>
                        <td id=""><{$v2.Shjs}></td>
                        <td id=""><{if $v2.Fhsl == $v2.Shsl}><{$v2.Shsl}><{else}><font color="red"><{$v2.Shsl}></font><{/if}></td>
						<td id=""><{if $v2.MfgDate == '0000-00-00'}><{else}><{$v2.MfgDate}><{/if}></td>
						<td id=""><{$v2.zbs}></td>
						<td id=""><{$v2.FhNum}></td>
                    </tr>
<{/foreach}>
					<!--Updated by quanjw for meijiao start 2015/1/20-->
					<!-- Updated for xiaoji by Zhu Dahua started  2015/09/16-->
					<tr><td colspan=12 style="text-align:right;float;right;padding-right:30px;"><b>小计：</b><b class="price"><{$v.Shsl}></b>吨</td></tr>
					<!--<tr><td colspan=12 style="text-align:right;float;right;padding-right:30px;"><b>小计：</b><b class="price"><{if $v.ischange == '0' && $v.Shsl == '0'}><{$v.Fhsl}><{else}><{$v.Shsl}><{/if}></b>吨</td></tr>-->
					<!-- Updated for xiaoji by Zhu Dahua ended  2015/09/16-->
					<!--Updated by quanjw for meijiao end 2015/1/20-->
                </table>
			</div>

    	</div>
<!------------------------------------------------------------------------------------------------------------------------------------->
        <div class="order1 bd_d6 oh"  id="shxxdiv_<{$v.oid}>" style="display:none;" >
            <div class="title sameT1">
            	<!--Updated by quanjw for jinshuzhipin start 2015/3/10-->
                <!--<h1>收货单信息</h1><a href=javascript:void(0) onclick='closeshxx(<{$v.oid}>)' id="lxr">关闭</a>-->
                <h1>收货单信息</h1><a href=javascript:void(0) onclick='closeshxx_plus(<{$v.oid}>)' id="lxr">关闭</a>
				<!--Updated by quanjw for jinshuzhipin end 2015/3/10-->
				<!--<a href="javascript:void()" onclick="shxxPay(<{$v.oid}>);"> 保存&nbsp;&nbsp;&nbsp;</a>-->
			</div>
			<div class="clear"></div>
            
            <div class="ordercontent">
			
                <table>
                    <tr>
						<th>发货单号：</th>
                        <td ><{$v.FhNum}></td>
						<th>收货日期：</th>
                        <td id=""><input onclick="WdatePicker()" class="Wdate" type="text" name="ShDate<{$v.oid}>" id="ShDate2<{$v.oid}>" <{if $v.ShDate==0}>value="<{$nowdate}>"<{else}>value="<{$v.ShDate}>"<{/if}> "></td>
                       <{if $ht.Delivery!=1}>
						<th>运输公司:</th>
                        <td ><{$yscom2[$v.Yscom]}></td>
						</tr><tr>
						<th>验收人：</th>
                        <td id=""><input type="text" name="Shlxr<{$v.oid}>" id="Shlxr2<{$v.oid}>" value="<{$v.Shlxr}>"></td>
                        <th>联系电话：</th>
                        <td id=""><input type="text" name="Shphone<{$v.oid}>"  id="Shphone2<{$v.oid}>" value="<{$v.Shphone}>"></td><{/if}>
						<th>车船号：</th>
                        <td><{$v.Carnum}></td>
						</tr></tr>
                        <th>验收备注：</th>
                        <td id=""  colspan=5 ><textarea  cols="120" rows="4"  style="margin-top:5px;margin-bottom:5px;resize:none;" name="Shbz<{$v.oid}>" value="<{$v.Shbz}>"></textarea></td>
                    </tr>
					
                    <input type="hidden" name="shflag" id="shflag" value="<{$v.oid}>">
                </table>            
            </div>

			<div class="ordercontent2" style="clear:both;border-left: 0px solid #d6d6d6;">
				<table style="padding-top:10px;" id="addtr<{$v.oid}>"> <!--合同状态 5  12 确认发货-->
					<!--Updated by quanjw for meijiao start 2015/1/20 增加 class和煤炭焦炭tr-->
					<!--Added by quanjw for meijiao start 2015/2/3 将radio放到table里面，美化-->
					<tr>
					<td>资源类型</td>
						<td colspan='11'>
						<div align="left">
						<{foreach from = $ZiYuType item=type_v key=type_k}>
							<input name="bigpz3<{$v.oid}>" type="radio" value="<{$type_k}>" id="<{$v.oid}>&&<{$type_k}>"  onclick="show_fhd2('<{$v.oid}>&&<{$type_k}>');" <{if $type_k==0}>checked<{/if}> ><{$type_v}>
					 	<{/foreach}>
						</div>
					<!--Added by shizg for uploadpicture started 2016/10/26-->
						<div align="right">
					    <{if $v.Status2=='1' || $v.Status2=='0'}>
						<input type="button" name="picbutton" value="上传照片" onclick="window.open('bizorder.php?view=shuploadpicture&Oid=<{$v.oid}>&ShorFh=1','','scrollbars=no,width=650,height=350,left=800,top=100')">
						<{/if}>
						<input type="button" name="piclistbutton" value="照片列表" onclick="window.open('bizorder.php?view=shpicturelists&Oid=<{$v.oid}>&ShorFh=1&xiugai=1&Status=<{$v.Status2}>','','scrollbars=no,width=650,height=550,left=800,top=100')">
						</div >
					<!--Added by shizg for uploadpicture ended 2016/10/26-->
						</td>

					</tr>
					<!--Added by quanjw for meijiao end 2015/2/3-->
					<tr class="fhd_tr_Vid_<{$MEITAN_VID}><{$v.oid}>">
						<td>品名</td>
						<td>挥发分</td>
						<td>灰分</td>
						<td>全硫分</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
				
					<tr class="fhd_tr_Vid_<{$JIAOTAN_VID}><{$v.oid}>">
						<td>品名</td>
						<td>灰分</td>
						<td>全硫分</td>
						<td>CSR</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
					
						<tr class="fhd_tr_Vid_0<{$v.oid}>">
						<td>品名</td>
						<td>材质</td>
						<td>规格</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
					<tr class="fhd_tr_Vid_<{$SHUINI_VID}><{$v.oid}>">
						<td>品名</td>
						<td>规格</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
					<!--Added by quanjw for jinshuzhipin start 2015/2/26-->
					<tr class="fhd_tr_Vid_<{$JINSHU_VID}><{$v.oid}>">
						<td>品名</td>
						<td>规格</td>
						<td>强度</td>
						<td>锌层重量</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
					<!--Added by quanjw for jinshuzhipin end 2015/2/26-->
                    <tr class="fhd_tr_Vid_<{$HW_VID}><{$v.oid}>">
						<td>品名</td>
						<td>材质</td>
						<td>规格</td>
                        <td>抗拉</td>
                        <td>屈服</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
				
				<!--Updated by quanjw for meijiao end 2015/1/20 不科学-->
<{foreach from=$pzzlist[$v.oid] item=v2 key=k2}>
                    <!--Updated by quanjw for jinshuzhipin start 2015/2/26-->
                    <!--<tr class="<{if $v2.Vid == $MEITAN_VID}>fhd_tr_Vid_<{$v2.Vid}><{else}><{if $v2.Vid == $JIAOTAN_VID}>fhd_tr_Vid_<{$v2.Vid}><{else}><{if $v2.Vid == $SHUINI_VID}>fhd_tr_Vid_<{$v2.Vid}><{else}>fhd_tr_Vid_0<{/if}><{/if}><{/if}>">-->
                    <tr class="<{if $v2.Vid == $MEITAN_VID || $v2.Vid == $JIAOTAN_VID || $v2.Vid == $SHUINI_VID || $v2.Vid == $JINSHU_VID || $v2.Vid== $HW_VID}>fhd_tr_Vid_<{$v2.Vid}><{$v.oid}><{else}>fhd_tr_Vid_0<{$v.oid}><{/if}>">
                    <!--Updated by quanjw for jinshuzhipin end 2015/2/26-->       
                        <td id=""><{$v2.pinming}></td>
                         <!--Updated by quanjw for meijiao start 2015/1/20-->
                        <{if $v2.Vid == $MEITAN_VID}>
                        <td id=""><{$v2.caizhi}></td>
                        <td id="" ><{$v2.guige}></td>
                         <td id="" ><{$v2.cd}></td>
                        <{elseif $v2.Vid == $JIAOTAN_VID}>
                        <td id="" ><{$v2.guige}></td> 
                        <td id=""><{$v2.caizhi}></td>
                        <td id="" ><{$v2.cd}></td>
                        <{elseif $v2.Vid == $SHUINI_VID}>
                        <td id="" ><{$v2.guige}></td> 
                        <{elseif $v2.Vid == $JINSHU_VID}>
                        <td id="" ><{$v2.guige}></td>
                        <td id="" ><{$v2.strength}></td>
                        <td id="" ><{$v2.xincengWeight}></td>
                        <{elseif $v2.Vid == $HW_VID}>
                        <td id=""><{$v2.caizhi}></td>
                        <td id="" ><{$v2.guige}></td>
                        <td id="" ><{$v2.yongtu}></td>
                        <td id="" ><{$v2.strength}></td>
                        <{else}>
                        <td id=""><{$v2.caizhi}></td>
                        <td id="" ><{$v2.guige}></td> 
                        <{/if}>
                         <!--Updated by quanjw for meijiao end 2015/1/20-->
                        <td id=""><{if $v2.Factory==''}><{$v2.OriginCode}><{else}><{$v2.Factory}><{/if}></td>
                        <td id=""><input type="text" size="6" name="Shjz<{$v2.ID}>" id="Shjz2<{$v2.ID}>" value="<{$v2.Shjz}>" onblur="yunsuan('<{$v2.ID}>','<{$v.oid}>')"></td>
                        <td id=""><input type="text" size="6" name="Shjs<{$v2.ID}>" id="Shjs2<{$v2.ID}>"  value="<{$v2.Shjs}>"  onblur="yunsuan('<{$v2.ID}>','<{$v.oid}>')"></td>
                        <td id=""><input type="text" size="6" name="Shsl<{$v2.ID}>" id="Shsl2<{$v2.ID}>" value="<{$v2.Shsl}>"  onblur="checkSL('<{$v2.ID}>','<{$v.Ddid}>')"></td>
						<td id=""><input type="text" size="8" name="MfgDate<{$v2.ID}>" id="MfgDate2<{$v2.ID}>"  <{if $v2.MfgDate == '0000-00-00'}>value=""<{else}> value="<{$v2.MfgDate}>"<{/if}> style="border:solid 1px #8eade0; width:100px; height:20px;" onclick="WdatePicker()" class="Wdate"></td>
						<td id=""><{$v2.zbs}></td>
						<td id=""><input type="text" size="8" name="FhNum<{$v2.ID}>" id="FhNum2<{$v2.ID}>" value="<{$v2.FhNum}>">
						<!--a style="cursor:pointer;" onclick="deleteod('addtr<{$v.oid}>',event.srcElement.parentElement.parentElement.rowIndex,'<{$v2.ID}>')">删除</a>--></td>
                    </tr >
<{/foreach}>
	
                    <!--tr>
                        <th>资源编号：</th>
                        <td id=""><{$v.Pid}></td>           
                        <th>品名：</th>
                        <td id=""><{$v.VarietyName}></td>
						<th>材质：</th>
                        <td id=""><{$v.MaterialCode}></td>
                    </tr>
                    <tr>
					    <th>规格：</th>
                        <td id="" ><{$v.SpecCode}></td>
                        <th>产地（钢厂）：</th>
                        <td colspan="3" id=""><{$v.Factory}></td>
                    </tr>
                    <tr>
                        <th><font color="red">*</font>数量：</th>
                        <td id=""><input type="text" name="Fhsl<{$v.oid}>" id="Fhsl2<{$v.oid}>" value="<{$v.Fhsl}>"></td>
                        <th><font color="red">*</font>件数：</th>
                        <td id=""><input type="text" name="Fhjs<{$v.oid}>" id="Fhjs2<{$v.oid}>"  value="<{$v.Fhjs}>"></td>
                        <th>件重：</th>
                        <td id=""><input type="text" name="Fhjz<{$v.oid}>" value="<{$v.Fhjz}>"></td>
                    </tr-->


                    <input type="hidden" name="fhflag" id="fhflag" value="<{$v.oid}>">
                </table>
            	<!--div class="contactList2"><input type="button" value="添加"  id="b1<{$v.oid}>" onclick="add('addtr<{$v.oid}>',<{$v.oid}>)"-->
				<img src="images/btn_bc.png" type="button" style="cursor: pointer;padding-left:100px;"  onclick="shxxPay(<{$v.oid}>);" />
				<!-- Updated for xiaoji by Zhu Dahua started  2015/09/16-->
<font  style="float:right;padding-right:30px;"><b>小计：</b><b class="price" id="xiaoj<{$v.oid}>"><{$v.Shsl}></b>吨</font>
<!--<font  style="float:right;padding-right:30px;"><b>小计：</b><b class="price" id="xiaoj<{$v.oid}>"><{if $v.ischange == '0' && $v.Shsl == '0'}><{$v.Fhsl}><{else}><{$v.Shsl}><{/if}></b>吨</font>-->
<!-- Updated for xiaoji by Zhu Dahua ended  2015/09/16-->
				</div>

			</div>

    	</div>
<script>
function show_fhd2(str){
//added by shizg for 资源选择问题 started 2017/02/24

if(str!=null&&str!=''){
		var voids=str.split("&&");
	var oid = voids[0];
	if(voids[1]!="999"){
		var boxes = document.getElementsByName("bigpz3"+oid);
		for(i=0;i<boxes.length;i++){
			$("#"+boxes[i].id).removeAttr("checked"); 
		}
			$("#"+str).attr("checked", "checked");
	}
}
  		var rdo3=$("input[name='bigpz3" +oid+"']:checked").val();
//added by shizg for 资源选择问题 ended 2017/02/24
  		//Updated by quanjw for jinshuzhipin start 2015/2/26
  		$(".fhd_tr_Vid_<{$MEITAN_VID}>"+oid).hide();
 		$(".fhd_tr_Vid_<{$JIAOTAN_VID}>"+oid).hide();
 		$(".fhd_tr_Vid_<{$SHUINI_VID}>"+oid).hide();
 		$(".fhd_tr_Vid_<{$JINSHU_VID}>"+oid).hide();
 		$(".fhd_tr_Vid_0"+oid).hide();
		$(".fhd_tr_Vid_<{$HW_VID}>"+oid).hide();
  		switch(rdo3){
  			case "<{$MEITAN_VID}>":
  				$(".fhd_tr_Vid_<{$MEITAN_VID}>"+oid).show();
  				break;
  			case "<{$JIAOTAN_VID}>":
  				$(".fhd_tr_Vid_<{$JIAOTAN_VID}>"+oid).show();
  				break;
  			case "<{$SHUINI_VID}>":
  				$(".fhd_tr_Vid_<{$SHUINI_VID}>"+oid).show();
  				break;
  			case "<{$JINSHU_VID}>":
  				$(".fhd_tr_Vid_<{$JINSHU_VID}>"+oid).show();
  				break;
			case "<{$HW_VID}>":
				$(".fhd_tr_Vid_<{$HW_VID}>"+oid).show();
				break;
  			default:
  				$(".fhd_tr_Vid_0"+oid).show();
  		}
  		//Updated by quanjw for jinshuzhipin end 2015/2/26
} 

</script>


<{/foreach}>
<div class="tijiaoBJ mt10 oh bd_d6">
<label class="fr"  style="padding-right:30px;">
<!-- Updated for heji by Zhu Dahua started  2015/09/16-->
<b>合计：</b><b class="price" id="allj"><{$Allsh}></b>吨
<!--<b>合计：</b><b class="price" id="allj"><{if $ischange == 0 && $Allsh == 0}><{$Allfh}><{else}><{$Allsh}><{/if}></b>吨-->
<!-- Updated for heji by Zhu Dahua ended  2015/09/16-->
</label>
</div>
</form>
<script>
	
	
function checkSL(item,oid){
	
	var sl=document.getElementById("Shsl2"+item).value;
	if(sl<0){
		alert("请输入正确的数量！");
		document.getElementById("Shsl2"+item).value=0;
		return;
		}
		yunsuan(item,oid);
	}
	
	
function yunsuan(item,oid){
	//alert(item);
var xiaoj=0;
var xiaoj2=0;
var allj=0;	

if(document.getElementById("Status2"+item).checked== true){}else{return false;}


	var jz=document.getElementById("Shjz2"+item).value;

	var js=document.getElementById("Shjs2"+item).value;
	var sl=(jz*10)*(js*10)/100;
	if(jz<0||js<0||sl<0)
	{	
		if(jz<0){
		/*alert("请输入正确的件重！");	
		document.getElementById("Shjz2"+item).value=0;
		return;*/
		}
		if(js<0){
		alert("请输入正确的件数！");	
		document.getElementById("Shjs2"+item).value=0;
		return;
		}
		
	}
	//var sl=accMul(jz,js);
	


<{foreach from=$zydetail  item=v key=k }>

<{foreach from=$pzzlist2[$v.oid] item=v2 key=k2}>



if(oid=="<{$v.Ddid}>"){
var zz="<{$v2.BuyQuantity}>";
}

if(document.getElementById("Status2<{$v2.ID}>").checked== true){

//alert(zz);return false;
	var jz2=document.getElementById("Shjz2<{$v2.ID}>").value;

	var js2=document.getElementById("Shjs2<{$v2.ID}>").value;

	var sl2=document.getElementById("Shsl2<{$v2.ID}>").value;
	if(sl2 ==0)
	{
		var xiaoj = xiaoj + jz2*js2;
	}
	else
	{
		var xiaoj = xiaoj +sl2;
	}
	if(oid=="<{$v.Ddid}>"){
		var jz3=document.getElementById("Shjz2<{$v2.ID}>").value;

		var js3=document.getElementById("Shjs2<{$v2.ID}>").value;

		var sl3=document.getElementById("Shsl2<{$v2.ID}>").value;
		if(sl3 ==0)
	{
		var xiaoj2 = xiaoj2 + jz3*js3;
	}
	else
	{
		var xiaoj2 = xiaoj2 +sl3;
	}
	}


	if(js2=="0" && jz2=="0"){
		var allj = parseFloat(allj) + parseFloat(sl2);
	}else{
		var allj = allj + jz2*js2;
	}

}
//alert("3");
<{/foreach}>
//alert(xiaoj);
//updated by quanjw for sl start 2015/1/8
//document.getElementById("xiaoj<{$v.oid}>").innerHTML= parseFloat(xiaoj).toFixed(3);
document.getElementById("xiaoj<{$v.oid}>").innerHTML= parseFloat(xiaoj).toFixed(4);
//updated by quanjw for sl end 2015/1/8
xiaoj = 0;
jz2 = 0;
js2 = 0;
<{/foreach}>
//updated by quanjw for sl start 2015/1/8
//document.getElementById("allj").innerHTML= parseFloat(allj).toFixed(3);
document.getElementById("allj").innerHTML= parseFloat(allj).toFixed(4);
//updated by quanjw for sl end 2015/1/8
//alert(sl);
//"jisuan"+oid();
}


function deleteod(id,rowIndex,oid){
//	alert(rowIndex);
	//var table =document.all[tableID].deleteRow(rowIndex);
if(rowIndex=="1"){return false;}
	var styles = document.getElementById(id);
	styles.deleteRow(rowIndex);

	var param = "action=ajaxaddorder&id="+oid + "&type=2";
	var ajax = new Ajax("bizorder.php",setdata2,param);
}

function add(id,oid){

 //newTr.cells[0].firstChild.value = newTr.rowIndex;
// document.getElementById("b1"+id).disabled = newTr.rowIndex ==5 ;


 var oTr = document.getElementById(id).rows[1];

 var newTr = oTr.cloneNode(true);
 document.getElementById(id).getElementsByTagName("tbody")[0].appendChild(newTr);	

	var param = "action=ajaxaddorder&id="+oid;
	var ajax = new Ajax("bizorder.php",setdata2,param);
}
function setdata2(result){

location.reload();
}
</script>


    <div class="tijiaoBJ mt10 oh bd_d6" style="<{if $ht.Status=='10'}>display:none;<{/if}>">
<{if $ht.Status != "21" && $ht.Status != "22" && $zy.Status!=3 && $zy.Status!=4}>
<div id="qrdiv3"  style="margin-top:10px;display:none">
<img src="images/btn_qrshd_gray.png" type="button" style="cursor:pointer;float:right;" width="92" height="26"  />
</div>
<div id="qrdiv1"  style="margin-top:10px;">
<input type="hidden" name="htid" id="htid" value="<{$ht.ID}>">
<input type="hidden" name="ddid" id="ddid" value="<{$params.id}>">
<label class="fr">


<{if $ht.PayType == "2" || $ht.PayType == "1"}>


<!-------收发货-------------------------------------------------------------------------------------------->


			<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && $ht.Status=="13"}>
			<img src="images/btn_qrys.png" type="button" style="cursor:pointer;" width="80" height="26" onclick="qrys(this);"/><!--确认验收-->
			<{/if}>

			<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Consignee && ($ht.Status=="6" || $ht.Status=="7")}>
            <{if $orderstatus > 0}><!--add by hezpeng for 多次发货 started 2016/12/02 orderstatus>0有未确认的收货单，可以再次收货-->
			<img src="images/btn_qrshd.png" type="button" style="cursor:pointer;" width="92" height="26" onclick="qrsh(this);" /><!-- -->
            <{else}>
            <img src="images/btn_qrshd_gray.png" type="button" style="cursor:pointer;" width="92" height="26" />
			<{/if}>
			<{/if}>

			<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Consignee && ($ht.Status=="11" || $ht.Status=="12")}>
			<img src="images/btn_qrshd_gray.png" type="button" style="cursor:pointer;" width="92" height="26"
			<{/if}>
<!-------收发货-------------------------------------------------------------------------------------------->

<{/if}>

<{if $ht.PayType == "3" || $ht.PayType == "4"}>


			<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && $ht.Status=="13"}>
			<img src="images/btn_qrys.png" type="button" style="cursor:pointer;" width="80" height="26" onclick="qrys(this);"/><!--确认验收-->
			<{/if}>

			<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Consignee && ($ht.Status=="6" || $ht.Status=="7")}>
            <{if $orderstatus > 0}><!--add by hezpeng for 多次发货 started 2016/12/02 orderstatus>0有未确认的收货单，可以再次收货-->
			<img src="images/btn_qrshd.png" type="button" style="cursor:pointer;" width="92" height="26" onclick="qrsh(this);" /><!--收货-->
            <{else}>
            <img src="images/btn_qrshd_gray.png" type="button" style="cursor:pointer;" width="92" height="26" />
			<{/if}>
			<{/if}>
<!--Update by xiakang for yunqian started 2015/06/02-->
      <{if $ht.contractTye=="0"}>	
			<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Consignee &&  ($ht.Status=="5" || ($ht.IsConfirmSo == "0" && $ht.IsConfirmBo == "1"))}>
			<img src="images/btn_qrshd_gray.png" type="button" style="cursor:pointer;" width="92" height="26" />
			<{/if}>
      <{else}>
            <{if $smarty.session.SYS_COMPANYID == $ht.Mid_Consignee &&  ($ht.Status=="17" || ($ht.IsConfirmSo == "0" && $ht.IsConfirmBo == "1"))}>
			<img src="images/btn_qrshd_gray.png" type="button" style="cursor:pointer;" width="92" height="26" />
			<{/if}>
	  <{/if}>
<!--Update by xiakang for yunqian started 2015/06/02-->


<{/if}>

</label>
</div>
<{/if}>
    </div>
    <div class="clear"></div>
    
    
    <div class="talkAndTrack mt10 wrap bd_d6 oh" id="tabsholder"><!--  洽谈和追踪  -->
        <ul class="tabs">
            <li id="tab1">在线洽谈</li>
            <li id="tab2">订单追踪</li>
        </ul>
        <div class="contents marginbot">
    
            <div id="content1" class="tabscontent"><!--   在线洽谈   -->
                
                <div class="talkArea"><!--  洽谈区域  -->
         
<{foreach from=$qiatan item=v}>			
				  <dl>
                        <dt>
                            <a href="#" target="_blank"><img src="img/userPhoto.jpg" /></a>
                            <span class="fl"><a href="listing.php?view=zylist&mid=<{$v.SendComID}>" target="_blank" <{if $smarty.session.SYS_COMPANYID == $v.SendComID}>class="color_blue"<{else}>class="color_yellow"<{/if}>><{$v.SendComNameShort}></a></span>
                            <span class="fl"></span>
                            <span class="fr"><{$v.SendTime}> 发表</span>
                        </dt>
                        <dd>
                            <{$v.Message}>
                            <span class="fr"><a href= "javascript:void(0);" onclick="Message.focus();Message.value='';reciveID.value='<{$v.SendComID}>';messageID.value='<{$v.ID}>';"></a></span>
                        </dd>
                    </dl>
<{/foreach}>            
                 
            
                
                </div><!--   洽谈区域 end  -->
                <div class="clear"></div> 
                
                
                <div class="content replyWindow"><!--  回复窗口  -->
				<input type="hidden" name="reciveID" id="reciveID" value="<{if $zy.Did == $smarty.session.SYS_COMPANYID}><{$zy.Mid}><{else}><{$zy.Did}><{/if}>">
				<input type="hidden" name="messageID" id="messageID" value="">
                    <textarea onclick="this.innerHTML='';" name="Message" id="Message">输入你想说的话，与商家/厂家直接交流。</textarea><br />
                    <{if $smarty.session.SYS_COMPANYID ==''}><span class="fl">您还未登录，请<a href="user.php?view=login" target="_blank" class="color_blue">登录</a></span><{/if}>
                    <input type="image" src="img/btn_fabu.jpg" class="fb_btn_fr" name="Submit" onclick="sendmessage('<{$comp2.ID}>')"/>
                </div><!--  回复窗口 end  -->  
                <div class="clear"></div>     
                
                
            </div><!--   在线洽谈 end   -->
            
            <div id="content2" class="tabscontent"><!--   订单追踪   -->
                <table>
                    <thead>
                        <tr>
                            <td width="180">处理时间</td>
                            <td>处理信息</td>
                            <td align="right">操作人</td>
                        </tr>
                    </thead>
                    <tbody>
					<{foreach from=$orderfind item=v}>
                        <tr>
                            <td><{$v.CreateDate}></td>
                            <td><{$v.StatusDesc}></td>
                            <td align="right"><{$v.CreateUser}>/<{$v.ARealName}></td>
                        </tr>
					<{/foreach}>
                    
                    </tbody>
                </table>
            </div><!--   订单追踪 end   -->
            
        </div>
    </div><!--  洽谈和追踪 end  -->
    <div class="clear"></div>





   <!--  总计  -->
	<!--div class="total bd_d6 bg_f8 mt10 oh">
        <ul>
        	<li class="fl">
            	<span>订单编号：<{$zy.OrderNo}></span>
                <span>订单日期：<{$zy.CreateDate}></span>
            </li>
            <li class="fr">
                <{if $myorder=="1" && $zy.Status=="1"}><span class="btn_tick"><a href="javascript:void(0)"  onclick="qrdd(<{$zy.ID}>);">订单确认</a></span><{/if}>
                <span style="margin-top: 3px;">总数量<b class="num"><{$zy.Tweight}></b>吨，总金额：<b class="price">￥<{$zy.Tmoney}></b>元</span>
            </li>
    	</ul>
    </div--><!--   总计 end  -->
    <div class="clear"></div>

    


</div><!--  container end  -->
<div class="clear"></div>


  <{include file="../footer.html"}>


<script>
function changefhxx(){
	var id=document.getElementById("firstid").value;
	var param = "action=ajaxaddorder&id="+id + "&type=4";
	var ajax = new Ajax("member.php",setdata2,param);
}


function qrddd(flag){
	if(window.confirm("是否确认?" ))
	{
		window.open("bizorder.php?action=qrdd&id="+flag);
	}
}

function sendmessage(item){
if("<{$smarty.session.SYS_COMPANYID}>"==''){
		alert("您还未登录");
		return false;
}	


	var Message = document.getElementById("Message").value;
	var reciveID = document.getElementById("reciveID").value;
//	var messageID = document.getElementById("messageID").value;
	if(Message =='' || Message=="输入你想说的话，与商家/厂家直接交流。"){
		alert("请输入你想说的话！");
		return false;
	}
	//alert(messageID);return false;
	var param = "action=ajaxsendmsgdd&ReceiveComID="+reciveID+"&MessageType=3"+"&Message="+Message+"&orderid=<{$params.id}>" + "&time="+new Date().getTime();
	var ajax = new Ajax("tender.php",setdata,param);
}
function setdata(result){
	//alert(result);
	var data = eval('(' + result + ')');
	if(parseInt(data.error)){
		alert(data.errorinfo);
	}
	else{
		alert("发送成功");
		location.reload();
		//document.getElementById("Message").value='';
		
    }
}


function checkfh(){


	var num5 = 0;
<{foreach from=$zydetail  item=v key=k }>	
	<{foreach from=$pzzlist[$v.oid] item=v2 key=k2}>
		if(document.getElementById("Shjz2<{$v2.ID}>").value == '' || document.getElementById("Shjz2<{$v2.ID}>").value == '0') num5 ++;
	<{/foreach}>
<{/foreach}>
	if(num5 >0){	
		//return "5";
	}

	var num1 = 0;
<{foreach from=$zydetail  item=v key=k }>		
	<{foreach from=$pzzlist[$v.oid] item=v2 key=k2}>
		if(document.getElementById("Shsl2<{$v2.ID}>").value == '' || document.getElementById("Shsl2<{$v2.ID}>").value == '0') num1 ++;
	<{/foreach}>
<{/foreach}>
	if(num1 >0){	
		return "1";
	}

	var num2 = 0;
<{foreach from=$zydetail  item=v key=k }>	
	<{foreach from=$pzzlist[$v.oid] item=v2 key=k2}>
		if(document.getElementById("Shjs2<{$v2.ID}>").value == '' || document.getElementById("Shjs2<{$v2.ID}>").value == '0') num2 ++;
	<{/foreach}>
<{/foreach}>
	if(num2 >0){	
		return "2";
	}

	var num3 = 0;
<{foreach from=$zydetail  item=v key=k }>		
		if(document.getElementById("Shlxr2<{$v.oid}>").value == '' || document.getElementById("Shlxr2<{$v.oid}>").value == '0') num3 ++;
<{/foreach}>
	if(num3 >0){	
		return "3";
	}

	var num4 = 0;
<{foreach from=$zydetail  item=v key=k }>		
		if(document.getElementById("Shphone2<{$v.oid}>").value == '' || document.getElementById("Shphone2<{$v.oid}>").value == '0') num4 ++;
<{/foreach}>
	if(num4 >0){	
		return "4";
	}



}





function fhxx(id){
	//return id;
var ids = 64;
var num1=0;
var num2=0;
var num3=0;
<{foreach from=$zydetail  item=v key=k }>
//return "<{$v.oid}>";
if("<{$v.oid}>"==id){
<{foreach from=$pzzlist[$v.oid] item=v2 key=k2}>
	var jz2=document.getElementById("Shjz2<{$v2.ID}>").value;

	var js2=document.getElementById("Shjs2<{$v2.ID}>").value;

	var sl2=document.getElementById("Shsl2<{$v2.ID}>").value;
//if(parseInt(jz2)<=0){num1++;}
if(parseInt(js2)<=0){num2++;}
if(parseFloat(sl2)<=0){num3++;}

<{/foreach}>
}

<{/foreach}>

//return num2;
if(parseInt(num1)>0){return "1";exit;}
if(parseInt(num2)>0){return "2";exit;}
if(parseInt(num3)>0){return "3";exit;}
}


//Added by quanjw for meijiao start 2015/1/20
 show_shd();
function show_shd(){
  		var rdo=$("input[name='bigpz']:checked").val();
  		//Updated by quanjw for jinshuzhipin start 2015/2/26
  		$(".tr_Vid_<{$MEITAN_VID}>").hide();
 		$(".tr_Vid_<{$JIAOTAN_VID}>").hide();
 		$(".tr_Vid_0").hide();
 		$(".tr_Vid_<{$SHUINI_VID}>").hide();
 		$(".tr_Vid_<{$JINSHU_VID}>").hide();
		$(".tr_Vid_<{$HW_VID}>").hide();
  		switch(rdo){
  			case "<{$MEITAN_VID}>":
  				$(".tr_Vid_<{$MEITAN_VID}>").show();
  				break;
  			case "<{$JIAOTAN_VID}>":
  				$(".tr_Vid_<{$JIAOTAN_VID}>").show();
  				break;
  			case "<{$SHUINI_VID}>":
  				$(".tr_Vid_<{$SHUINI_VID}>").show();
  				break;
  			case "<{$JINSHU_VID}>":
  				$(".tr_Vid_<{$JINSHU_VID}>").show();
				break;
			case "<{$HW_VID}>":
				$(".tr_Vid_<{$HW_VID}>").show();
  				break;
  			default:
  				$(".tr_Vid_0").show();
  		}
  		//Updated by quanjw for jinshuzhipin end 2015/2/26
}

show_fhd();
function show_fhd(str){
 		var rdo2=$("input[name='bigpz2']:checked").val();
		if(str!=null&&str!=''){
			var boxes = document.getElementsByName("bigpz2");
			for(i=0;i<boxes.length;i++){
				$("#"+boxes[i].id).removeAttr("checked"); 
			}
			$("#"+str).attr("checked", "checked");
		}
	//Updated by quanjw for jinshuzhipin start 2015/2/26
  		$(".fhd_tr_Vid_<{$MEITAN_VID}>").hide();
 		$(".fhd_tr_Vid_<{$JIAOTAN_VID}>").hide();
 		$(".fhd_tr_Vid_<{$SHUINI_VID}>").hide();
 		$(".fhd_tr_Vid_<{$JINSHU_VID}>").hide();
 		$(".fhd_tr_Vid_0").hide();
		$(".fhd_tr_Vid_<{$HW_VID}>").hide();
  		switch(rdo2){
  			case "<{$MEITAN_VID}>":
  				$(".fhd_tr_Vid_<{$MEITAN_VID}>").show();
  				break;
  			case "<{$JIAOTAN_VID}>":
  				$(".fhd_tr_Vid_<{$JIAOTAN_VID}>").show();
  				break;
  			case "<{$SHUINI_VID}>":
  				$(".fhd_tr_Vid_<{$SHUINI_VID}>").show();
  				break;
  			case "<{$JINSHU_VID}>":
  				$(".fhd_tr_Vid_<{$JINSHU_VID}>").show();
  				break;
			case "<{$HW_VID}>":
				$(".fhd_tr_Vid_<{$HW_VID}>").show();
				break;
  			default:
  				$(".fhd_tr_Vid_0").show();
  		}
  		//Updated by quanjw for jinshuzhipin end 2015/2/26
} 

/*function show_fhd2(){
  		var rdo3=$("input[name='bigpz3']:checked").val();
  		//Updated by quanjw for jinshuzhipin start 2015/2/26
  		$(".fhd_tr_Vid_<{$MEITAN_VID}>").hide();
 		$(".fhd_tr_Vid_<{$JIAOTAN_VID}>").hide();
 		$(".fhd_tr_Vid_<{$SHUINI_VID}>").hide();
 		$(".fhd_tr_Vid_<{$JINSHU_VID}>").hide();
 		$(".fhd_tr_Vid_0").hide();
  		switch(rdo3){
  			case "<{$MEITAN_VID}>":
  				$(".fhd_tr_Vid_<{$MEITAN_VID}>").show();
  				break;
  			case "<{$JIAOTAN_VID}>":
  				$(".fhd_tr_Vid_<{$JIAOTAN_VID}>").show();
  				break;
  			case "<{$SHUINI_VID}>":
  				$(".fhd_tr_Vid_<{$SHUINI_VID}>").show();
  				break;
  			case "<{$JINSHU_VID}>":
  				$(".fhd_tr_Vid_<{$JINSHU_VID}>").show();
  				break;
  			default:
  				$(".fhd_tr_Vid_0").show();
  		}
  		//Updated by quanjw for jinshuzhipin end 2015/2/26
} 
 */ 
//Added by quanjw for jinshuzhipin start 2015/3/10	
//修正 展开修改和关闭后 资源信息和radio不对应
function editshxx_plus(id){
	editshxx(id);
	show_fhd2(id+"&&999");
}

function closeshxx_plus(id){
	closeshxx(id);
	show_fhd();
}
//Added by quanjw for jinshuzhipin end 2015/3/10
//Added by quanjw for meijiao end 2015/1/20

</script>