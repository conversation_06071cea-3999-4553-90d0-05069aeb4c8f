@charset "utf-8";
/* CSS Document */

.container{
	position: relative;
}
/* ================================================== lineFocus ====================================================== */
/* ====== 焦点图 indexFocus ====== */
#myFocus{ 
	width: 326px;
	height: 207px;
}
#myFocus img{ 
	width: 230px;
	height: 120px;    
}
div.indexFocus{
	width: 326px;
	height: 207px;
	margin-right: 10px;
}
.indexFocus .pic li{
	position: absolute;
	left: 48px;
	top: 42px;
	width: 230px;
	height: 120px;
}
.indexFocus .pic img{
	width: 230px;
	height: 120px;
}
.indexFocus .txt{
	position: absolute;
	top: 8px;
	left: 0;
}
.indexFocus .txt li{
	overflow: hidden;
	height: 26px;
	line-height: 26px;
}
.indexFocus .txt li a{
	display: block;
	color: #333;
	text-align: center;
	font-size: 12px;
	font-weight: bold;
	text-decoration: none;
}
.indexFocus .num,
.indexFocus .num_bg{
	height: 17px;
	position: absolute;
	z-index: 3;
	right: 0;
	top: 190px;
	color: #fff;
}
.indexFocus .num_bg{
	width: 100%;
	background: url(../img/indexFocus_bg.png) right bottom no-repeat;
	_height: 14px;
	_background: 0;
	_filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='../img/indexFocus_bg.png', sizingMethod='scale');
}
.indexFocus .num_bg li{
	position:absolute;
}
.indexFocus .num li{
	float: left;
	width: 22px;
	height: 15px;
	line-height: 15px;
	padding-top: 2px;
	text-align: center;
	cursor: pointer;
	position: relative;
}
.indexFocus .num li a{
	color: #000;
}
.indexFocus .num li b{
	position: absolute;
	top: 2px;
	left: 0;
	width: 1px;
	height: 15px;
	overflow: hidden;
	background: #fff;
}
.indexFocus .num li.hover,
.indexFocus .num li.current{
	background: url(../img/indexFocus_red.gif) no-repeat center 0;
}
.indexFocus .num li.hover a,
.indexFocus .num li.current a{
	color: #fff;
}


/* ====== 最新公告 latestNotice ====== */
div.latestNotice{
	width: 405px;
	height: 207px;
}
div.latestNotice ul.tabs{
	position: relative;
	width: 406px;
	right: 0;
}
div.latestNotice ul.tabs li{
	width: 100px;
}
div.latestNotice ul.tabs li.current{
	width: 102px;
}
div.latestNotice .contents{
	padding-top: 9px;
}
div.latestNotice .contents li{
	clear: both;
	height: 26px;
	line-height: 26px;
	overflow: hidden;
	background: url(../img/icon_dian_gray.png) no-repeat 10px center;
	position: relative;
	text-indent: 20px;
}
div.latestNotice .contents li p{
	float: left;
	width: 330px;
	text-overflow: ellipsis;
	white-space:  nowrap;
	overflow: hidden;
}
div.latestNotice .contents span{
	position: absolute;
	top: 0;
	right: 10px;
	color: #7f7f7f;
}

/* ====== 会员中心 memberCenter ====== */
div.memberCenter{
	width: 242px;
	height: 197px;
	padding: 10px 0 0 1px;
}
div.memberCenter li{	
	width: 94px;
	height: 31px;
	overflow: hidden;
	float: left;
	margin: 0 0 8px 18px;
	_display: inline;
}


/* ====================================================================== lineXHXS 现货销售 ========================================================================= */

/* ====== 关键字搜索 ====== */
div.keySearch{ 
	width: 743px;
}
div.keySearch .tabs ul{
	width: 750px;
}
div.keySearch .tabs li,
div.keySearch .tabs li.on{
	width: 123px;
}
div.keySearch .content input[type=text]{
	width: 492px;
}
div.keySearch .content input.inputText{
	_width: 492px;
}
div.keySearch p.enter,
div.keySearch p.hot{
	margin-left: 54px;
}


/* ====== 详细搜索 ======  */
div.xxSearch{
	background: #fff url(../img/icon_xxSearch_bg.jpg) repeat-x 0 bottom;
	_height: 96px;
	position: relative;
	padding: 3px 0 8px 10px;
}
div.xxSearch h1{
	position: absolute;
	top: 0;
	left: 0;
	float: left;
	width: 35px;
	text-align: center;
	border-right: 1px solid #dcdddd;
	height: 87px;
	padding-top: 26px;
	line-height: 16px;
}
div.xxSearch input[type=text]{
	height: 20px;
	line-height: 20px;
	width: 120px;
	text-indent: 4px;
	border: 1px solid #dbdbdb;
	background: #fff;
}
div.xxSearch input.inputText{
	_height: 20px;
	_line-height: 20px;
	_text-indent: 4px;
	_border: 1px solid #dbdbdb;
	_background: #fff;
}
div.xxSearch .input36{ width: 36px;}
div.xxSearch .input82{ width: 82px;}
div.xxSearch .input100{ width: 100px;}
div.xxSearch select{
	height: 22px;
	line-height: 22px;
	border: 1px solid #dbdbdb;
	background: #fff;
}
div.xxSearch label{
	white-space: nowrap;
	float: left;
	margin: 10px 0 0 15px;
	_margin-top: 8px;
}
div.xxSearch input[type=image]{
	float: left;
	width: 46px;
	padding: 2px;
	/* margin: 10px 0 0 15px; */
}
div.xxSearch input.inputImage{
	_float: left;
	_margin: 10px 0 0 15px;
}

/* ====== 现货销售 ====== */
div.productList{
	_width: 743px;
	height: 846px;
	_overflow: hidden;
}
div.productList .tabs{
	height: 31px;
	position: relative;
	width: 100%;
	left: -1px;
}
div.productList .tabs ul {
    display: flex;
    justify-content: space-between;
    gap: 2px;
	background: url(../img/keySearch_title_li.jpg) repeat-x;
	border-bottom: 1px solid #dcdddd;
}

div.productList .tabs li {
    /* 可选：设置间距 */
    margin-right: 10px;
}
div.productList .tabs li{

	height: 30px;
	line-height: 30px;

	border-left: 1px solid #dcdddd;

	padding: 0 4px;
	cursor: pointer;
	/* width: 74px; */
}
div.productList .tabs li a{
	display: block;
	color: #515151;
}
div.productList .tabs li.on{
	background: #fff;
	border-bottom: 1px solid #fff;
	font-weight: bold;
	/* width: 74px; */
}
div.productList .tabs li.on a{
	font-weight: bold;
}
div.productList thead tr{
	background: none;
}

/* ============================================================ lineWSXH 网上现货 ========================================================= */
div.lineTit{
	background: url(../img/productList_table_thead.jpg) repeat-x;
	height: 32px;
	position: relative;
}
div.lineTit h5{
	float: left;
	position: absolute;
	top: -1px;
	left: -6px;
	background: url(../img/icon_index_lineTit_bg.png) no-repeat;
	width: 120px;
	height: 40px;
	text-indent: 23px;
	line-height: 32px;
	font-family: "微软雅黑";
	font-size: 14px;
	color: #fff;
}
div.lineTit img{
	float: right;
	margin: 11px 10px 0 0;
	_display: inline;
}

/* ====== 网上现货 ====== */
div.productWSXH{
	height: 493px;
}

/* ====== 工程价格 engineerPrice ====== */
div.engineerPrice{
	height: 525px;
}
div.engineerPrice .tabs li,
div.engineerPrice .tabs li.on{
	width: 80px;
}
div.engineerPrice .priceCon .tags,
div.engineerPrice .priceCon .tags2{
	height: 32px;
	border-bottom: 1px solid #dcdddd;
	position: relative;
	z-index: 999;
	padding-left: 6px;
}
div.engineerPrice .priceCon .tags li,
div.engineerPrice .priceCon .tags2 li{ 
	float: left; 
	height: 32px;
	line-height: 32px;
	text-align: center;
	width: 58px;
}
div.engineerPrice .priceCon .tags a,
div.engineerPrice .priceCon .tags2 a{
	color: #515151;
}
div.engineerPrice .priceCon .tags li.selectTag,
div.engineerPrice .priceCon .tags2 li.selectTag2{
	background: url(../img/icon_triangle_dw_gray.png) no-repeat center bottom;
	font-weight: bold;
	padding-bottom: 4px;
}
div.engineerPrice .tagContent,
div.engineerPrice .tagContent2{ 
	DISPLAY: none;  
	clear: both;
}
div.engineerPrice #tagContent DIV.selectTag,
div.engineerPrice #tagContent2 DIV.selectTag2{ 
	DISPLAY: block;
}

div.engineerPrice table thead td{
	color: #999;
	font-weight: bold;
	background: #f9f9f9;
}
div.engineerPrice table td{
	height: 26px;
}
div.engineerPrice table{
	background: url(../img/icon_productList_f9_26.jpg) repeat 0 26px;
}
	


/* ===================================================== lineNews 资讯 ========================================================= */
div.blockNews{
	border-top: none;
	width: 365px;
	height: 310px;
	*height: 310px;
	overflow: hidden;
}
div.newsLeft{
	width: 366px;
}
div.blockNews ul.tabs li{
	display: block; 
	cursor: pointer;
	width: 92px;
}
div.blockNews .tabscontent{ 
	display: none;
	clear: both;
	padding-top: 9px;
	*padding-top: 0;
	*margin-top: 9px;
}
div.blockNews img.icon_more{
	margin-top: 0;
	*top: -30px;
}
div.blockNews .tabscontent li{
	clear: both;
	height: 26px;
	line-height: 26px;
	overflow: hidden;
	position: relative;
	background: url(../img/icon_dian_gray.png) no-repeat 10px center;
	text-indent: 20px;
}
div.blockNews .tabscontent li a{
	display: block;
	float: left;
	text-overflow: ellipsis;
	width: 275px;
	overflow: hidden;
	white-space: nowrap;
}
div.blockNews .tabscontent li span{
	position: absolute;
	top: 0;
	right: 10px;
	color: #7f7f7f;
}


/*  交易帮助  */
div.dealHelp{
	_height: 193px;
	_overflow: hidden;
}
div.dealHelp div.help{
	width: 721px;
	height: 36px;
	line-height: 36px;
	margin: 10px auto 0 auto;
}
div.dealHelp div.help li{
	background: url(../img/icon_dealhelp_span.png) no-repeat 0 center;
	float: left;
	padding-left: 30px;
	margin: 0 16px 0 0;
}
div.dealHelp div.help li:first-child{
	background: none;
	padding-left: 25px;
}
div.dealHelp div.help span{
	display: block;
	padding-left: 20px;
}
div.dealHelp div.help li.li01 span{
	background: url(../img/icon_dealHelp_1.png) no-repeat 0 center;
}
div.dealHelp div.help li.li02 span{
	background: url(../img/icon_dealHelp_2.png) no-repeat 0 center;
}
div.dealHelp div.help li.li03 span{
	background: url(../img/icon_dealHelp_3.png) no-repeat 0 center;
}
div.dealHelp div.help li.li04 span{
	background: url(../img/icon_dealHelp_4.png) no-repeat 0 center;
}
div.dealHelp div.help li.li05 span{
	background: url(../img/icon_dealHelp_5.png) no-repeat 0 center;
}
div.dealHelp div.help li.li06 span{
	background: url(../img/icon_dealHelp_6.png) no-repeat 0 center;
}

div.dealHelp div.list ul{
	float: left;
	background-size: 50%;
	background: url(../img/icon_footer_helpCenter_line.png) no-repeat 0 center;
	padding: 8px 10px 8px 15px;
}
div.dealHelp div.list ul:first-child{
	background: none;
}
div.dealHelp div.list li{
	clear: both;
	height: 24px;
	line-height: 24px;
	background: url(../img/icon_dian_gray.png) no-repeat 10px center;
	text-indent: 20px;
	text-overflow: ellipsis;
	width: 220px;
	white-space: nowrap;
	overflow: hidden;
}




/* ====== 最新加盟 ====== */
div.latestJoin{
}
div.latestJoin .content{
	padding-bottom: 3px;
}
div.latestJoin .content ul{
	background: url(../img/icon_lineheight26_bg.jpg) repeat;
}
div.latestJoin .content li{
	clear: both;
	height: 26px;
	line-height: 26px;
	background:url(../img/icon_dian_gray.png) no-repeat 10px center;
	text-indent: 20px;
	text-overflow: ellipsis;
	width: 200px;
	overflow: hidden;
	white-space: nowrap;
}


/* ====== 右下角会议信息 ====== */
div.meeting{
	height: 236px;
	padding: 2px;
}
div.meeting img{
	width: 239px;
	height: 175px;
	margin: 0 auto;
}
div.meeting p{
	text-align: left;
	line-height: 20px;
	padding: 2px 5px 0 10px;
}
div.meeting a:hover{
	color: #515151;
}