<{include file="../header.html" }>
<link type="text/css" rel="stylesheet" href="css/orderDetail.css" />
<script type="text/javascript" src="js/tytabs.jquery.min.js"></script>
<script type="text/javascript">
<!--
$(document).ready(function(){
	$("#tabsholder").tytabs({
	tabinit:"1",
	fadespeed:"fast"
	});
	  
	//fhxxPay(<{$tempOid}>);
	
	yunsuan(<{$tempID}>,<{$tempOid}>);     
});    
-->
</script>
<!--add by xiakang for yunqian started 2015/05/22-->
<style type="text/css">
<!--
div.progressBar-two dl,
div.progressBar-two dt,
div.progressBar-two dd{
	width: 88px;
}
-->
</style>
<!--add by xiakang for ended yunqian 2015/05/22-->
<script>
//Added for quanxian sheding  by hzp started  2015/1/13
var sid='';
function selectht(id){
	  sid=id;
	  //alert(sid);
	 var param1 = "action=GetUserState";
     var ajax = new Ajax("member.php",GetState2,param1);
	
}
function GetState2(returnstr){
        var par=returnstr.split(";");
		//alert(returnstr);
         var pos= par[0].indexOf("1");
         if((parseInt(par[1])==1)||pos>=0){
		 	//	 str =window.showModalDialog("member.php?view=selectmodle&id="+sid,1,"dialogWidth=500px;dialogHeight=300px");
         // if(str==undefined || str==null) return;
         // else{
		//alert(str);
	      // window.open(str+"&hth="+sid);
	    // }
		 window.open("member.php?view=selectmodle&id="+sid,"newwindow","height=300,width=500,top=200,left=500");
	   }
	   else{
		alert("对不起，您没有操作权限");
		 document.getElementById("loading").style.display="none";
		 window.location.reload();
	  }
	 }
//Added for quanxian sheding  by hzp ended  2015/1/30
//订单取消验证
//Added for quanxian sheding  by hzp started  2015/1/13
var ht_id='';
function htoff(htid){
 // alert(htid);
  var param1 = "action=GetUserState";
  ht_id=htid;
  var ajax = new Ajax("member.php",htGetState,param1);
}
function htGetState(returnstr){
        var par=returnstr.split(";");
		 //alert(returnstr);
         var pos= par[0].indexOf("4");
         if((parseInt(par[1])==1)||pos>=0){
		 window.open("member.php?view=htoff&tid="+ht_id,"_blank");
	     }
	   else{
		alert("对不起，您没有操作权限");
		 document.getElementById("loading").style.display="none";
		 window.location.reload();
	  }
	 }
//Added for quanxian sheding  by hzp ended  2015/1/30
</script>

<style>
div.loading{
	position: fixed;
	top: 50%;
	left: 50%;
	width: 220px;
	height: 48px;
	padding: 19px 0 0 36px;
	margin: -33px 0 0 -128px;
	border: 1px solid #d6d6d6;
	background: #fff;
	z-index: 999;
	line-height: 30px;
	-webkit-box-shadow: 0 0 3px #ccc;
	-moz-box-shadow: 0 0 3px #ccc;
	box-shadow: 0 0 3px #ccc;
}
div.loading img{
	float: left;
	vertical-align: middle;
}
div.loading span{
	float: left;
	margin-left: 10px;
	vertical-align: middle;
}

</style>
<script src="./js/ajax.js?"+Math.random() ></script>
<script> 

document.write("<s"+"cript type='text/javascript' src='/js/ajaxRequest.js?"+Math.random()+"'></scr"+"ipt>"); 
document.write("<s"+"cript type='text/javascript' src='/js/ajaxfrom.js?"+Math.random()+"'></scr"+"ipt>"); 
</script> 

<div class="loading" id="loading" style="display:none;">
	<img src="img/loading.gif" />
    <span>正在提交中，请稍后...</span>
</div>
<input type="hidden" id="tempoid" value="<{$tempOid}>">
<div id="container" class="container"><!--------------------------  container  ------------------------------>

	<div class="location"><!--  当前位置  -->
    	<span style="margin-right: -3px;">当前位置：</span>
        <span><a href="/" target="">网站首页</a></span> > 
        <span><a href="bizorder.php?view=myorder&type=1" target="">我的订单</a></span> > 
        <span>发货单</span>
    </div><!--  当前位置 end  <{$zy.OrderNo}>-->
    <div class="clear"></div>

<!--UPdate by xiakang for yunqian started 2015/05/22-->
<{if $ht.contractTye=="0"}>
	
    <div class="progressBar wrap mt30"><!--  进度条  -->
    	<ul>
        	<dl>
            	<span>1</span>
            	<dt>资源发布</dt>
                <dd><{$fbdate|date_format:"%Y-%m-%d"}><br /><{$fbdate|date_format:"%H:%M:%S"}></dd>
            </dl>
        	<dl <{if $zy.Status == 1}>class=""<{/if}>>
            	<span>2</span>
            	<dt>草约生成</dt>
                <dd><{$zy.CreateDate|date_format:"%Y-%m-%d"}><br /><{$zy.CreateDate|date_format:"%H:%M:%S"}></dd>
            </dl>
        	<dl <{if $qrdate.Status=='' && $zy.Status == 1}>class="on"<{/if}>><!--    class='on' 即表示当前步骤    -->
            	<span>3</span>
            	<dt>订单洽谈</dt>
                <dd><{$qrdate.CreateDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.CreateDate|date_format:"%H:%M:%S"}></dd>
            </dl>
        	<dl <{if $qrdate.Status == 5 || $qrdate.Status == 11 }>class="on"<{/if}>>
            	<span>4</span>
            	<dt>电子合同</dt>
                <dd><{if $ht.PayType=="1" && $qrdate.FkDate!="0000-00-00 00:00:00"}><{$qrdate.FkDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.FkDate|date_format:"%H:%M:%S"}><{/if}>
				<{if $ht.PayType=="4" && $qrdate.FhDate!="0000-00-00 00:00:00"}><{$qrdate.FhDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.FhDate|date_format:"%H:%M:%S"}><{/if}>

				</dd>
            </dl>

<{if $ht.PayType=="1"}>
			<dl <{if  $qrdate.Status == 5 || $qrdate.Status == 11 }>class="on"<{/if}>>
            	<span>5</span>
            	<dt>付款</dt>
                <dd><{if $qrdate.FkDate!="0000-00-00 00:00:00"}><{$qrdate.FkDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.FkDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>


        	<dl <{if   $qrdate.Status == 12 }>class="on"<{/if}>>
            	<span>6</span>
            	<dt>发货</dt>
                <dd><{if $qrdate.FhDate!="0000-00-00 00:00:00"}><{$qrdate.FhDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.FhDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>

        	<dl <{if   $qrdate.Status == 7 || $qrdate.Status == 13}>class="on"<{/if}>>
            	<span>7</span>
            	<dt>收货</dt>
                <dd><{if $qrdate.YsDate!="0000-00-00 00:00:00"}><{$qrdate.YsDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.YsDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>
			
        	<dl <{if   $qrdate.Status == 8  || $qrdate.Status == 9}>class="on"<{/if}>>
            	<span>8</span>
            	<dt>结算</dt>
                <dd><{if $qrdate.JsDate!="0000-00-00 00:00:00"}><{$qrdate.JsDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.JsDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>
			
        	<dl <{if   $qrdate.Status == 14 || $qrdate.Status == 15 }>class="on"<{/if}>>
            	<span>9</span>
            	<dt>付尾款</dt>
                <dd><{if $qrdate.SwkDate!="0000-00-00 00:00:00"}><{$qrdate.SwkDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.SwkDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>
<{else}>

			<dl <{if  $qrdate.Status == 5 }>class="on"<{/if}>>
            	<span>5</span>
            	<dt>发货</dt>
                <dd><{if $qrdate.FhDate!="0000-00-00 00:00:00"}><{$qrdate.FhDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.FhDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>


        	<dl <{if   $qrdate.Status == 7 ||  $qrdate.Status == 13 }>class="on"<{/if}>>
            	<span>6</span>
            	<dt>收货</dt>
                <dd><{if $qrdate.YsDate!="0000-00-00 00:00:00"}><{$qrdate.YsDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.YsDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>

        	<dl <{if   $qrdate.Status == 8 || $qrdate.Status == 9}>class="on"<{/if}>>
            	<span>7</span>
            	<dt>结算</dt>
                <dd><{if $qrdate.JsDate!="0000-00-00 00:00:00"}><{$qrdate.JsDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.JsDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>
			
        	<dl <{if   $qrdate.Status == 14 ||  $qrdate.Status == 15}>class="on"<{/if}>>
            	<span>8</span>
				<dt>付款</dt>
            	<dd><{if $qrdate.SwkDate!="0000-00-00 00:00:00"}><{$qrdate.SwkDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.SwkDate|date_format:"%H:%M:%S"}><{/if}></dd>
                <dd></dd>
            </dl>

<{/if}>
        </ul>
    </div><!--  进度条 end  -->
<!-----------------------------------------------------------------------------------------><!-----------------------------------------------------------------------------------------><!----------------------------------------------------------------------------------------->
<{else}>
	<div class="progressBar progressBar-two wrap mt30"><!--  进度条  -->
    	<ul>
        	<dl>
            	<span>1</span>
            	<dt>资源发布</dt>
                <dd><{$fbdate|date_format:"%Y-%m-%d"}><br /><{$fbdate|date_format:"%H:%M:%S"}></dd>
            </dl>
        	<dl <{if $zy.Status == 1}>class=""<{/if}>>
            	<span>2</span>
            	<dt>草约生成</dt>
                <dd><{$zy.CreateDate|date_format:"%Y-%m-%d"}><br /><{$zy.CreateDate|date_format:"%H:%M:%S"}></dd>
            </dl>
        	<dl <{if $qrdate.Status=='' && $zy.Status == 1}>class="on"<{/if}>><!--    class='on' 即表示当前步骤    -->
            	<span>3</span>
            	<dt>订单洽谈</dt>
                <dd><{$qrdate.CreateDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.CreateDate|date_format:"%H:%M:%S"}></dd>
            </dl>
			<dl <{if $qrdate.Status == 5 || $qrdate.Status == 16 }>class="on"<{/if}>><!--    class='on' 即表示当前步骤    -->
            	<span>4</span>
            	<dt>合同签署</dt>
	<!--Update by xiakang for yunqian started 2015/05/29-->
		<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && ($yqsp.shipper_sign_status =="2" && $yqsp.consignee_sign_status =="2")}>
                <dd><{$yqsp.shipper_sign_time|date_format:"%Y-%m-%d"}><br /><{$yqsp.shipper_sign_time|date_format:"%H:%M:%S"}></dd>
				<{/if}>
		<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Consignee && ($yqsp.shipper_sign_status =="2" && $yqsp.consignee_sign_status =="2")}>
                <dd><{$yqsp.consignee_sign_time|date_format:"%Y-%m-%d"}><br /><{$yqsp.consignee_sign_time|date_format:"%H:%M:%S"}></dd>
		  <{/if}>
	<!--Update by xiakang for yunqian ended 2015/05/29-->
            </dl>
        	<dl <{if $qrdate.Status == 16 || $qrdate.Status == 17}>class="on"<{/if}>>
            	<span>5</span>
            	<dt>电子合同</dt>
                <dd><{if $ht.PayType=="1" && $qrdate.FkDate!="0000-00-00 00:00:00"}><{$qrdate.FkDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.FkDate|date_format:"%H:%M:%S"}><{/if}>
				<{if $ht.PayType=="4" && $qrdate.FhDate!="0000-00-00 00:00:00"}><{$qrdate.FhDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.FhDate|date_format:"%H:%M:%S"}><{/if}>

				</dd>
            </dl>

<{if $ht.PayType=="1"}>
			<dl <{if $qrdate.Status == 17 || $qrdate.Status == 11 }>class="on"<{/if}>>
            	<span>6</span>
            	<dt>付款</dt>
                <dd><{if $qrdate.FkDate!="0000-00-00 00:00:00"}><{$qrdate.FkDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.FkDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>


        	<dl <{if   $qrdate.Status == 12 }>class="on"<{/if}>>
            	<span>7</span>
            	<dt>发货</dt>
                <dd><{if $qrdate.FhDate!="0000-00-00 00:00:00"}><{$qrdate.FhDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.FhDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>

        	<dl <{if   $qrdate.Status == 7 || $qrdate.Status == 13}>class="on"<{/if}>>
            	<span>8</span>
            	<dt>收货</dt>
                <dd><{if $qrdate.YsDate!="0000-00-00 00:00:00"}><{$qrdate.YsDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.YsDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>
			
        	<dl <{if   $qrdate.Status == 8  || $qrdate.Status == 9}>class="on"<{/if}>>
            	<span>9</span>
            	<dt>结算</dt>
                <dd><{if $qrdate.JsDate!="0000-00-00 00:00:00"}><{$qrdate.JsDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.JsDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>
			
        	<dl <{if   $qrdate.Status == 14 || $qrdate.Status == 15 }>class="on"<{/if}>>
            	<span>10</span>
            	<dt>付尾款</dt>
                <dd><{if $qrdate.SwkDate!="0000-00-00 00:00:00"}><{$qrdate.SwkDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.SwkDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>
<{else}>

			<dl <{if  $qrdate.Status == 17 }>class="on"<{/if}>>
            	<span>6</span>
            	<dt>发货</dt>
                <dd><{if $qrdate.FhDate!="0000-00-00 00:00:00"}><{$qrdate.FhDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.FhDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>


        	<dl <{if   $qrdate.Status == 7 ||  $qrdate.Status == 13 }>class="on"<{/if}>>
            	<span>7</span>
            	<dt>收货</dt>
                <dd><{if $qrdate.YsDate!="0000-00-00 00:00:00"}><{$qrdate.YsDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.YsDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>

        	<dl <{if   $qrdate.Status == 8 || $qrdate.Status == 9}>class="on"<{/if}>>
            	<span>8</span>
            	<dt>结算</dt>
                <dd><{if $qrdate.JsDate!="0000-00-00 00:00:00"}><{$qrdate.JsDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.JsDate|date_format:"%H:%M:%S"}><{/if}></dd>
            </dl>
			
        	<dl <{if   $qrdate.Status == 14 ||  $qrdate.Status == 15}>class="on"<{/if}>>
            	<span>9</span>
				<dt>付款</dt>
            	<dd><{if $qrdate.SwkDate!="0000-00-00 00:00:00"}><{$qrdate.SwkDate|date_format:"%Y-%m-%d"}><br /><{$qrdate.SwkDate|date_format:"%H:%M:%S"}><{/if}></dd>
                <dd></dd>
            </dl>

<{/if}>
        </ul>
    </div><!--  进度条 end  -->
<{/if}>
<!--UPdate by xiakang for yunqian ended 2015/05/22-->
    <div class="clear"></div>


    <div class="tijiaoBJ mt10 oh bd_d6" style="<{if $ht.Status=='10'}>display:none;<{/if}>">
	
<{if $ht.Status != "21" && $ht.Status != "22" && $zy.Status!=3 && $zy.Status!=4}>
<div id="qrdiv3" align="right" style="display:none ;margin-top:10px"><img src="images/btn_qrfhd_gray.png" style="cursor:pointer;"  width="92" height="26" /></div>
<div id="qrdiv0" style="margin-top:10px" >

<input type="hidden" name="htid" id="htid" value="<{$ht.ID}>">
<input type="hidden" name="ddid" id="ddid" value="<{$params.id}>">

<label class="fr">


<{if $ht.PayType == "2" || $ht.PayType == "1"}>

<!-------收发货-------------------------------------------------------------------------------------------->
			<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && $ht.Status=="12"}>
                <{if $orderstatus > 0}><!--add by hezpeng for 多次发货 started 2016/12/02 orderstatus>0有未确认的发货单，可以再次发货-->
                <img src="images/btn_qrfhd.png" type="button" style="cursor:pointer;" width="92" height="26" onclick="qrfh(this);"/><!--  -->
                <{else}>
                <img src="images/btn_qrfhd_gray.png" style="cursor:pointer;"  width="92" height="26" />
                <{/if}>
			<{/if}>


<{/if}>

<{if $ht.PayType == "3" || $ht.PayType == "4"}>
            <!--UPdate by xiakang for yunqian started 2015/05/30-->
            <{if $ht.contractTye=="0"}>	
                        <{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && $ht.Status=="5"}>
                            <{if $orderstatus > 0}><!--add by hezpeng for 多次发货 started 2016/12/02 orderstatus>0有未确认的发货单，可以再次发货-->
                            <img src="images/btn_qrfhd.png" type="button" style="cursor:pointer;" width="92" height="26" onclick="qrfh(this);"/><!---->
                            <{else}>
                            <img src="images/btn_qrfhd_gray.png" style="cursor:pointer;"  width="92" height="26" />
                            <{/if}>
                        <{/if}>
                        <{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && $ht.IsConfirmSo == "1"&& $ht.IsConfirmBo == "0"}>
                        <img src="images/btn_qrfhd_gray.png" style="cursor:pointer;"  width="92" height="26" />
                        <{/if}>
            <{else}>
                        <{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && ($ht.Status=="17" || $ht.Status=="5")}>
                            <{if $orderstatus > 0}><!--add by hezpeng for 多次发货 started 2016/12/02 orderstatus>0有未确认的发货单，可以再次发货-->
                            <img src="images/btn_qrfhd.png" type="button" style="cursor:pointer;" width="92" height="26" onclick="qrfh(this);"/><!---->
                            <{else}>
                            <img src="images/btn_qrfhd_gray.png" style="cursor:pointer;"  width="92" height="26" />
                            <{/if}>
                        <{/if}>
                        <{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && $ht.IsConfirmSo == "1"&& $ht.IsConfirmBo == "0"}>
                        <img src="images/btn_qrfhd_gray.png" style="cursor:pointer;"  width="92" height="26" />
                        <{/if}>
            <{/if}>
            <!--Updata by xiakang for yunqian ended 2015/05/30-->

			<!--UPdate by tuxw for yunqian started 2015/07/09-->
			<!--<{*if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && $ht.Status=="17"*}>
				<img src="images/btn_qrfhd.png" type="button" style="cursor:pointer;" width="92" height="26" onclick="qrfh(this);"/>
			<{*/if*}><!--  -->
			<!--UPdate by tuxw for yunqian started 2015/07/09-->
            <!--UPdate by xiakang for yunqian started 2015/05/30-->
            <!--{if $buycom.yq_liucheng =="0" && $salecom.yq_liucheng =="0"}-->
                        <!--{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && $ht.Status=="5"}-->
                        <!--img src="images/btn_qrfhd.png" type="button" style="cursor:pointer;" width="92" height="26" onclick="qrfh(this);"/-->
                        <!--{/if}-->
                        <!--{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && $ht.IsConfirmSo == "1"&& $ht.IsConfirmBo == "0"}-->
                        <!--img src="images/btn_qrfhd_gray.png" style="cursor:pointer;"  width="92" height="26" /-->
                        <!--{/if}-->
            <!--{else}-->
                        <!--{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && $ht.Status=="17"}-->
                        <!--img src="images/btn_qrfhd.png" type="button" style="cursor:pointer;" width="92" height="26" onclick="qrfh(this);"/-->
                        <!--{/if}-->
                        <!--{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && $ht.IsConfirmSo == "1"&& $ht.IsConfirmBo == "0"}-->
                        <!--img src="images/btn_qrfhd_gray.png" style="cursor:pointer;"  width="92" height="26" /-->
                        <!--{/if}-->
            <!--{/if}-->
            <!--Updata by xiakang for yunqian ended 2015/05/30-->
<{/if}>
<!-------收发货-------------------------------------------------------------------------------------------->
</label>
</div>
<{/if}>
    </div>
    <div class="clear"></div>
   
	<div class="total bd_d6 bg_f8 mt10 oh"><!--  总计  -->
        <ul>
        	<li class="fl">
            	<span>订单编号：<{$zy.OrderNo}></span>
                <span>订单日期：<{$zy.CreateDate}></span>
				<span>订单状态：<{$htstatus[$ht.Status]}></span>
            </li>
 <!--update by xiakang for yunqian started 2015/06/10-->
	<{if $ht.contractTye=="0"}>	
		<li class="fl" ><{if $ht.Status > 4 && $ht.Status != 21 && $ht.Status != 22 }>
			<{if $ht.Status != "10"}>
			<!--member.php?view=htoff&tid=<{$ht.ID}>  target="_blank"-->
			<a href="javascript:void(0)"  onclick="htoff('<{$ht.ID}>')">
			<img src="img/btn_ddqx.png"></a> <{/if}>
			<a href="javascript:selectht('<{$ht.ID}>')"><img src="img/btn_xzht.png"></a		
			<{/if}>
		</li>
	<{/if}>
    <{if $ht.contractTye=="1"}>	
		<li class="fl" >
		<{if $ht.Status > 4 && $ht.Status != 21 && $ht.Status != 22 }>
			<{if $ht.Status != "10"}>
			<a href="javascript:void(0)"  onclick="htoff('<{$ht.ID}>')">
			<img src="img/btn_ddqx.png"></a> 
			<{/if}>
		  <{if $ht.Status=="5"}>
			<a href="bizorder.php?action=yqckht&id=<{$ht.ID}>"><img src="img/btn_ckht.png"></a>
		  <{else}>
		    <a href="bizorder.php?action=yqckht&id=<{$ht.ID}>"><img src="img/btn_ckht.png"></a>
		    <a href="bizorder.php?action=yqxzht&id=<{$ht.ID}>"><img src="img/btn_xzht.png"></a>
		   <{/if}>
		<{/if}>

			</li>
	<{/if}>
<!--update by xiakang for yunqian ended 2015/06/10-->
            <li class="fr">
                <span style="margin-top: 3px;">订单总数量<b class="num"><{$zy.Tweight}></b>吨，总金额：<b class="price">￥<{$zy.Tmoney}></b>元</span>
            </li>
    	</ul>
    </div><!--   总计 end  -->
    <div class="clear"></div>
	
	<div class="lianjie mt10 oh">
	<a href="bizorder.php?view=orderdetail&id=<{$params.id}>" target="_blank">洽谈订单</a> |
				<a href="bizorder.php?view=payment&id=<{$params.id}>" target="_blank" >订单支付</a>|
		<a href="bizorder.php?view=fhysd&id=<{$params.id}>" target="_blank" class="on">发货单</a> |

        <{if $ht.Status == "10" ||  $ht.Status=="8" || $ht.Status > 13 }><a href="bizorder.php?view=shysd&id=<{$params.id}>" target="_blank">验收单</a> |<{else}><a>验收单</a> |<{/if}>
		
        <{if $ht.Status == "10" ||  $ht.Status=="14" ||  $ht.Status=="15" }><a href="bizorder.php?view=account&id=<{$params.id}>" target="_blank">结算单</a><{else}><a>结算单</a><{/if}>
		
        <{if $ht.Status == "10" || $ht.Status > 6 }><a href="pdf.php?action=send&id=<{$ht.BID}>" target="_blank">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;打印发货单</a><{/if}>


          

    </div>
    <div class="buyerInfo bd_d6 mt10 oh"><!--   买家信息  -->
    	<p>
        	<span class="fl">采购方</span>
           	<span class="fr" style="margin-left: 550px;">供应方</span>
        </p>
        <div class="clear"></div>
        
        <table class="fl" style="width: 599px;">
            <tbody>
            	<tr>
                	<th>公司名称：</th>
                    <td><{$buycp.ComName}></td>
                </tr>

            </tbody>
        </table>
        
        <table class="fr" style="width: 599px;">
        	<tbody>
            	<tr>
    				<th>公司名称：</th>
                    <td><{$sellcp.ComName}></td>
                </tr>

			</tbody>
		</table>
    
    </div><!--  buyerInfo end  -->
    <div class="clear"></div>


<!---------------------------------------------交割信息------------------------------------------------------------------------->
<form name="fhxxform" id="fhxxform" action="bizorder.php?action=fhxx" method="post" >
<{foreach from=$zydetail  item=v key=k name="a"}>
<!--<{if $k>0}><input type="radio" onclick=""><b>发货信息同上</b><{/if}> -->
  <div class="fahuoContact mt10 oh" style="padding-bottom:30px;"><!--  发货联系人信息  -->
     <!--update by xiakang for started 2015/06/02-->
     <{if $ht.contractTye=="0"}>	
        <{if $k=="0"}><input type="hidden" id="firstid" value="<{$v.oid}>"><{/if}>
        <{if $v.Status!='2' && $v.Status!='1'}><!--add by hezpeng for 多次发货 started 2016/12/02 status=2双方已确认的发货单，不能再次修改-->
        <{if $k=="1" &&  $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && ($ht.Status==12 || $ht.Status==5)}><input type="radio" value="1" onclick="changefhxx(<{$v.oid}>)">发货信息同上<{/if}>
        <{/if}>
     <{else}>
        <{if $k=="0"}><input type="hidden" id="firstid" value="<{$v.oid}>"><{/if}>
        <{if $v.Status!='2' && $v.Status!='1'}><!--add by hezpeng for 多次发货 started 2016/12/02 status=2双方已确认的发货单，不能再次修改-->
        <{if $k=="1" &&  $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && ($ht.Status==12 || $ht.Status==17)}><input type="radio" value="1" onclick="changefhxx(<{$v.oid}>)">发货信息同上<{/if}>
        <{/if}>
     <{/if}>
     <!--update by xiakang for ended 2015/06/02-->
        <div class="order1 bd_d6 oh"  id="showfhxx_<{$v.oid}>" style="display:block;" >

            <div class="title sameT1">
            <!--update by xiakang for started 2015/06/02-->
            <{if $ht.contractTye=="0"}>	
                <h1>发货单信息</h1>
                <!--多次发货start-->
                <{if $v.Status!='2' && $v.Status!='1' && $ht.AllowMultiFh=='1'}><!--add by hezpeng for 多次发货 started 2016/12/02 status=2双方已确认的发货单，不能再次修改-->
                    <{if $zy.Status!=3 && $zy.Status!=4}>
                    <{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && ($ht.Status==12 || $ht.Status==5)}><!--Added for quanxian sheding  by hzp started  2014/12/25--><{if $IsModify>0}><!--Added for quanxian sheding  by hzp ended  2014/12/25--><a href=javascript:void(0) onclick='editfhxx_plus(<{$v.oid}>)' id="lxr">展开修改>>></a><{/if}><{/if}><{/if}>
                    <{if $v.IsMain!=1 && (($ht.Status==12 && $ht.PayType==1 )|| ($ht.Status==5 && $ht.PayType==4 )) && $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper}><{if $IsModify>0}><a href="javascript:void()"  onclick="deletedfhd('showfhxx_<{$v.oid}>',this,'<{$v.oid}>')">删除该发货单&nbsp;&nbsp;&nbsp;</a><{/if}><{/if}>
                <{elseif $v.Status=='2' && $ht.AllowMultiFh=='1'}>
                    <a>双方已确认</a>
                <{elseif $v.Status=='1' && $ht.AllowMultiFh=='1'}>
                    <a>发货单已确认</a>
                    <!--多次发货end-->
                <{else}>
                    <!--单次发货start-->
                    <{if $zy.Status!=3 && $zy.Status!=4}>
                    <{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && ($ht.Status==12 || $ht.Status==5)}><!--Added for quanxian sheding  by hzp started  2014/12/25--><{if $IsModify>0}><!--Added for quanxian sheding  by hzp ended  2014/12/25--><a href=javascript:void(0) onclick='editfhxx_plus(<{$v.oid}>)' id="lxr">展开修改>>></a><{/if}><{/if}><{/if}>
                    <{if $v.IsMain!=1 && (($ht.Status==12 && $ht.PayType==1 )|| ($ht.Status==5 && $ht.PayType==4 )) && $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper}><{if $IsModify>0}><a href="javascript:void()"  onclick="deletedfhd('showfhxx_<{$v.oid}>',this,'<{$v.oid}>')">删除该发货单&nbsp;&nbsp;&nbsp;</a><{/if}><{/if}>
                    <!--单次发货end-->
                <{/if}>
            <{else}>
                <h1>发货单信息</h1>
                <!--多次发货start-->
                <{if $v.Status!='2' && $v.Status!='1' && $ht.AllowMultiFh=='1'}><!--add by hezpeng for 多次发货 started 2016/12/02 status=2双方已确认的发货单，不能再次修改-->
                    <{if $zy.Status!=3 && $zy.Status!=4}>
                    <{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && ($ht.Status==12 || $ht.Status==17)}><!--Added for quanxian sheding  by hzp started  2014/12/25--><{if $IsModify>0}><!--Added for quanxian sheding  by hzp ended  2014/12/25--><a href=javascript:void(0) onclick='editfhxx_plus(<{$v.oid}>)' id="lxr">展开修改>>></a><{/if}><{/if}><{/if}>
                    <{if $v.IsMain!=1 && (($ht.Status==12 && $ht.PayType==1 )|| ($ht.Status==17 && $ht.PayType==4 )) && $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper}><{if $IsModify>0}><a href="javascript:void()"  onclick="deletedfhd('showfhxx_<{$v.oid}>',this,'<{$v.oid}>')">删除该发货单&nbsp;&nbsp;&nbsp;</a><{/if}><{/if}>
                <{elseif $v.Status=='2' && $ht.AllowMultiFh=='1'}>
                <a>双方已确认</a>
                <{elseif $v.Status=='1' && $ht.AllowMultiFh=='1'}>
                <a>发货单已确认</a>
                <!--多次发货end-->
                <{else}>
                    <!--单次发货start-->
                    <{if $zy.Status!=3 && $zy.Status!=4}>
                    <{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && ($ht.Status==12 || $ht.Status==17)}><!--Added for quanxian sheding  by hzp started  2014/12/25--><{if $IsModify>0}><!--Added for quanxian sheding  by hzp ended  2014/12/25--><a href=javascript:void(0) onclick='editfhxx_plus(<{$v.oid}>)' id="lxr">展开修改>>></a><{/if}><{/if}><{/if}>
                    <{if $v.IsMain!=1 && (($ht.Status==12 && $ht.PayType==1 )|| ($ht.Status==17 && $ht.PayType==4 )) && $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper}><{if $IsModify>0}><a href="javascript:void()"  onclick="deletedfhd('showfhxx_<{$v.oid}>',this,'<{$v.oid}>')">删除该发货单&nbsp;&nbsp;&nbsp;</a><{/if}><{/if}>
                    <!--单次发货end-->
                <{/if}>
            <{/if}>
            <!--update by xiakang for ended 2015/06/02-->
			</div>
			<div class="clear"></div>
            
            <div class="ordercontent">
                <table> 
					<tr>
						<th>发货单号：</th>
                        <td id="Yscom<{$v.oid}>"><{$v.FhNum}></td>
						<th>发货日期：</th>
                        <td id="Yscom<{$v.oid}>"><{if $v.FhDate==0}><{$nowDate}><{else}><{$v.FhDate}><{/if}></td>
						<th>车船号：</th>
                        <td id="Carnum<{$v.oid}>"><{$v.Carnum}></td>
                       
                        
                    </tr>
					<{if $ht.Delivery!=1}>
                    <tr>
					 <th>运输公司：</th>
                        <td id="Yscom<{$v.oid}>"><{$yscom2[$v.Yscom]}></td>
						<th>联系人：</th>
                        <td id="Fhlxr<{$v.oid}>"><{$v.Fhlxr}></td>
                        <th>联系电话：</th>
                        <td id="Fhphone<{$v.oid}>"><{$v.Fhphone}></td>
                        
                        
                    </tr>
					<{/if}>
					<tr>
					<th>备注：</th>
                     <td colspan="5" id="Fhbz<{$v.oid}>"><{$v.Fhbz}></td>
					</tr>
				</table>             
            </div>

            <div class="ordercontent2" style="clear:both;border-left: 0px solid #d6d6d6;">
				<table style="padding-top:10px;"> <!--合同状态 5  12 确认发货-->
					
					<!--Updated by quanjw for meijiao start 2015/1/20-->
					<!--Added by quanjw for meijiao start 2015/2/3 将radio放到table里面，美化-->
					<tr>
					<td>资源类型</td>
						<td colspan='12'>
						<{foreach from = $ZiYuType item=type_v key=type_k}>
								<input name="bigpz" type="radio" value="<{$type_k}>" id="<{$v.oid}><{$type_k}>" onclick="show_fh('<{$v.oid}><{$type_k}>');" <{if $type_k==0}>checked<{/if}> ><{$type_v}>
					 	<{/foreach}>
					<!--Added by shizg for uploadpicture started 2016/10/26-->
						<div align="right">
						<input type="button" name="piclistbutton" value="发货照片列表" onclick="window.open('bizorder.php?view=shpicturelists&Oid=<{$v.oid}>&ShorFh=2&xiugai=0&Status=<{$v.Status}>','','scrollbars=no,width=650,height=550,left=800,top=100')">
						<input type="button" name="piclistbutton" value="收货照片列表" onclick="window.open('bizorder.php?view=shpicturelists&Oid=<{$v.oid}>&ShorFh=1&xiugai=0&Status=<{$v.Status}>','','scrollbars=no,width=650,height=550,left=800,top=100')">
						</div >
					<!--Added by shizg for uploadpicture ended 2016/10/26-->

						</td>
					</tr>
					<!--Added by quanjw for meijiao end 2015/2/3-->
					<tr class="tr_Vid_<{$MEITAN_VID}>">
						<td>品名</td>
						<td>挥发分</td>
						<td>灰分</td>
						<td>全硫分</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
				
					<tr class="tr_Vid_<{$JIAOTAN_VID}>">
						<td>品名</td>
						<td>灰分</td>
						<td>全硫分</td>
						<td>CSR</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
					
					<tr class="tr_Vid_0">
						<td>品名</td>
						<td>材质</td>
						<td>规格</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
					<tr class="tr_Vid_<{$SHUINI_VID}>">
						<td>品名</td>
						<td>规格</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
					<!--Added by quanjw for jinshuzhipin start 2015/2/26-->
					<tr class="tr_Vid_<{$JINSHU_VID}>">
						<td>品名</td>
						<td>规格</td>
						<td>强度</td>
						<td>锌层重量</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
                    <tr class="tr_Vid_<{$HW_VID}>">
						<td>品名</td>
						<td>材质</td>
						<td>规格</td>
                        <td>抗拉</td>
                        <td>屈服</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
					</tr>
				<!--Updated by quanjw for meijiao end 2015/1/20-->
                <{foreach from=$pzzlist[$v.oid] item=v2 key=k2}>
					<!--Updated by quanjw for jinshuzhipin start 2015/2/26-->
                    <!--<tr class="<{if $v2.Vid== $MEITAN_VID}>tr_Vid_<{$v2.Vid}><{else}><{if $v2.Vid== $JIAOTAN_VID}>tr_Vid_<{$v2.Vid}><{else}><{if $v2.Vid== $SHUINI_VID}>tr_Vid_<{$v2.Vid}><{else}>tr_Vid_0<{/if}><{/if}><{/if}>">-->
                    <tr class="<{if $v2.Vid== $MEITAN_VID || $v2.Vid== $JIAOTAN_VID || $v2.Vid== $SHUINI_VID || $v2.Vid== $JINSHU_VID || $v2.Vid== $HW_VID}>tr_Vid_<{$v2.Vid}><{else}>tr_Vid_0<{/if}>">
                    <!--Updated by quanjw for jinshuzhipin end 2015/2/26-->
                        <td id=""><{$v2.pinming}></td>
                        <!--Updated by quanjw for meijiao start 2015/1/14-->
                        <{if $v2.Vid== $MEITAN_VID}>
                        <td id=""><{$v2.caizhi}></td>
                        <td id="" ><{$v2.guige}></td>
                         <td id="" ><{$v2.cd}></td> 
                        <{elseif $v2.Vid== $JIAOTAN_VID}> 
                        <td id="" ><{$v2.guige}></td> 
                        <td id=""><{$v2.caizhi}></td>
                        <td id="" ><{$v2.cd}></td>
                        <{elseif $v2.Vid== $SHUINI_VID}>
                        <td id="" ><{$v2.guige}></td> 
                        <{elseif $v2.Vid== $JINSHU_VID}>
                        <td id="" ><{$v2.guige}></td>
                        <td id="" ><{$v2.strength}></td>
                        <td id="" ><{$v2.xincengWeight}></td>
                        <{elseif $v2.Vid== $HW_VID}>
                        <td id=""><{$v2.caizhi}></td>
                        <td id="" ><{$v2.guige}></td>
                        <td id="" ><{$v2.yongtu}></td>
                        <td id="" ><{$v2.strength}></td>
                        <{else}>
                        <td id=""><{$v2.caizhi}></td>
                        <td id="" ><{$v2.guige}></td> 
                        <{/if}>
                         <!--Updated by quanjw for meijiao end 2015/1/14-->
                       
                        <td id=""><{if $v2.Factory==''}><{$v2.OriginCode}><{else}><{$v2.Factory}><{/if}></td>
                        <td id=""><{$v2.Fhjz}></td>
                        <td id=""><{$v2.Fhjs}></td>
						<!-- Updated for fhysd by Zhu Dahua started  2015/09/14-->
                        <td id=""><{if $v2.Fhsl == $v2.Shsl}><{$v2.Fhsl}><{else}><font color="red"><{$v2.Fhsl}></font><{/if}></td>
						<!-- Updated for fhysd by Zhu Dahua ended  2015/09/14-->
						<td id=""><{if $v2.MfgDate == '0000-00-00'}><{else}><{$v2.MfgDate}><{/if}></td>
						<td id=""><{$v2.zbs}></td>
						<td id=""><{$v2.FhNum}></td>
                    </tr>
                <{/foreach}>
					<!--<tr><td colspan=10 style="text-align:right;float;right;padding-right:30px;"><b>小计：</b><b class="price" id="xiaoj2<{$v.oid}>"><{$v.Fhsl}></b>吨</td></tr>-->
					<!--Updated by quanjw for meijiao start 2015/1/20 小计 td对不上 colspan 10改成11-->
					<tr ><td colspan=11 style="text-align:right;float;right;padding-right:30px;"><b>小计：</b><b class="price" id="xiaoj2<{$v.oid}>"><{$v.Fhsl}></b>吨</td></tr>
					<!--Updated by quanjw for meijao start 2015/1/20-->
                </table>
            </div>
    	</div>
		
        <div class="order1 bd_d6 oh"  id="fhxxdiv_<{$v.oid}>" style="display:none" >
            <div class="title sameT1">
            	<!--Updated by quanjw for jinshuzhipin start 2015/3/10-->
                <!--<h1>发货单信息</h1><a href=javascript:void(0) onclick='closefhxx(<{$v.oid}>)' id="lxr">关闭</a>-->
				<h1>发货单信息</h1><a href=javascript:void(0) onclick='closefhxx_plus(<{$v.oid}>)' id="lxr">关闭</a>
				<!--Updated by quanjw for jinshuzhipin end 2015/3/10-->
				<!--<a href="javascript:void(0);" onclick="fhxxPay(<{$v.oid}>);">保存&nbsp;&nbsp;&nbsp;</a>-->
			</div>
			<div class="clear"></div>
    
				<input type="hidden" id="Delivery" value="<{$ht.Delivery}>">
				<table class="writeContactInfo" width="1000"> 
                    <tr width="100%">
						<td  class="td1">发货单号：</td>
                        <td id=""><input type="text" name="FhNum<{$v.oid}>" id="FhNum2<{$v.oid}>" readonly value="<{$v.FhNum}>"></td>	
						

						<td class="td1" width="15px">车船号：</td>
                        <td width="15px"><input type="text" name="Carnum<{$v.oid}>" id="Carnum2<{$v.oid}>" value="<{$v.Carnum}>"></td>

						<td class="td2" width="15px">发货日期：</td>
                        <td width="15px"><input  onclick="WdatePicker()" class="Wdate" type="text" style="text-align:center;padding-top:5px;" name="FhDate<{$v.oid}>" <{if $v.FhDate==0}>value="<{$nowDate}>"<{else}>value="<{$v.FhDate}>"<{/if}>></td>

						

						
                    </tr>
				</table>  


            <div class="contactList">


				
                <table  <{if $ht.Delivery==1}>style="display:none"<{/if}>>
				<{if $ht.Delivery==1}>
					<input type="hidden" name="Yscom<{$v.oid}>" id="Yscom2<{$v.oid}>" value="">
					<input type="hidden" name="Fhlxr<{$v.oid}>" value="">
					<input type="hidden" name="Fhphone<{$v.oid}>" value="">
				 <{else}>
					<input type="hidden" name="Yscom<{$v.oid}>" id="Yscom2<{$v.oid}>" value="<{$v.Yscom}>">
					<input type="hidden" name="Fhlxr<{$v.oid}>" id="Fhlxr2<{$v.oid}>" value="<{$v.Fhlxr}>">
					<input type="hidden" name="Fhphone<{$v.oid}>" id="Fhphone2<{$v.oid}>" value="<{$v.Fhphone}>">
				 <{/if}>

						<tr>
							<td class="td1"  colspan=2 style="text-align:center;">运输公司</td>
                            <td class="td2" colspan=2 style="text-align:center;">联系人</td>
                            <td class="td3" colspan=2 style="text-align:center;">联系电话</td>
     
						</tr>
                        <{foreach name=hot from=$yscom item=v3}>
                       	<tr id="contact<{$v.ID}>">
                            <td class="td1" colspan=2><center><label><input name="IsDefault<{$v.oid}>" type="radio"   value="<{$v3.ID}>"  <{if $v.Yscom==$v3.ID }>checked<{/if}>/>&nbsp;&nbsp;<strong><{$v3.TransName}></strong></label></center></td>
                            <td class="td2" id="IsDefaultTm<{$v3.ID}>" colspan=2 ><{$v3.TransMan}></td>
                            <td class="td3" id="IsDefaultTp<{$v3.ID}>" colspan=2><{$v3.TransPhone}></td>
                        </tr>
                        <{/foreach}>
						<{if $nullyscom=="1"}><tr><td colspan=6><font  color="red" >您当前没有添加运输公司，请添加运输公司</font>&nbsp;&nbsp;<a href="user.php?view=addtrans" target="_blank">添加运输公司</a></td></tr><{/if}>
				</table>
		  </div>
				<table class="writeContactInfo" width="1000"> 
                    <tr width="100%">
                        <td >备注：</td>
                        <td colspan=5><input type="text" name="Fhbz<{$v.oid}>" size=50 value="<{$v.Fhbz}>"></td>
						
                    </tr>
				</table>             
            

        <div class="ordercontent2" style="clear:both;border-left: 0px solid #d6d6d6;">
				<table style="padding-top:10px;" id="addtr<{$v.oid}>"> <!--合同状态 5  12 确认发货-->
					<!--Updated by quanjw for meijiao start 2015/1/15-->
					<!--Added by quanjw for meijiao start 2015/2/3 将radio放到table里面，美化-->
					<tr>
					<td>资源类型</td>
						<td colspan='12'>
						<{foreach from = $ZiYuType item=type_v key=type_k}>
								<input name="bigpz2<{$v.oid}>" type="radio" value="<{$type_k}>" id="<{$v.oid}>&&<{$type_k}>" onclick="show_fh2('<{$v.oid}>&&<{$type_k}>');" <{if $type_k==0}>checked<{/if}> ><{$type_v}>
					 	<{/foreach}>

					<!--Added by shizg for uploadpicture started 2016/10/26-->
						<div align="right">
						<{if $v.Status!=1&&$v.Status!=2}>
						<input type="button" name="picbutton" value="上传照片" onclick="window.open('bizorder.php?view=shuploadpicture&Oid=<{$v.oid}>&ShorFh=2','','scrollbars=no,width=650,height=350,left=800,top=100')">
						<{/if}>
						<input type="button" name="piclistbutton" value="发货照片列表" onclick="window.open('bizorder.php?view=shpicturelists&Oid=<{$v.oid}>&ShorFh=2&xiugai=1&Status=<{$v.Status}>','','scrollbars=no,width=650,height=550,left=800,top=100')">
						</div >
					<!--Added by shizg for uploadpicture ended 2016/10/26-->
						</td>
					</tr>
					<!--Added by quanjw for meijiao end 2015/2/3-->
					<tr class="tr_Vid_<{$MEITAN_VID}><{$v.oid}>">
						<td>选择</td>
						<td>品名</td>
						<td>挥发分</td>
						<td>灰分</td>
						<td>全硫分</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
						<td>操作</td>
					</tr>
					<tr class="tr_Vid_<{$JIAOTAN_VID}><{$v.oid}>">
						<td>选择</td>
						<td>品名</td>
						<td>灰分</td>
						<td>全硫分</td>
						<td>CSR</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
						<td>操作</td>
					</tr>
					<tr class="tr_Vid_0<{$v.oid}>">
						<td>选择</td>
						<td>品名</td>
						<td>材质</td>
						<td>规格</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
						<td>操作</td>
					</tr>	
					<tr class="tr_Vid_<{$SHUINI_VID}><{$v.oid}>">
						<td>选择</td>
						<td>品名</td>
						<td>规格</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
						<td>操作</td>
					</tr>
					<!--Added by quanjw for jinshuzhipin start 2015/2/26-->
					<tr class="tr_Vid_<{$JINSHU_VID}><{$v.oid}>">
						<td>选择</td>
						<td>品名</td>
						<td>规格</td>
						<td>强度</td>
						<td>锌层重量</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
						<td>操作</td>
					</tr>	
                    <tr class="tr_Vid_<{$HW_VID}><{$v.oid}>">
						<td>选择</td>
						<td>品名</td>
						<td>材质</td>
						<td>规格</td>
                        <td>抗拉</td>
                        <td>屈服</td>
						<td>产地（厂家）</td>
						<td>件重</td>
						<td>件数</td>
						<td>数量</td>
						<td>生产日期</td>
						<td>质保书</td>
						<td>货号</td>
						<td>操作</td>
					</tr>
				<!--Updated by quanjw for meijiao end 2015/1/15-->
            <div id="addtr2<{$v.oid}>">
            <{foreach from=$pzzlist2[$v.oid] item=v2 key=k2}>
						<!--Updated by quanjw for jinshuzhipin start 2015/2/26-->
						<!--<tr id='dt<{$v2.ID}>' class="<{if $v2.Vid== $MEITAN_VID}>tr_Vid_<{$v2.Vid}><{else}><{if $v2.Vid== $JIAOTAN_VID}>tr_Vid_<{$v2.Vid}><{else}><{if $v2.Vid== $SHUINI_VID}>tr_Vid_<{$v2.Vid}><{else}>tr_Vid_0<{/if}><{/if}><{/if}>">-->
                        <tr id='dt<{$v2.ID}>' class="<{if $v2.Vid== $MEITAN_VID || $v2.Vid== $JIAOTAN_VID || $v2.Vid== $SHUINI_VID || $v2.Vid== $JINSHU_VID || $v2.Vid== $HW_VID}>tr_Vid_<{$v2.Vid}><{$v.oid}><{else}>tr_Vid_0<{$v.oid}><{/if}>">
                        <!--Updated by quanjw for jinshuzhipin end 2015/2/26-->
                        <td id=""><input name="Status<{$v2.ID}>" id="Status2<{$v2.ID}>" type="checkbox" value="<{if $v2.Status2==1}>1<{else}>0<{/if}>"  onclick="javascript:checkoid('<{$v.oid}>');this.value=this.value==0?1:0;yunsuan2('<{$v2.ID}>','<{$v.Ddid}>');"<{if $v2.Status2==1}>checked<{/if}>></td>
                        <td id=""><{$v2.pinming}></td>
                        <!--Updated by quanjw for meijiao start 2015/1/14-->
                        <{if $v2.Vid== $MEITAN_VID}>
                        <td id=""><{$v2.caizhi}></td>
                        <td id="" ><{$v2.guige}></td> 
                         <td id="" ><{$v2.cd}></td>
                        <{elseif $v2.Vid== $JIAOTAN_VID}>
                        <td id="" ><{$v2.guige}></td> 
                        <td id=""><{$v2.caizhi}></td>
                        <td id="" ><{$v2.cd}></td>
                        <{elseif $v2.Vid== $SHUINI_VID}>
                         <td id="" ><{$v2.guige}></td> 
                        <{elseif $v2.Vid== $JINSHU_VID}>
                        <td id="" ><{$v2.guige}></td>
                        <td id="" ><{$v2.strength}></td>
                        <td id="" ><{$v2.xincengWeight}></td>
                        <{elseif $v2.Vid== $HW_VID}>
                        <td id=""><{$v2.caizhi}></td>
                        <td id="" ><{$v2.guige}></td>
                        <td id="" ><{$v2.yongtu}></td>
                        <td id="" ><{$v2.strength}></td>
                        <{else}>
                        <td id=""><{$v2.caizhi}></td>
                        <td id="" ><{$v2.guige}></td>
                         <{/if}>
                         <!--Updated by quanjw for meijiao end 2015/1/14-->
                       
                        <td><{if $v2.Factory==''}><{$v2.OriginCode}><{else}><{$v2.Factory}><{/if}></td>
                        <td><input type="text" size="6" name="Fhjz<{$v2.ID}>" id="Fhjz2<{$v2.ID}>" value="<{if $v2.Fhjz=='0'}><{else}><{$v2.Fhjz}><{/if}>" onfocus="this.focused=true;this.select();" onmouseup="if(this.focused){this.focused=false;return false;}" onblur="yunsuan('<{$v2.ID}>','<{$v.Ddid}>')"></td>
                        <td><input type="text" size="6" name="Fhjs<{$v2.ID}>" id="Fhjs2<{$v2.ID}>"  value="<{if $v2.Fhjs=='0'}><{else}><{$v2.Fhjs}><{/if}>" onfocus="this.focused=true;this.select();" onmouseup="if(this.focused){this.focused=false;return false;}"  onblur="yunsuan('<{$v2.ID}>','<{$v.Ddid}>')"></td>
                        <td><input type="text" size="6" name="Fhsl<{$v2.ID}>" id="Fhsl2<{$v2.ID}>" value="<{$v2.Fhsl}>" onfocus="this.focused=true;this.select();" onmouseup="if(this.focused){this.focused=false;return false;}" onblur="checkSL('<{$v2.ID}>','<{$v.Ddid}>');"></td>
						<td><input type="text" size="8" name="MfgDate<{$v2.ID}>" id="MfgDate2<{$v2.ID}>"  <{if $v2.MfgDate == '0000-00-00'}>value=""<{else}> value="<{$v2.MfgDate}>"<{/if}> style="border:solid 1px #8eade0; width:90px; height:20px;" onclick="WdatePicker()" class="Wdate"></td>
						<td><{$v2.zbs}></td>
						<td><input type="text" size="8" name="FhNum<{$v2.ID}>" id="FhNum2<{$v2.ID}>" value="<{$v2.FhNum}>"></td>
						<td>
						<{if $v2.IsMain=="1"}><{if $IsModify>0}><a style="cursor:pointer;"  onclick="add('addtr<{$v.oid}>',<{$v.oid}>,<{$v2.ID}>)">添加</a><{/if}><{/if}>
						<{if $v2.IsMain=="0"}><{if $IsModify>0}><a style="cursor:pointer;" onclick="deleteod('addtr<{$v.oid}>',this,'<{$v2.ID}>')">删除</a><{/if}><{/if}>
						</td>
					<!--<{if $k2!="0"}>	<a style="cursor:pointer;" onclick="deleteod('addtr<{$v.oid}>',this,'<{$v2.ID}>')">删除</a><{/if}></td>
                    --></tr>
            <{/foreach}>
            </div>
					<tr style="display:none"><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td>
					</tr>


                    <input type="hidden" name="fhflag" id="fhflag" value="<{$v.oid}>">
                </table>
            	<div class="contactList2">
				    <!--<input type="button" value="添加"  id="b1<{$v.oid}>" onclick="add('addtr<{$v.oid}>',<{$v.oid}>)">-->
				    <input type="checkbox" onclick="checkfhdall('<{$v.oid}>')" name="checkbox<{$v.oid}>" id="checkbox<{$v.oid}>" style="margin-left:10px;">	全选
				    <img src="images/btn_bc.png" type="button" style="cursor: pointer;padding-left:50px;margin:5px 0 5px 0"  onclick="fhxxPay2(<{$v.oid}>);" />
                    <font  style="float:right;padding-right:30px;"><b>小计：</b><b class="price" id="xiaoj<{$v.oid}>"><{$v.Fhsl}></b>吨</font>

				
				</div>
				
          </div>
			
    	</div>
</div> 
<!--Update by xiakang for yunqian started 2015/06/02-->
<{if $ht.contractTye=="0"}>	
  <{if  (($ht.Status==12 && $ht.PayType==1 )|| ($ht.Status==5 && $ht.PayType==4 ))   && $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper }>
  <div style="width:100%;" >
  <div align="right" style="margin-right:10px;margin-bottom:10px;">
  <{if $IsModify>0}>
  <a  href="bizorder.php?action=fhysd&&oid=<{$v.oid}>&&Did=<{$v.Did}>">
  <!--<font style="font-size:15px">添加发货单</font>-->
  <img src="img/btn_tjfhd.png">
  </a>
  <{/if}>
  </div>
  </div>
  <{/if}>
<{else}>
  <{if  (($ht.Status==12 && $ht.PayType==1 )|| ($ht.Status==17 && $ht.PayType==4 ))   && $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper }>
  <div style="width:100%;" >
  <div align="right" style="margin-right:10px;margin-bottom:10px;">
  <{if $IsModify>0}>
  <a  href="bizorder.php?action=fhysd&&oid=<{$v.oid}>&&Did=<{$v.Did}>">
  <!--<font style="font-size:15px">添加发货单</font>-->
  <img src="img/btn_tjfhd.png">
  </a>
  <{/if}>
  </div>
  </div>
  <{/if}>
 <{/if}>
<!--Update by xiakang for yunqian ened 2015/06/02-->

    <script>
	/*added by hezp started 2016/09/18*/
	function checkfhdall(oid){
		//alert(oid);
		jQuery.ajax({
            url: 'bizorder.php?action=select_oid',
            data: "oid="+oid, // 获取数据
            type: 'POST', // 设置请求类型为 ‘POST’，默认为 ‘GET’
            dataType:"json",
			success: function(data) {
				
				var datainfo = data.datainfo;
				for(var i=0; i<datainfo.length; i++)
				{
					//alert(datainfo[i]);
					if(document.getElementById("checkbox"+oid).checked){
						//alert(1);
						document.getElementById("Status2"+datainfo[i]).value=1;
						document.getElementById("Status2"+datainfo[i]).checked=true;
					}else{
						//alert(2);
						document.getElementById("Status2"+datainfo[i]).value=0;
						document.getElementById("Status2"+datainfo[i]).checked=false;
					}

				}


            }
        });
		
	}
	function checkoid(oid){
		//alert(oid);
		jQuery.ajax({
            url: 'bizorder.php?action=select_oid',
            data: "oid="+oid, // 获取数据
            type: 'POST', // 设置请求类型为 ‘POST’，默认为 ‘GET’
            dataType:"json",
			success: function(data) {
				
				var datainfo = data.datainfo;
				var status = '0';
				for(var i=0; i<datainfo.length; i++)
				{
					//alert(datainfo[i]);
					if(!document.getElementById("Status2"+datainfo[i]).checked){
						//alert(1);
						status='1';
						break;
					}

				}
				if(status=='1'){
					document.getElementById("checkbox"+oid).checked=false;
				}else{
					document.getElementById("checkbox"+oid).checked=true;
				}


            }
        });
	}
	/*added by hezp ended 2016/09/18*/


$(document).ready(function(){
	//added by hezpeng started 2016/09/20
	
	checkoid(<{$v.oid}>);
	
	//added by hezpeng ended 2016/09/20

	$("input[name=IsDefault<{$v.oid}>]").click(function(){
		var id=$("input[name=IsDefault<{$v.oid}>]:checked").val();	

		jQuery.ajax({
            url: 'bizorder.php?view=yscomcontact',
            data: "id="+id+"&time="+new Date().getTime(), // 从表单中获取数据
            type: 'POST', // 设置请求类型为 ‘POST’，默认为 ‘GET’
            dataType:"json",
			success: function(data) {
				
				if(parseInt(data.error)){
					alert(data.errorinfo);
				}
				else{
 					$("input[name='Yscom<{$v.oid}>']").val(data.datainfo.ID);
					$("input[name='Fhlxr<{$v.oid}>']").val(data.datainfo.TransMan);
					$("input[name='Fhphone<{$v.oid}>']").val(data.datainfo.TransPhone);
				}
            }
        });
	
	});
});

//updated by shizg for 资源选择问题 started 2017/02/27
  	function show_fh2(str){

		if(str!=null&&str!=''){
			var voids=str.split("&&");
			var oid = voids[0];
			if(voids[1]!="999"){
				var boxes = document.getElementsByName("bigpz2"+oid);
				for(i=0;i<boxes.length;i++){
					// $("#"+boxes[i].id).removeAttr("checked"); 
					$('[id="'+boxes[i].id+'"]').removeAttr("checked"); 
				}
					// $("#"+str).attr("checked", "checked");
					$('[id="'+str+'"]').attr("checked", "checked");
			}
		}
		var rdo2=$("input[name='bigpz2" +oid+"']:checked").val();

  		//Updated by quanjw for jinshuzhipin start 2015/2/26
  		$(".tr_Vid_<{$MEITAN_VID}>"+oid).hide();
 		$(".tr_Vid_<{$JIAOTAN_VID}>"+oid).hide();
 		$(".tr_Vid_0"+oid).hide();
 		$(".tr_Vid_<{$SHUINI_VID}>"+oid).hide();
  		$(".tr_Vid_<{$JINSHU_VID}>"+oid).hide();
        $(".tr_Vid_<{$HW_VID}>"+oid).hide();
  		switch(rdo2){
  			case "<{$MEITAN_VID}>":
  				$(".tr_Vid_<{$MEITAN_VID}>"+oid).show();
  				break;
  			case "<{$JIAOTAN_VID}>":
  				$(".tr_Vid_<{$JIAOTAN_VID}>"+oid).show();
  				break;
  			case "<{$SHUINI_VID}>":
  				$(".tr_Vid_<{$SHUINI_VID}>"+oid).show();
  				break;
  			case "<{$JINSHU_VID}>":
  				$(".tr_Vid_<{$JINSHU_VID}>"+oid).show();
  				break;
            case "<{$HW_VID}>":
				$(".tr_Vid_<{$HW_VID}>"+oid).show();
				break;
  			default:
  				$(".tr_Vid_0"+oid).show();
  		}
  		//Updated by quanjw for jinshuzhipin end 2015/2/26
  	}
//updated by shizg for 资源选择问题 ended 2017/02/27
      </script>



  <p><{/foreach}></p>
  <p><span class="fahuoContact mt10 oh" style="padding-bottom:30px;"></p>

 
  </span> </p>
  <div class="tijiaoBJ mt10 oh bd_d6">
<label class="fr"  style="padding-right:30px;">
<b>合计：</b><b class="price" id="allj"><{$Allsl}></b>吨
</label>
</div>
</form>



 <div id="contentjs">

<script> //alert("ss");

function checkSL(item,oid){
	checkfhxx(<{$v.oid}>);
	var sl=document.getElementById("Fhsl2"+item).value;
	if(sl<0){
		alert("请输入正确的数量！");
		document.getElementById("Fhsl2"+item).value=0;
		return;
		}
		yunsuan(item,oid);
	}

function yunsuan(item,oid){
	
	//alert(item);
var xiaoj=0;
var xiaoj2=0;
var allj=0;	





if(document.getElementById("Status2"+item).checked== true){}else{return false;}


	var jz=document.getElementById("Fhjz2"+item).value;

	var js=document.getElementById("Fhjs2"+item).value;
	var sl=(jz*10)*(js*10)/100;
	if(jz<0||js<0||sl<0)
	{	
		if(jz<0){
		/*alert("请输入正确的件重！");	
		document.getElementById("Fhjz2"+item).value=0;
		return;*/
		}
		if(js<0){
		alert("请输入正确的件数！");	
		document.getElementById("Fhjs2"+item).value=0;
		return;
		}
		
	}
	//var sl=accMul(jz,js);
	


<{foreach from=$zydetail  item=v key=k }>

<{foreach from=$pzzlist2[$v.oid] item=v2 key=k2}>



if(oid=="<{$v.Ddid}>"){
var zz="<{$v2.BuyQuantity}>";
}

if(document.getElementById("Status2<{$v2.ID}>").checked== true){

	//alert(zz);return false;
	var jz2=document.getElementById("Fhjz2<{$v2.ID}>").value;

	var js2=document.getElementById("Fhjs2<{$v2.ID}>").value;

	var sl2=document.getElementById("Fhsl2<{$v2.ID}>").value;
		if(sl2 ==0)
		{
			 xiaoj = xiaoj + jz2*js2;
			 xiaoj2 = xiaoj2 + jz3*js3;
		}
		else
		{
			 xiaoj = xiaoj + parseFloat(sl2);
			 allj = parseFloat(allj) + parseFloat(sl2);
		}
		if(oid=="<{$v.Ddid}>"){
			var jz3=document.getElementById("Fhjz2<{$v2.ID}>").value;

			var js3=document.getElementById("Fhjs2<{$v2.ID}>").value;

			var sl3=document.getElementById("Fhsl2<{$v2.ID}>").value;
			if(sl3 ==0)
		{
			 xiaoj2 = xiaoj2 + jz3*js3;
			 //allj = allj + jz2*js2;
		}
		else
		{
			 xiaoj2 = xiaoj2 +parseFloat(sl3);
		
			 //allj = parseFloat(allj) + parseFloat(sl2);
		}
	
	
	/*
	if(js2=="0" && jz2=="0")
	{
		var allj = parseFloat(allj) + parseFloat(sl2);
	}
	else
	{
		var allj = allj + jz2*js2;
	}
*/
  }
}


//alert("3");
<{/foreach}>
//alert(xiaoj2);
//updated by qunajw for sl start 2015/1/8
document.getElementById("xiaoj<{$v.oid}>").innerHTML= parseFloat(xiaoj).toFixed(4);
document.getElementById("xiaoj2<{$v.oid}>").innerHTML= parseFloat(xiaoj).toFixed(4);
//updated by qunajw for sl snd 2015/1/8
xiaoj = 0;
jz2 = 0;
js2 = 0;
sl2 = 0;
<{/foreach}>


//alert(xiaoj2);return false;

if(xiaoj2<(zz*0.95) && jz!="0" && js!="0"){
	//alert("发货总数量少于该订单资源总量的95%,请重新核对填写！");
	//document.getElementById("FhStatus2").innerHTML="<font color=red size='5px'>发货总数量少于该订单资源总量的95%,请重新核对填写！</font>";
	document.getElementById("qrdiv0").style.display="none";
	document.getElementById("qrdiv").style.display="none";
	document.getElementById("qrdiv2").style.display="";
	document.getElementById("qrdiv3").style.display="";

//	document.getElementById("Fhsl2"+item).value="";
//	return false;
}else if(xiaoj2>(zz*1.05) && jz!="0" && js!="0"){
	//document.getElementById("FhStatus2").innerHTML="<font color=red size='5px'>发货总数量大于该订单资源总量的105%,请重新核对填写！</font>";
	document.getElementById("qrdiv0").style.display="none";
	document.getElementById("qrdiv").style.display="none";
	document.getElementById("qrdiv2").style.display="";
	document.getElementById("qrdiv3").style.display="";
	//document.getElementById("Fhsl2"+item).value="";
	//return false;	
}else{
	document.getElementById("qrdiv0").style.display="";
	document.getElementById("qrdiv").style.display="";
	document.getElementById("qrdiv2").style.display="none";
	document.getElementById("qrdiv3").style.display="none";
}
	

//	document.getElementById("Fhjs2"+item).value="";
//	document.getElementById("Fhjz2"+item).value="";
if(document.getElementById("Fhsl2"+item).value==0){
document.getElementById("Fhsl2"+item).value= sl;
}

//updated by quanjw for sl start 2015/1/8
//document.getElementById("allj").innerHTML= parseFloat(allj).toFixed(3);
document.getElementById("allj").innerHTML= parseFloat(allj).toFixed(4);
//updated by quanjw for sl end 2015/1/8
	//yunsuan(item,oid);



 

//alert("xiaoj2"+xiaoj2);
//alert("xiaoj2>zz"+xiaoj2>zz);


	
	

//alert(sl);
//"jisuan"+oid();
}
function deleteod(id,row,oid){

	if(confirm('请确认是否删除！')){
	jQuery.ajax({
            url: 'member.php?action=ajaxaddorder',
            data: "id="+oid + "&type=2&time="+new Date().getTime(), // 从表单中获取数据
            type: 'POST', // 设置请求类型为 ‘POST’，默认为 ‘GET’
           // dataType:"json",
			success: function(data) {
				//alert(data);
			//	if(data.data==1){
					//$("#dt"+id).hide();
					$("#dt"+oid).remove();
			//	}
			location.reload();
            }
        });
	}

}
function deletedfhd(id,row,oid){

	if(confirm('请确认是否删除！')){
	jQuery.ajax({
            url: 'member.php?action=ajaxaddorder',
            data: "id="+oid + "&type=5&time="+new Date().getTime(), // 从表单中获取数据
            type: 'POST', // 设置请求类型为 ‘POST’，默认为 ‘GET’
           // dataType:"json",
			success: function(data) {
				//alert(data);
			//	if(data.data==1){
					//$("#dt"+id).hide();
					$("#showfhxx_"+oid).remove();
			//	}
			location.reload();
            }
        });
	}

}



//yunsuan(158);
</script>
</div>
<script>
function add(id,oid,oddid){

 var oTr = document.getElementById(id).rows[1];
	

 var newTr = oTr.cloneNode(true);

	var param = "action=ajaxaddorder&id="+oid +"&odid="+ oddid + "&time=" + new Date().getTime();
	
	var ajax = new Ajax("member.php",setdata2,param);
}
function setdata2(result){

//alert(result);

if(result=="1"){
	alert("该资源发货数量已达到订购数量");
}

var responseText = decodeURIComponent(result).toString();
responseText = responseText.replace(/{/g,'<').replace(/}/g,'>');
var reArrayz = responseText.split('|-|');
//alert(reArrayz[2]);
$("#"+reArrayz[1]+" tbody").append(reArrayz[0]);
	//location.reload();
 //document.getElementById(reArrayz[1]).getElementsByTagName("tbody")[0].appendChild(reArrayz[1]);

 //document.getElementById(reArrayz[1]).innerHTML=result;	
//xmlhttp.responseText
 $("#contentjs").load("bizorder.php?view=ajaxfhd&id=<{$params.id}> #contentjs","");
 yunsuan(reArrayz[2],reArrayz[3]);
}



function checkfhxx(id){
	//return id;
<{foreach from=$zydetail  item=v key=k }>
//return "<{$v.oid}>";
if("<{$v.oid}>"==id){
<{foreach from=$pzzlist2[$v.oid] item=v2 key=k2}>

if(document.getElementById("Status2<{$v2.ID}>").checked== true){

	var sl2=document.getElementById("Fhsl2<{$v2.ID}>").value;

	if(parseFloat(sl2)==0)
	{
		var tempST="Status2"+"<{$v2.ID}>";
		$("#"+tempST).attr("checked",false);
		document.getElementById(tempST).value=0;
	}
}

<{/foreach}>
}

<{/foreach}>


}



function fhxx(id){

var num1=0;
var num2=0;
var num3=0;
<{foreach from=$zydetail  item=v key=k }>
//return "<{$v.oid}>";
if("<{$v.oid}>"==id){
<{foreach from=$pzzlist2[$v.oid] item=v2 key=k2}>

if(document.getElementById("Status2<{$v2.ID}>").checked== true){

	var jz2=document.getElementById("Fhjz2<{$v2.ID}>").value;

	var js2=document.getElementById("Fhjs2<{$v2.ID}>").value;

	var sl2=document.getElementById("Fhsl2<{$v2.ID}>").value;

	//if(parseInt(jz2)<=0 ||	jz2==''){num1++;}
	if(parseInt(js2)<=0 ||	js2==''){num2++;}
	if(parseFloat(sl2)<=0 ||　sl2==''){num3++;}
	
}

<{/foreach}>
}

<{/foreach}>


//return num2;
if(parseInt(num1)>0){return "1";exit;}
if(parseInt(num2)>0){return "2";exit;}
if(parseInt(num3)>0){return "3";exit;}
}

</script>

<div id="FhStatus2" align="right" ></div>


    <div class="tijiaoBJ mt10 oh bd_d6" style="<{if $ht.Status=='10'}>display:none;<{/if}>">
	
<{if $ht.Status != "21" && $ht.Status != "22" && $zy.Status!=3 && $zy.Status!=4}>
<div id="qrdiv2" align="right" style="display:none; margin-top:10px;"><img src="images/btn_qrfhd_gray.png" style="cursor:pointer;"  width="92" height="26" /></div>
<div id="qrdiv" style="margin-top:10px" >

<input type="hidden" name="htid" id="htid" value="<{$ht.ID}>">
<input type="hidden" name="ddid" id="ddid" value="<{$params.id}>">
<label class="fr">


<{if $ht.PayType == "2" || $ht.PayType == "1"}>

<!-------收发货-------------------------------------------------------------------------------------------->
			
			<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && $ht.Status=="12"}>
                <{if $orderstatus > "0"}><!--add by hezpeng for 多次发货 started 2016/12/02 orderstatus>0有未确认的发货单，可以再次发货-->
			    <img src="images/btn_qrfhd.png" type="button" style="cursor:pointer;" width="92" height="26" onclick="qrfh(this);"/><!--  -->
                <{else}>
                <img src="images/btn_qrfhd_gray.png" style="cursor:pointer;"  width="92" height="26" />
			    <{/if}>
			<{/if}>





<!-------收发货-------------------------------------------------------------------------------------------->

<{/if}>

<{if $ht.PayType == "3" || $ht.PayType == "4"}>
<!--UPdate by xiakang for yunqian started 2015/05/30-->
<{if $ht.contractTye=="0"}>	
			<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && $ht.Status=="5"}>
                <{if $orderstatus > "0"}><!--add by hezpeng for 多次发货 started 2016/12/02 orderstatus>0有未确认的发货单，可以再次发货-->
                <img src="images/btn_qrfhd.png" type="button" style="cursor:pointer;" width="92" height="26" onclick="qrfh(this);"/><!---->
                <{else}>
                <img src="images/btn_qrfhd_gray.png" style="cursor:pointer;"  width="92" height="26" />
                <{/if}>
            <{/if}>
			<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && $ht.IsConfirmSo == "1"&& $ht.IsConfirmBo == "0"}>
			<img src="images/btn_qrfhd_gray.png" style="cursor:pointer;"  width="92" height="26" />
			<{/if}>
<{else}>
            <{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && ($ht.Status=="17" || $ht.Status=="5")}>
                <{if $orderstatus > "0"}><!--add by hezpeng for 多次发货 started 2016/12/02 orderstatus>0有未确认的发货单，可以再次发货-->
                <img src="images/btn_qrfhd.png" type="button" style="cursor:pointer;" width="92" height="26" onclick="qrfh(this);"/><!---->
                <{else}>
                <img src="images/btn_qrfhd_gray.png" style="cursor:pointer;"  width="92" height="26" />
                <{/if}>
			<{/if}>
			<{if $smarty.session.SYS_COMPANYID == $ht.Mid_Shipper && $ht.IsConfirmSo == "1"&& $ht.IsConfirmBo == "0"}>
			<img src="images/btn_qrfhd_gray.png" style="cursor:pointer;"  width="92" height="26" />
			<{/if}>
<{/if}>
<!--Updata by xiakang for yunqian ended 2015/05/30-->
<{/if}>

</label>
</div>
<{/if}>
    </div>
    <div class="clear"></div>
    
    
    <div class="talkAndTrack mt10 wrap bd_d6 oh" id="tabsholder"><!--  洽谈和追踪  -->
        <ul class="tabs">
            <li id="tab1">在线洽谈</li>
            <li id="tab2">订单追踪</li>
        </ul>
        <div class="contents marginbot">
    
            <div id="content1" class="tabscontent"><!--   在线洽谈   -->
                
                <div class="talkArea"><!--  洽谈区域  -->
         
<{foreach from=$qiatan item=v}>			
				  <dl>
                        <dt>
                            <a href="#" target="_blank"><img src="img/userPhoto.jpg" /></a>
                            <span class="fl"><a href="listing.php?view=zylist&mid=<{$v.SendComID}>" target="_blank" <{if $smarty.session.SYS_COMPANYID == $v.SendComID}>class="color_blue"<{else}>class="color_yellow"<{/if}>><{$v.SendComNameShort}></a></span>
                            <span class="fl"></span>
                            <span class="fr"><{$v.SendTime}> 发表</span>
                        </dt>
                        <dd>
                            <{$v.Message}>
                            <span class="fr"><a href= "javascript:void(0);" onclick="Message.focus();Message.value='';reciveID.value='<{$v.SendComID}>';messageID.value='<{$v.ID}>';"></a></span>
                        </dd>
                    </dl>
<{/foreach}>            
                 
            
                
                </div><!--   洽谈区域 end  -->
                <div class="clear"></div>
                
                
                <div class="content replyWindow"><!--  回复窗口  -->
				<input type="hidden" name="reciveID" id="reciveID" value="<{if $zy.Did == $smarty.session.SYS_COMPANYID}><{$zy.Mid}><{else}><{$zy.Did}><{/if}>">
				<input type="hidden" name="messageID" id="messageID" value="">
                    <textarea onclick="this.innerHTML='';" name="Message" id="Message">输入你想说的话，与商家/厂家直接交流。</textarea><br />
                    <{if $smarty.session.SYS_COMPANYID ==''}><span class="fl">您还未登录，请<a href="user.php?view=login" target="_blank" class="color_blue">登录</a></span><{/if}>
                    <input type="image" src="img/btn_fabu.jpg" class="fb_btn_fr" name="Submit" onclick="sendmessage('<{$comp2.ID}>')"/>
                </div><!--  回复窗口 end  -->  
                <div class="clear"></div>
                
                
            </div><!--   在线洽谈 end   -->
            
            <div id="content2" class="tabscontent"><!--   订单追踪   -->
                <table style="width: 1159px;">
                    <thead>
                        <tr>
                            <td width="180">处理时间</td>
                            <td>处理信息</td>
                            <td align="right">操作人</td>
                        </tr>
                    </thead>
                    <tbody>
					<{foreach from=$orderfind item=v}>
                        <tr>
                            <td><{$v.CreateDate}></td>
                            <td><{$v.StatusDesc}></td>
                            <td align="right"><{$v.CreateUser}>/<{$v.ARealName}></td>
                        </tr>
					<{/foreach}>
                    
                    </tbody>
                </table>
            </div><!--   订单追踪 end   -->
            
        </div>
    </div><!--  洽谈和追踪 end  -->
    <div class="clear"></div>





   <!--  总计  -->
	<!--div class="total bd_d6 bg_f8 mt10 oh">
        <ul>
        	<li class="fl">
            	<span>订单编号：<{$zy.OrderNo}></span>
                <span>订单日期：<{$zy.CreateDate}></span>
            </li>
            <li class="fr">
                <{if $myorder=="1" && $zy.Status=="1"}><span class="btn_tick"><a href="javascript:void(0)"  onclick="qrdd(<{$zy.ID}>);">订单确认</a></span><{/if}>
                <span style="margin-top: 3px;">总数量<b class="num"><{$zy.Tweight}></b>吨，总金额：<b class="price">￥<{$zy.Tmoney}></b>元</span>
            </li>
    	</ul>
    </div--><!--   总计 end  -->
    <div class="clear"></div>

    


</div><!--  container end  -->
<div class="clear"></div>


  <{include file="../footer.html"}>


<script>
function changefhxx(nowId){
	var id=document.getElementById("firstid").value;
	
	var param = "action=ajaxaddorder&id="+id + "&type=3"+"&nowID="+nowId+"&dtime="+new Date().getTime();
	var ajax = new Ajax("member.php",setdata3,param);
}
function setdata3(result){
	
	location.reload();
}

function qrddd(flag){
	if(window.confirm("是否确认?" ))
	{
		window.open("bizorder.php?action=qrdd&id="+flag);
	}
}

function sendmessage(item){
if("<{$smarty.session.SYS_COMPANYID}>"==''){
		alert("您还未登录");
		return false;
}	


	var Message = document.getElementById("Message").value;
	var reciveID = document.getElementById("reciveID").value;
//	var messageID = document.getElementById("messageID").value;
	if(Message =='' || Message=="输入你想说的话，与商家/厂家直接交流。"){
		alert("请输入你想说的话！");
		return false;
	}
	//alert(messageID);return false;
	var param = "action=ajaxsendmsgdd&ReceiveComID="+reciveID+"&MessageType=3"+"&Message="+Message+"&orderid=<{$params.id}>" + "&time="+new Date().getTime();
	var ajax = new Ajax("tender.php",setdata,param);
}
function setdata(result){
	//alert(result);
	var data = eval('(' + result + ')');
	if(parseInt(data.error)){
		alert(data.errorinfo);
	}
	else{
		alert("发送成功");
		location.reload();
		//document.getElementById("Message").value='';
		
    }
}



function checkfh(){


	var num5 = 0;
<{foreach from=$zydetail  item=v key=k }>	
	<{foreach from=$pzzlist2[$v.oid] item=v2 key=k2}>
		if((document.getElementById("Fhjz2<{$v2.ID}>").value == '' || document.getElementById("Fhjz2<{$v2.ID}>").value == '0') && document.getElementById("Status2<{$v2.ID}>").checked== true) num5 ++;
	<{/foreach}>
<{/foreach}>
	if(num5 >0){	
		//return "5";
	}

	var num1 = 0;
<{foreach from=$zydetail  item=v key=k }>		
	<{foreach from=$pzzlist2[$v.oid] item=v2 key=k2}>
		if((document.getElementById("Fhsl2<{$v2.ID}>").value == '' || document.getElementById("Fhsl2<{$v2.ID}>").value == '0') && document.getElementById("Status2<{$v2.ID}>").checked== true) num1 ++;
	<{/foreach}>
<{/foreach}>
	if(num1 >0){	
		return "1";
	}

	var num2 = 0;
<{foreach from=$zydetail  item=v key=k }>	
	<{foreach from=$pzzlist2[$v.oid] item=v2 key=k2}>
		if((document.getElementById("Fhjs2<{$v2.ID}>").value == '' || document.getElementById("Fhjs2<{$v2.ID}>").value == '0') && document.getElementById("Status2<{$v2.ID}>").checked== true ) num2 ++;
	<{/foreach}>
<{/foreach}>
	if(num2 >0){	
		return "2";
	}

	var num3 = 0;
<{foreach from=$zydetail  item=v key=k }>		
		if(document.getElementById("Yscom2<{$v.oid}>").value == '' || document.getElementById("Yscom2<{$v.oid}>").value == '0') num3 ++;
<{/foreach}>
	if(num3 >0){	
		return "3";
	}

	var num4 = 0;
<{foreach from=$zydetail  item=v key=k }>		
		if(document.getElementById("Carnum2<{$v.oid}>").value == '' || document.getElementById("Carnum2<{$v.oid}>").value == '0') num4 ++;
<{/foreach}>
	if(num4 >0){	
		return "4";
	}




}


function fhxxPaytest(id){
	//alert("1111111");
//var fhxxform = "document.fhxxform" + id;
	//alert(id);

	//	var param = "action=fhxx&id="+id;
	  //   var ajax = new Ajax("bizorder.php", outWritefh5, param );

//alert(document.getElementById( "Fhjs2"+id ).value);

	//alert(checkradio( "Yscom"+id ));
	//if( checkradio( "Yscom"+id ) == "" ){
	//	alert("请选择运输公司");
	//	return false;
	//}	
/*
if(fhxx(id)=="1"){
alert("请填写件重");return false;
}else if(fhxx(id)=="2"){
alert("请填写件数");return false;
}else if(fhxx(id)=="3"){
alert("请填写数量");return false;
}*/
	
	if(fhxx(id)=="3"){
	alert("请填写数量");return false;
	}
//return false;
	if(document.getElementById("Delivery").value!="1"){
	//alert("t1");
		if(document.getElementById( "Yscom2"+id ).value == "" || document.getElementById( "Yscom2"+id ).value == "0" ){
			alert("请选择运输公司["+"Yscom2"+id+"]["+document.getElementById( "Yscom2"+id ).value+"]");
			//alert("t2");
			return false;
		}
	}
//	if(document.getElementById( "Fhsl2"+id ).value == "" || document.getElementById( "Fhsl2"+id ).value == "0" ){
//		alert("请填写数量");
//		return false;
//	}
//	if(document.getElementById( "Fhjs2"+id ).value == "" || document.getElementById( "Fhjs2"+id ).value == "0" ){
//		alert("请填写件数");
//		return false;
//	}

//alert(id);
	// Updated for fhysd by Zhu Dahua started  2015/09/16
	//document.getElementById("fhxxform").action="bizorder.php?action=fhxx&id="+id;
	 document.getElementById("fhxxform").action="bizorder.php?action=fhxx2&id="+id;
	 // Updated for fhysd by Zhu Dahua ended  2015/09/16
	 submitRequest(document.fhxxform,"post","text",outWritefh5test,null);//使用自定义myProcess
	return true;
}

function outWritefh5test(count){
if(count=="1"){
	alert("资源发货数量已达到订购数量");
	return false;
}	
	
	 
     var tmp = count.split("|-|");
	 document.getElementById( "Fhsl"+tmp[8] ).innerHTML = tmp[0];
	 document.getElementById( "Fhjz"+tmp[8] ).innerHTML = tmp[1];
	 document.getElementById( "Fhjs"+tmp[8] ).innerHTML = tmp[2];
	 document.getElementById( "Yscom"+tmp[8] ).innerHTML = tmp[3];
	 document.getElementById( "Fhlxr"+tmp[8] ).innerHTML = tmp[4];
	 document.getElementById( "Fhphone"+tmp[8] ).innerHTML = tmp[5];
	 document.getElementById( "Carnum"+tmp[8] ).innerHTML = tmp[6];
	 document.getElementById( "Fhbz"+tmp[8] ).innerHTML = tmp[7];

  document.getElementById("showfhxx_"+ tmp[8]).style.display="block";
 document.getElementById("fhxxdiv_"+ tmp[8]).style.display="none";
}
//Added by quanjw for meijiao start 2015/1/20
 show_fh();
function show_fh(str){
 		var rdo=$("input[name='bigpz']:checked").val();
		if(str!=null&&str!=''){
			var boxes = document.getElementsByName("bigpz");
			for(i=0;i<boxes.length;i++){
				$("#"+boxes[i].id).removeAttr("checked"); 
			}
			$("#"+str).attr("checked", "checked");
		}


  		//var rdo=$("input[name='bigpz']:checked").val();
  		//Updated by quanjw for jinshuzhipin start 2015/2/26
  		$(".tr_Vid_<{$MEITAN_VID}>").hide();
 		$(".tr_Vid_<{$JIAOTAN_VID}>").hide();
 		$(".tr_Vid_0").hide();
 		$(".tr_Vid_<{$SHUINI_VID}>").hide();
 		$(".tr_Vid_<{$JINSHU_VID}>").hide();
        $(".tr_Vid_<{$HW_VID}>").hide();
  		switch(rdo){
  			case "<{$MEITAN_VID}>":
  				$(".tr_Vid_<{$MEITAN_VID}>").show();
  				break;
  			case "<{$JIAOTAN_VID}>":
  				$(".tr_Vid_<{$JIAOTAN_VID}>").show();
  				break;
  			case "<{$SHUINI_VID}>":
  				$(".tr_Vid_<{$SHUINI_VID}>").show();
  				break;
  			case "<{$JINSHU_VID}>":
  				$(".tr_Vid_<{$JINSHU_VID}>").show();
				break;
            case "<{$HW_VID}>":
				$(".tr_Vid_<{$HW_VID}>").show();
				break;
  			default:
  				$(".tr_Vid_0").show();
  		}
  		//Updated by quanjw for jinshuzhipin end 2015/2/26
  	}
  	
  	/*function show_fh2(){
  		var rdo2=$("input[name='bigpz2']:checked").val();
  		//Updated by quanjw for jinshuzhipin start 2015/2/26
  		$(".tr_Vid_<{$MEITAN_VID}>").hide();
 		$(".tr_Vid_<{$JIAOTAN_VID}>").hide();
 		$(".tr_Vid_0").hide();
 		$(".tr_Vid_<{$SHUINI_VID}>").hide();
  		$(".tr_Vid_<{$JINSHU_VID}>").hide();
  		switch(rdo2){
  			case "<{$MEITAN_VID}>":
  				$(".tr_Vid_<{$MEITAN_VID}>").show();
  				break;
  			case "<{$JIAOTAN_VID}>":
  				$(".tr_Vid_<{$JIAOTAN_VID}>").show();
  				break;
  			case "<{$SHUINI_VID}>":
  				$(".tr_Vid_<{$SHUINI_VID}>").show();
  				break;
  			case "<{$JINSHU_VID}>":
  				$(".tr_Vid_<{$JINSHU_VID}>").show();
  				break;
  			default:
  				$(".tr_Vid_0").show();
  		}
  		//Updated by quanjw for jinshuzhipin end 2015/2/26
  	}*/
//Added by quanjw for jinshuzhipin start 2015/3/10 	
//在展开修改时候,修正资源radio 跟 资源内容不一致情况
function editfhxx_plus(id){
	editfhxx(id);
	show_fh2(id+"&&999");
  	}
  	
function closefhxx_plus(id){
	closefhxx(id);
	show_fh();
  	}
//Added by quanjw for jinshuzhipin end 2015/3/10
//Added by quanjw for meijiao end 2015/1/20
</script>