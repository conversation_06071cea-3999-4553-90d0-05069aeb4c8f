<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
<meta http-equiv="X-UA-Compatible" content="IE=8" />
<title><{$page_title1}><{$page_title}><{$smarty.const.WEBNAME}></title>
<link rel="shortcut icon" href="favicon.ico" /></head>

<link type="text/css" rel="stylesheet" href="css/base.css" />
<link type="text/css" rel="stylesheet" href="css/index.css" />
<script language="javascript" type="text/javascript" src="./js/jquery-1.6.2.min.js"></script>

<script language="javascript" type="text/javascript" src="./js/ajaxgwc.js"></script>
		<script src="./js/swt.js" ></script>
<script language="javascript" type="text/javascript" src="./js/ajaxsendmsgbox.js"></script>

<script language="javascript" type="text/javascript" src="js/My97DatePicker/WdatePicker.js"></script>
<script src="./js/ajax.js" ></script>
<link href="css/tender.css" rel="stylesheet" type="text/css" />

<script src="./js/ajaxlogin.js" ></script>
<SCRIPT language="javascript" src="./js/opwindows3.js"></SCRIPT>
<div id="ajaxdivgwc" ></div>
 
        	<div class="productList bd_d6 oh" ><!--   资源列表  -->  

	

            	<div class="tabs"><!--  选项卡  -->
                    <ul>
                        <li id="wsxh_0_bar"  onclick="show_wsxh_item('wsxh_0');" class="on" ><a >全部</a></li>
<{foreach from=$bigpz item=v key=k}>
						<li id="wsxh_<{$k}>_bar"  onclick="show_wsxh_item('wsxh_<{$k}>');" ><a style="text-align:center"><{$v}></a></li>
<{/foreach}>
                    </ul>
                </div><!--  选项卡 end  -->
                <div class="clear"></div>


 <div id="wsxh_0" style="display:block;"  >  
 <div style="height:460px">
            	<table>
                	<thead>
                    	<tr>
                        	<td width="42" align="center">选择</td>
                            <td width="75" >公司名称</td>
                            <td width="65" >品名</td>
                            <td width="50" >材质</td>
                            <td width="60" >规格</td>
                            <td width="42" >价格</td>
							<{if $params.st =="7"}>
							<td width="82"  align="center">成交量/需求量</td>
							<{else}>
                            <td width="82"  align="center">成交量/资源量</td>
							<{/if}>
                            <td width="60" >生产厂家</td>
							<{if $params.st =="7"}>
							<td width="70" >收货地</td>
							<{else}>
                            <td width="42" >交货地</td>
							<{/if}>
                            <td width="42" >时间</td>
							
                            <td width="62" >&nbsp;&nbsp;&nbsp;在线</td>
                            <td width="40" >洽谈</td>
							<td width="40" >我要</td>
                        </tr>
                    </thead>
                    <tbody>
<{foreach from=$pzzy[0] item=zy key=k name="a"}>

                    	
                    	<tr>
                        	<td align="center"><span id="jjcg2_xuanz<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><input type="checkbox"  name="res_id[]" type="checkbox" value="<{$zy.ID}>"/></span></td>
                            <td><span id="jjcg2_ComNameShort<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a  href="listing.php?view=zylist&mid=<{$zy.Mid}>" target="_blank"><{$zy.ComNameShort|substr:0:12}></a></span>
							</td>
                            <td><span id="jjcg2_VarietyName<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.VarietyName|substr:0:12 }></a></span></td>
                            <td><span id="jjcg2_MaterialCode<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.MaterialCode|substr:0:30 }></a></span></td>
                            <td><span id="jjcg2_SpecCode<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.SpecCode|substr:0:10 }></a></span></td>
                            <td><span id="jjcg2_SalesMinPrice<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{if $zy.SalesMinPrice !='0.00' && $zy.SalesMinPrice != "" && $zy.TradeType!="3"}><{$zy.SalesMinPrice|string_format:'%.0f'}><{else}>协议价<{/if}></a></span></td>
                            <td align="center"><span id="jjcg2_QuantitySales<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.QuantitySalesed}>/<{$zy.QuantitySales}></a></span></td>
                            <td><span id="jjcg2_OriginCode<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.OriginCode|substr:0:12}></a></span></td>
                            <td><span id="jjcg2_PickUpCity<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.PickUpCity }></a></span></td>
							 <td><span id="jjcg2_CreateDate<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.CreateDate|date_format:"%m/%d"}></a></span></td>

                            <td>
                            	<span id="jjcg2_contact<{$qhstr}>_<{$smarty.foreach.a.iteration}>" >
									<a href="javascript:checkHasAndChatTo('<{$zy.CreateUser}>')"><img class="img_<{$zy.CreateUser}>" id="img_<{$zy.CreateUser}>" src="images/gmt4.gif" style="border:0;" ></a>
									<{if $zy.QQNum!=""}><a href="//wpa.qq.com/msgrd?v=3&uin=<{$zy.QQNum}>&site=qq&menu=yes"><img src="img/talk_qq.png" /></a><{/if}>
								
								</span>
                            </td>
							
                            <td><span id="jjcg2_liuyan<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>&talk=1"  target="_blank">留言</a></span></td>
							
							<td><span id="jjcg2_status<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank">
									
									<{if $zy.flag == 1 && $zy.salestp == 1}><img  src="images/buy.jpg" style="border:0;" ><{/if}>
									<{if $zy.flag == 2 && $zy.salestp == 1}><img  src="images/buy2.jpg" style="border:0;" ><{/if}>
									<{if $zy.flag == 1 && $zy.salestp == 2}><img  src="images/sales.jpg" style="border:0;" ><{/if}>
									<{if $zy.flag == 2 && $zy.salestp == 2}><img  src="images/sales2.jpg" style="border:0;" ><{/if}>
									
									</a></span>
							</td>
						</tr>   
						
<{/foreach}>
                	</tbody>
                </table>
</div>

				<div class="clear"></div>
                
                <div class="allSelect"><!--  全选  -->
                
                	<div class="fl">
                    	<label><input type="checkbox" onclick="checkallzyx('')"  id="check" />全选</label>
				
					<{if $params.st =="8"}>
					    <span class="btn_tick"><a href="#"  onclick="tj( 'res_id[]' );return false;">加入采购车</a></span>
					<{elseif $params.st =="7"}>
                		<span class="btn_tick"><a href="#"  onclick="tj( 'res_id[]' );return false;">加入供货车</a></span>
					<{else}>
						<span class="btn_tick"><a href="#"  onclick="tj( 'res_id[]' );return false;">加入购物车</a></span>
					<{/if}>
				
                        <a href="member.php?view=jymanage" target="view_window"><img src="img/btn_fbwsxh.jpg" /></a>
                    </div>
                    
                    <div class="page fr"><span class="disabled">&lt; Prev</span>第<span  id="jjcg2_<{$qhstr}>cpage"><{$page}></span>/<{$totalall}>页&nbsp;&nbsp;&nbsp;&nbsp;
                    	<a  onclick="gopre_<{$qhstr}>2(2,'<{$totalall}>',1,'all','');" cursor:pointer>上一页</a>&nbsp;&nbsp;
                    	<a onclick="gonext_<{$qhstr}>2(2,'<{$totalall}>',1,'all','');">下一页</a>&nbsp;&nbsp;&nbsp;&nbsp;
                    	跳 至&nbsp;&nbsp;<b><input  id="searchpage<{$qhstr}>_2" onblur="searchpage_<{$qhstr}>2(2,'<{$totalall}>',1,'all','');" value=""  type="text" style="width:50px;text-align:center;height:18px; border:1px solid #ccc;"/></b>&nbsp;&nbsp;页</div>
                
                </div><!--  全选 end  -->

</div>

<{foreach from=$bigpz item=v key=k }>
 <div id="wsxh_<{$k}>" style="display:none;"   >   
 <div style="height:460px">
            	<table>
                	<thead>
                    	<tr>
                        	<td width="40">选择</td>
                            <td class="tl">公司名称</td>
                            <td class="tl">品名</td>
                            <{if isset($listAttrs[$k]) }>
                            	<{foreach from=$listAttrs[$k] item=listname key=listkey}>
                            		<td class="t1"><{$listname}></td>
                            	<{/foreach}>
                            <{else}>
                            	<td class="tl">材质</td> 
                            	<td class="tl">规格</td> 
                            <{/if}>
                            
                            <td>价格</td>
							<{if $params.st =="7"}>
							<td>成交量/需求量</td>
							<{else}>
                            <td>成交量/资源量</td>
							<{/if}>
                            <td>生产厂家</td>
							<{if $params.st =="7"}>
							<td>收货地</td>
							<{else}>
                            <td>交货地</td>
							<{/if}>
							<td>时间</td>
                            <td>在线</td>
                            <td width="45">洽谈</td>
							<td width="45">我要</td>
                        </tr>
                    </thead>
                    <tbody>
<{foreach from=$pzzy[$k] item=zy key=k2  name="a"}>

                    	
                    	<tr>
                        	<td align="center">
						</span>
							<span id="jjcg<{$k}>4_xuanz<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><input type="checkbox"  name="res_id[]<{$k}>" type="checkbox" value="<{$zy.ID}>"/></span>
							</td>
                            <td><span id="jjcg<{$k}>4_ComNameShort<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a  href="listing.php?view=zylist&mid=<{$zy.Mid}>" target="_blank"><{$zy.ComNameShort|substr:0:12}></a></span>
							</td>
                            <td><span id="jjcg<{$k}>4_VarietyName<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.VarietyName|substr:0:12 }></a></span></td>
                            <{if isset($listAttrs[$k]) }>
                            	<{foreach from=$listAttrs[$k] item=listname key=listkey}>
                            		<td class="tl"><span id="jjcg<{$k}>4_<{$listkey}><{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.$listkey|substr:0:7 }></a></span></td> 
                            	<{/foreach}>
                            <{else}>
                            	<td class="tl"><span id="jjcg<{$k}>4_MaterialCode<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.MaterialCode|substr:0:7 }></a></span></td> 
                            	<td class="tl"><span id="jjcg<{$k}>4_SpecCode<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.SpecCode|substr:0:10 }></a></span></td> 
                            <{/if}>
                            <td><span id="jjcg<{$k}>4_SalesMinPrice<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{if $zy.SalesMinPrice !='0.00' && $zy.SalesMinPrice != ""}><{$zy.SalesMinPrice|string_format:'%.0f'}><{else}>协议价<{/if}></a></span></td>
                            <td align="center"><span id="jjcg<{$k}>4_QuantitySales<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.QuantitySalesed}>/<{$zy.QuantitySales}></a></span></td>
                            <td><span id="jjcg<{$k}>4_OriginCode<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.OriginCode|substr:0:12}></a></span></td>
                            <td><span id="jjcg<{$k}>4_PickUpCity<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.PickUpCity }></a></span></td>
							<td><span id="jjcg<{$k}>4_CreateDate<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.CreateDate|date_format:"%m/%d" }></a></span></td>
                            <td>
                            	<span id="jjcg<{$k}>4_contact<{$qhstr}>_<{$smarty.foreach.a.iteration}>" >
									<a href="javascript:checkHasAndChatTo('<{$zy.CreateUser}>')"><img class="img_<{$zy.CreateUser}>" id="img_<{$zy.CreateUser}>" src="images/gmt4.gif" style="border:0;" ></a>
									<{if $zy.QQNum!=""}><a href="//wpa.qq.com/msgrd?v=3&uin=<{$zy.QQNum}>&site=qq&menu=yes"><img src="img/talk_qq.png" /></a><{/if}>
								</span>
                            </td>
                            <td><span id="jjcg<{$k}>4_liuyan<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>&talk=1"  target="_blank">留言</a></span></td>
							<td><span id="jjcg<{$k}>4_status<{$qhstr}>_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank">
									
									<{if $zy.flag == 1 && $zy.salestp == 1}><img  src="images/buy.jpg" style="border:0;" ><{/if}>
									<{if $zy.flag == 2 && $zy.salestp == 1}><img  src="images/buy2.jpg" style="border:0;" ><{/if}>
									<{if $zy.flag == 1 && $zy.salestp == 2}><img  src="images/sales.jpg" style="border:0;" ><{/if}>
									<{if $zy.flag == 2 && $zy.salestp == 2}><img  src="images/sales2.jpg" style="border:0;" ><{/if}>
									
									</a></span>
							</td>
						</tr>   
						
<{/foreach}>
                	</tbody>
                </table>
</div>
				<div class="clear"></div>
                
                <div class="allSelect"><!--  全选  -->
                
                	<div class="fl">
                    	<label><input type="checkbox" onclick="checkallzyx('<{$k}>')"  id="check<{$k}>" />全选</label>
                	<{if $params.st =="8"}>
					    <span class="btn_tick"><a href="#"  onclick="tj( 'res_id[]<{$k}>' );return false;">加入采购车</a></span>
					<{elseif $params.st =="7"}>
                		<span class="btn_tick"><a href="#"  onclick="tj( 'res_id[]<{$k}>' );return false;">加入供货车</a></span>
					<{else}>
						<span class="btn_tick"><a href="#"  onclick="tj( 'res_id[]<{$k}>' );return false;">加入购物车</a></span>
					<{/if}>
                        <a href="member.php?view=jymanage" target="view_window"><img src="img/btn_fbwsxh.jpg" /></a>
                    </div>
                    
                    <div class="page fr"><span class="disabled">&lt; Prev</span>第<span id="jjcg<{$k}>4_<{$qhstr}>cpage"><{$page}></span>/<{$totalpzz[$k]}>页&nbsp;&nbsp;&nbsp;&nbsp;
                    	<a  onclick=" gopre_<{$qhstr}>2(8,'<{$totalpzz[$k]}>',2,'<{$k}>4','<{$k}>');">上一页</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    	<a onclick="gonext_<{$qhstr}>2(8,'<{$totalpzz[$k]}>',2,'<{$k}>4','<{$k}>');">下一页</a>&nbsp;&nbsp;&nbsp;&nbsp;
                    	跳 至&nbsp;&nbsp;<b><input  id="searchpage<{$qhstr}>_<{$k}>4" onblur="searchpage_<{$qhstr}>2(8,'<{$totalpzz[$k]}>',2,'<{$k}>4','<{$k}>');" value=""  type="text" style="width:50px;text-align:center; height:18px; border:1px solid #ccc;"/></b>&nbsp;&nbsp;页</div>



                </div><!--  全选 end  -->

</div>
<{/foreach}>	
 <input type="hidden" id="params" value="<{$str}>">

<script>
function show_cgbj_item(item){

<{foreach from=$bigpz item=v key=k}>
	
	document.getElementById("cgbj_<{$k}>").style.display = "none";
	document.getElementById("cgbj_<{$k}>_bar").className = "";

<{/foreach}>
	document.getElementById("cgbj_ot").style.display = "none";
	document.getElementById("cgbj_ot_bar").className = "";

	document.getElementById("cgbj_0").style.display = "none";
	document.getElementById("cgbj_0_bar").className = "";

document.getElementById(item).style.display = "block";	

	var item_bar=item+"_bar";
	document.getElementById(item_bar).className="on";		


}

function show_wsxh_item(item){
<{foreach from=$bigpz item=v key=k}>
	
	document.getElementById("wsxh_<{$k}>").style.display = "none";
	document.getElementById("wsxh_<{$k}>_bar").className = "";

<{/foreach}>

	document.getElementById("wsxh_0").style.display = "none";
	document.getElementById("wsxh_0_bar").className = "";

document.getElementById(item).style.display = "block";	

	var item_bar=item+"_bar";
	document.getElementById(item_bar).className="on";		


}
<{if $params.vid == null}>
//show_wsxh_item('wsxh_0>')
<{else}>
//show_wsxh_item('wsxh_<{$params.vid}>')
<{/if}>
function show_wsjp_item(item){

<{foreach from=$bigpz item=v key=k}>
	
	document.getElementById("wsjp_<{$k}>").style.display = "none";
	document.getElementById("wsjp_<{$k}>_bar").className = "";

<{/foreach}>
	document.getElementById("wsjp_ot").style.display = "none";
	document.getElementById("wsjp_ot_bar").className = "";

	document.getElementById("wsjp_0").style.display = "none";
	document.getElementById("wsjp_0_bar").className = "";

document.getElementById(item).style.display = "block";	

	var item_bar=item+"_bar";
	document.getElementById(item_bar).className="on";		


}

function show_ccys_item(item){

<{foreach from=$bigpz item=v key=k}>
	
	document.getElementById("ccys_<{$k}>").style.display = "none";
	document.getElementById("ccys_<{$k}>_bar").className = "";

<{/foreach}>
	document.getElementById("ccys_ot").style.display = "none";
	document.getElementById("ccys_ot_bar").className = "";

	document.getElementById("ccys_0").style.display = "none";
	document.getElementById("ccys_0_bar").className = "";

document.getElementById(item).style.display = "block";	

	var item_bar=item+"_bar";
	document.getElementById(item_bar).className="on";		


}


</script>
<script src="/js/ajax.js" ></script>
<script src="/js/common.js" ></script>
<script charset="gbk2312">
// update by libing for pager start 2015/03/26
var page = 1; 
var page_2 = 1; 

function gopre_<{$qhstr}>2(tab,total,types,d,k){ //去上一页

	if(tab==2){ //全部
		var prepage = parseInt( page_2 ) - 1;
		if(prepage >=1){
			page_2= prepage;
		}
	} else {
		eval("if (typeof(page_"+d+") === 'undefined') { page_"+d+"=1;}");
		eval("var curpage = page_"+d);
		prepage = curpage-1;
	}
	
	if( prepage < 1 ){
		eval("page_"+d+"=1");
		return false;
	} 
	eval("page_"+d+" = "+prepage);
	//page = prepage;
	cd_type=tab;
	vid=types;
	mainsearch<{$qhstr}>2(tab,d,k);

}

function gonext_<{$qhstr}>2(tab,total,types,d,k){ //去下一页
		if(tab==2){
			var nextpage = parseInt( page_2 ) + 1;
			if( nextpage  <= total ){
				page_2= nextpage;
			}
		} else {
			eval("if (typeof(page_"+d+") === 'undefined') { page_"+d+"=1;}");
			eval("var curpage = page_"+d);
			nextpage = parseInt(curpage)+1;
		}

	if( nextpage  > total ){
		eval("page_"+d+"=total");
		return false;
	} 
	eval("page_"+d+"=nextpage");
	
	cd_type=tab;
	vid=types;
	mainsearch<{$qhstr}>2(tab,d,k);
}


function searchpage_<{$qhstr}>2(tab,total,types,d,k){
	if(tab == 2){
		var p = document.getElementById( "searchpage<{$qhstr}>_"+tab ).value;
	}else {
		var p = document.getElementById( "searchpage<{$qhstr}>_"+d ).value;
	}

	if( p == '' ){
		return false;
	}

	if( isNaN( p ) || p == '' ){
		alert( "请输入合法数字" );
		return false;
	}

	p = parseInt( p );
	if( p < 1 || p > total ){
		alert( "请输入合法的页码" );
		return false;
	}

	if(tab==2){  
		page_2 = p;
	}	else {
		eval("page_"+d+"="+p);
	}

	cd_type=tab;
	vid=types;
	mainsearch<{$qhstr}>2(tab,d,k);

}



function mainsearch<{$qhstr}>2(tab,d,k){	

	if(tab==2){ 
		var pages = page_2;
	} else {
		eval("var pages = page_"+d);
	}	
	
	var param = "view=ajaxgetindexcominfo&needCache=1&page=" + pages + "&type=" + cd_type +"&vid=" + vid +"&d="+d+"&k="+k +"<{$str}>" ;
	var ajax = new Ajax( "index.php", setData<{$qhstr}>2, param );

}

function setData<{$qhstr}>2( returnStr ){
//		 alert(returnStr);

		   var tmptype = returnStr.split("|T|");
		   var type = tmptype[0];
		    var tmp = tmptype[2].split( "|H|" );
		    var typ="";

			var typx= tmptype[1];
			//var x="page"+tmptype[1]+"_";

		 if(type == 1){typ="jjxs1_";}		
		 if(type == 2){typ="jjcg2_";}
		 if(type == 5){typ="jjxs5_";}
		 if(type == 6){typ="jjcg6_";}
		 if(type == 7 ){typ="jjxs"+tmptype[1]+"_";}
		 if(type == 8){typ="jjcg"+tmptype[1]+"_";}

		 if(typx == "gcxs" || typx == "gccg" || typx == "sjxs"  || typx == "sjcg" ){
			typ =tmptype[1]+"_";
		 }
// alert(typ);
		   var records = tmp[0];
		 
		   if( records == '' ){
		   		for( var i = 0; i < 10; i++ ){
		         var id = i + 1;
				}
		      // waitover();
			   return false;
		   }
	   
		   var others = tmp[1];
		   records = records.split( "|O|" );
		  // alert(typ);

		   tmp = others.split( "|A|" );
		   page = tmp[1];
		 //  alert(page);
		   totalpage = tmp[0];
		   document.getElementById(typ + "<{$qhstr}>cpage" ).innerHTML = page;
		  // document.getElementById(typ + "cpages" ).innerHTML = page;
// alert(typeof(records[99])); 	

			//Updated by quanjw for yema start 2015/3/24
			for( var i = 0; i < <{$per}>; i++ ){
		   //for( var i = 0; i < 26; i++ ){
			//Updated by quanjw for yema end 2015/3/24
			   var id = i + 1;		
			  // alert(records[i]);
			   if( typeof(records[i]) != 'undefined' ){
				
		         var tmp = records[i].split( "|X|" );	
				//alert(typ + "contact" + id  );
				if(document.getElementById( typ + "ComNameShort<{$qhstr}>_" + id )){
					document.getElementById( typ + "ComNameShort<{$qhstr}>_" + id ).innerHTML = tmp[0];			
				}
			     
			     if(document.getElementById( typ + "VarietyName<{$qhstr}>_" + id )){
			     	document.getElementById( typ + "VarietyName<{$qhstr}>_" + id ).innerHTML = tmp[1];
			    }
			     //Updated by quanjw for jinshuzhipin start 2015/3/24
			     if(document.getElementById( typ + "MaterialCode<{$qhstr}>_" + id )){
			     	document.getElementById( typ + "MaterialCode<{$qhstr}>_" + id ).innerHTML = tmp[2];	
			     }
			     //Updated by quanjw fow jinshuzhipin end 2015/3/24
                 if(document.getElementById( typ + "SpecCode<{$qhstr}>_" + id )){
			     document.getElementById( typ + "SpecCode<{$qhstr}>_" + id ).innerHTML = tmp[3];
                 }
			     document.getElementById( typ + "OriginCode<{$qhstr}>_" + id ).innerHTML = tmp[4];
			     document.getElementById( typ + "PickUpCity<{$qhstr}>_" + id ).innerHTML = tmp[5];
			     document.getElementById( typ + "SalesMinPrice<{$qhstr}>_" + id ).innerHTML = tmp[6];
				 document.getElementById( typ + "QuantitySales<{$qhstr}>_" + id ).innerHTML = tmp[7];
			    // document.getElementById( typ + "QuantitySalesed_" + id ).innerHTML = tmp[8];
			    // document.getElementById( typ + "fbdate_" + id ).innerHTML = tmp[9];
				// document.getElementById( typ + "jjtype_" + id ).innerHTML = tmp[10];
				 //if(document.getElementById( typ +"<{$qhstr}>"+ id )){
				 //	document.getElementById( typ +"<{$qhstr}>"+ id ).innerHTML = tmp[11];
				 //	document.getElementById( typ +"<{$qhstr}>"+ id ).style.display = "block";
				 //}
				 //document.getElementById( typ + id + "_2").innerHTML = tmp[12];				
				 document.getElementById( typ + "xuanz<{$qhstr}>_" + id ).innerHTML = tmp[13];
				document.getElementById( typ + "contact<{$qhstr}>_" + id ).innerHTML = tmp[14];
				document.getElementById( typ + "liuyan<{$qhstr}>_" + id ).innerHTML = tmp[15];
				document.getElementById( typ + "CreateDate<{$qhstr}>_" + id ).innerHTML = tmp[16]; 
				//Updated by quanjw for jinshuzhipin start 2015/3/24
			    if(document.getElementById( typ + "cd<{$qhstr}>_" + id )){
			     	document.getElementById( typ + "cd<{$qhstr}>_" + id ).innerHTML = tmp[17];	
			    }
			    if(document.getElementById( typ + "strength<{$qhstr}>_" + id )){
			     	document.getElementById( typ + "strength<{$qhstr}>_" + id ).innerHTML = tmp[18];	
			    }
			    if(document.getElementById( typ + "xincengWeight<{$qhstr}>_" + id )){
			     	document.getElementById( typ + "xincengWeight<{$qhstr}>_" + id ).innerHTML = tmp[19];	
			    }
			    if(document.getElementById( typ + "status<{$qhstr}>_" + id )){
			     	document.getElementById( typ + "status<{$qhstr}>_" + id ).innerHTML = tmp[20];	
			    }
                
                if(document.getElementById( typ + "tks_fe<{$qhstr}>_" + id )){
			     	document.getElementById( typ + "tks_fe<{$qhstr}>_" + id ).innerHTML = tmp[21];	
			    }
			    if(document.getElementById( typ + "tks_al<{$qhstr}>_" + id )){
			     	document.getElementById( typ + "tks_al<{$qhstr}>_" + id ).innerHTML = tmp[22];	
			    }
			    if(document.getElementById( typ + "tks_si<{$qhstr}>_" + id )){
			     	document.getElementById( typ + "tks_si<{$qhstr}>_" + id ).innerHTML = tmp[23];	
			    }
			    //Updated by quanjw fow jinshuzhipin end 2015/3/24
                   //add by zfy started 2019/09/26 加焊网
                   if(document.getElementById( typ + "yongtu<{$qhstr}>_" + id )){
                       document.getElementById( typ + "yongtu<{$qhstr}>_" + id ).innerHTML = tmp[24];
                   }
                   if(document.getElementById( typ + "strength<{$qhstr}>_" + id )){
                       document.getElementById( typ + "strength<{$qhstr}>_" + id ).innerHTML = tmp[25];
                   }
                   //add by zfy ended 2019/09/26 加焊网

			   }
			   if( typeof(records[i]) == 'undefined' ){
			   	if(document.getElementById( typ + "ComNameShort<{$qhstr}>_" + id )){
			   		document.getElementById( typ + "ComNameShort<{$qhstr}>_" + id ).innerHTML = '';					
			   	}
			     
			     document.getElementById( typ + "VarietyName<{$qhstr}>_" + id ).innerHTML = '';
			     //Updated by quanjw for jinshuzhipin start 2015/3/24
			     if(document.getElementById( typ + "MaterialCode<{$qhstr}>_" + id )){
			     	document.getElementById( typ + "MaterialCode<{$qhstr}>_" + id ).innerHTML = "";
			     }
			     //Updated by quanjw fow jinshuzhipin end 2015/3/24
                 if(document.getElementById( typ + "SpecCode<{$qhstr}>_" + id )){
			     document.getElementById( typ + "SpecCode<{$qhstr}>_" + id ).innerHTML = '';
                 }
			     document.getElementById( typ + "OriginCode<{$qhstr}>_" + id ).innerHTML = '';
			     document.getElementById( typ + "PickUpCity<{$qhstr}>_" + id ).innerHTML = '';
			     document.getElementById( typ + "SalesMinPrice<{$qhstr}>_" + id ).innerHTML = '';
				 document.getElementById( typ + "QuantitySales<{$qhstr}>_" + id ).innerHTML = '';
			   //  document.getElementById( typ + "QuantitySalesed_" + id ).innerHTML = '';
				// document.getElementById( typ + "fbdate_" + id ).innerHTML = '';
			    // document.getElementById( typ + "jjtype_" + id ).innerHTML = '';
			    			   //Updated by quanjw for jinshuzhipin start 2015/3/24
			    //if(document.getElementById( typ +"<{$qhstr}>"+ id )){
			    // document.getElementById( typ +"<{$qhstr}>"+ id ).innerHTML = '';
				 //document.getElementById( typ +"<{$qhstr}>"+ id ).style.display = "none";
			    //}
				 // document.getElementById( typ + id + "_2" ).style ="none";
				// document.getElementById( typ + id ).style = "position:absolute;left:580px;padding-top:0px;width:290px;  visibility:hidden;";
				// document.getElementById( typ + id + "_2" ).style  = "position:absolute;left:580px;padding-top:0px;width:290px;  visibility:hidden;";
				
				document.getElementById( typ + "xuanz<{$qhstr}>_" + id ).innerHTML = '';
				document.getElementById( typ + "contact<{$qhstr}>_" + id ).innerHTML = '';
				document.getElementById( typ + "liuyan<{$qhstr}>_" + id ).innerHTML = '';
				document.getElementById( typ + "CreateDate<{$qhstr}>_" + id ).innerHTML = '';
			   //Updated by quanjw for jinshuzhipin start 2015/3/24
			    if(document.getElementById( typ + "cd<{$qhstr}>_" + id )){
			     	document.getElementById( typ + "cd<{$qhstr}>_" + id ).innerHTML = "";	
			    }
			    if(document.getElementById( typ + "strength<{$qhstr}>_" + id )){
			     	document.getElementById( typ + "strength<{$qhstr}>_" + id ).innerHTML = "";	
			    }
			    if(document.getElementById( typ + "xincengWeight<{$qhstr}>_" + id )){
			     	document.getElementById( typ + "xincengWeight<{$qhstr}>_" + id ).innerHTML = "";	
			    }
			    if(document.getElementById( typ + "status<{$qhstr}>_" + id )){
			     	document.getElementById( typ + "status<{$qhstr}>_" + id ).innerHTML = "";	
			    }
                
                if(document.getElementById( typ + "tks_fe<{$qhstr}>_" + id )){
			     	document.getElementById( typ + "tks_fe<{$qhstr}>_" + id ).innerHTML = "";	
			    }
			    if(document.getElementById( typ + "tks_al<{$qhstr}>_" + id )){
			     	document.getElementById( typ + "tks_al<{$qhstr}>_" + id ).innerHTML = "";	
			    }
			    if(document.getElementById( typ + "tks_si<{$qhstr}>_" + id )){
			     	document.getElementById( typ + "tks_si<{$qhstr}>_" + id ).innerHTML = "";	
			    }
			    //Updated by quanjw fow jinshuzhipin end 2015/3/24
                   //add by zfy started 2019/09/26 加焊网
                   if(document.getElementById( typ + "yongtu<{$qhstr}>_" + id )){
                       document.getElementById( typ + "yongtu<{$qhstr}>_" + id ).innerHTML = "";
                   }
                   if(document.getElementById( typ + "strength<{$qhstr}>_" + id )){
                       document.getElementById( typ + "strength<{$qhstr}>_" + id ).innerHTML = "";
                   }
                   //add by zfy ended 2019/09/26 加焊网

			   }
		   } 

}

//固定购物车
function fixedgwc(){
 //   document.getElementById( "ajaxdivgwc" ).style.top = document.body.scrollTop + 500 ;
document.getElementById("ajaxdivgwc").style.top=(parseInt(document.documentElement.clientHeight,10)/2)+parseInt(document.documentElement.scrollTop,10)-120+"px";    
	//alert(document.body.scrollTop + 800);
 setTimeout("fixedgwc()",6); 
// alert("ss");
}

fixedgwc();

</script>

