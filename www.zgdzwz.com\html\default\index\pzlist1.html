<!DOCTYPE HTML>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=8" />
<title><{$page_title1}><{$page_title}><{$smarty.const.WEBNAME}></title>
<link rel="shortcut icon" href="favicon.ico" /></head>

<link type="text/css" rel="stylesheet" href="css/base.css" />
<link type="text/css" rel="stylesheet" href="css/index.css" />
<script language="javascript" type="text/javascript" src="./js/jquery.min.js"></script>

<script language="javascript" type="text/javascript" src="./js/ajaxgwc.js"></script>
		<script src="./js/swt.js" ></script>
<script language="javascript" type="text/javascript" src="./js/ajaxsendmsgbox.js"></script>

<script language="javascript" type="text/javascript" src="js/My97DatePicker/WdatePicker.js"></script>
<script src="./js/ajax.js" ></script>
<link href="css/tender.css" rel="stylesheet" type="text/css" />

<script src="./js/ajaxlogin.js" ></script>
<SCRIPT language="javascript" src="./js/opwindows3.js"></SCRIPT>
<div id="ajaxdivgwc" ></div>
 
        	<div class="productList bd_d6 oh mt10"><!--   资源列表  -->  

	

            	<div class="tabs"><!--  选项卡  -->
                    <ul>
                        <li id="wsxh_0_bar"  onMouseMove="show_wsxh_item('wsxh_0');" class="on" ><a >全部</a></li>
<{foreach from=$bigpz item=v key=k}>
						<li id="wsxh_<{$k}>_bar"  onMouseMove="show_wsxh_item('wsxh_<{$k}>');"  ><a style="text-align:center"><{$v}></a></li>
<{/foreach}>

                        <li id="wsxh_ot_bar"  onMouseMove="show_wsxh_item('wsxh_ot');" ><a href="#">其它</a></li>
                    </ul>
                </div><!--  选项卡 end  -->
                <div class="clear"></div>


 <div id="wsxh_0" style="display:block;"  >  
 <div style="height:460px">
            	<table>
                	<thead>
                    	<tr>
                        	<td width="42" align="center">选择</td>
                            <td width="75" >公司名称</td>
                            <td width="65" >品名</td>
                            <td width="50" >材质</td>
                            <td width="60" >规格</td>
                            <td width="42" >价格</td>
                            <td width="62"  align="center">成交量/资源量</td>
                            <td width="42" >生产厂家</td>
                            <td width="42" >交货地</td>
                            <td width="42" >&nbsp;&nbsp;&nbsp;在线</td>
                            <td width="40" >洽谈</td>
                        </tr>
                    </thead>
                    <tbody>
<{foreach from=$pzzy[0] item=zy key=k name="a"}>

                    	<tr  onMouseOver="jjcg2_<{$smarty.foreach.a.iteration}>.style.visibility='visible';" onMouseOut="jjcg2_<{$smarty.foreach.a.iteration}>.style.visibility='hidden';">
                        	<td align="center"><span id="jjcg2_xuanz_<{$smarty.foreach.a.iteration}>" ><input type="checkbox"  name="res_id[]" type="checkbox" value="<{$zy.ID}>"/></span></td>
                            <td><span id="jjcg2_ComNameShort_<{$smarty.foreach.a.iteration}>" ><a  href="listing.php?view=zylist&mid=<{$zy.Mid}>" target="_blank"><{$zy.ComNameShort|truncate:12:"":true:false}></a></span>
                                <dl class="pop"  id="jjcg2_<{$smarty.foreach.a.iteration}>" style="visibility:hidden;">
                                    <span></span>
                                    <dt><{$zy.ComName}></dt>
                                    <dd>
                                        <p>公司地址：<{$zy.Address}></p>
                                        <p>联系电话：<{$zy.ContactTel}></p>
                                        <p><{$smarty.const.kefu}><{$smarty.const.PHONENUM}></p>
                                    </dd>
                                </dl>
							
							</td>
                            <td><span id="jjcg2_VarietyName_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.VarietyName|truncate:12:"":true:false }></a></span></td>
                            <td><span id="jjcg2_MaterialCode_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.MaterialCode|truncate:6:"":true:false }></a></span></td>
                            <td><span id="jjcg2_SpecCode_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.SpecCode|truncate:10:"":true:false }></a></span></td>
                            <td><span id="jjcg2_SalesMinPrice_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{if $zy.SalesMinPrice !='0.00' && $zy.SalesMinPrice != ""}><{$zy.SalesMinPrice|string_format:'%.0f'}><{else}>协议价<{/if}></a></span></td>
                            <td align="center"><span id="jjcg2_QuantitySales_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.QuantitySalesed}>/<{$zy.QuantitySales}></a></span></td>
                            <td><span id="jjcg2_OriginCode_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.OriginCode|truncate:12:"":true:false}></a></span></td>
                            <td><span id="jjcg2_PickUpCity_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.PickUpCity }></a></span></td>
                            <td>
                            	<span id="jjcg2_contact_<{$smarty.foreach.a.iteration}>" >
									<a href="javascript:checkHasAndChatTo('<{$zy.CreateUser}>')"><img class="img_<{$zy.CreateUser}>" id="img_<{$zy.CreateUser}>" src="images/gmt4.gif" style="border:0;" ></a>
									<{if $zy.QQNum!=""}><a href="//wpa.qq.com/msgrd?v=3&uin=<{$zy.QQNum}>&site=qq&menu=yes"><img src="img/talk_qq.png" /></a><{/if}>
								
								</span>
                            </td>
                            <td><span id="jjcg2_liuyan_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>&talk=1"  target="_blank">留言</a></span></td>
						</tr>   
						
<{/foreach}>
                	</tbody>
                </table>
</div>

				<div class="clear"></div>
                
                <div class="select"><!--  全选  -->
                
                	<div class="fl">
                    	<label><input type="checkbox" onclick="checkallzy()"  id="check" />全选</label>
                		<a href="#"  onclick="tj( 'res_id[]' );return false;"><img src="img/btn_jrcgc.png" /></a>
                        <a href="member.php?view=jymanage" target="_blank"><img src="img/btn_fbzy.png" /></a>
                    </div>
                    
                    <div class="page fr"><span class="disabled">&lt; Prev</span>第<span  id="jjcg2_cpage"><{$page}></span>/<{$totalall}>页&nbsp;&nbsp;&nbsp;&nbsp;<a  onclick="if(page_2=='1') return false;gopre2(2,'<{$totalall}>',1);" cursor:pointer>上一页</a>&nbsp;&nbsp;　<a onclick="if(page_2=='<{$totalall}>') return false;gonext2(2,'<{$totalall}>',1);">下一页</a>&nbsp;&nbsp;&nbsp;&nbsp;跳 至&nbsp;&nbsp;<b><input  id="searchpage_2" onblur="searchpage2(2,'<{$totalall}>',1);" value=""  type="text" style="width:50px;text-align:center;height:18px; border:1px solid #ccc;"/></b>&nbsp;&nbsp;页</div>
                
                </div><!--  全选 end  -->

</div>

<{foreach from=$bigpz item=v key=k }>
 <div id="wsxh_<{$k}>" style="display:none;"   >   
 <div style="height:460px">
            	<table>
                	<thead>
                    	<tr>
                        	<td width="42" align="center">选择</td>
                            <td width="75" >公司名称</td>
                            <td width="65" >品名</td>
                            <td width="50" >材质</td>
                            <td width="60" >规格</td>
                            <td width="42" >价格</td>
                            <td width="62"  align="center">成交量/资源量</td>
                            <td width="42" >生产厂家</td>
                            <td width="42" >交货地</td>
                            <td width="42" >&nbsp;&nbsp;&nbsp;在线</td>
                            <td width="40" >洽谈</td>
                        </tr>
                    </thead>
                    <tbody>
<{foreach from=$pzzy[$k] item=zy key=k2  name="a"}>

                    	<tr onMouseOver="jjcg<{$k}>4_<{$smarty.foreach.a.iteration}>.style.visibility='visible';" onMouseOut="jjcg<{$k}>4_<{$smarty.foreach.a.iteration}>.style.visibility='hidden';" >
                        	<td align="center"><span id="jjcg<{$k}>4_xuanz_<{$smarty.foreach.a.iteration}>" ><input type="checkbox"  name="res_id[]" type="checkbox" value="<{$zy.ID}>"/></span></td>
                            <td><span id="jjcg<{$k}>4_ComNameShort_<{$smarty.foreach.a.iteration}>" ><a  href="listing.php?view=zylist&mid=<{$zy.Mid}>" target="_blank"><{$zy.ComNameShort|truncate:12:"":true:false}></a></span>				
							<dl class="pop"  id="jjcg<{$k}>4_<{$smarty.foreach.a.iteration}>" style="visibility:hidden;">
                                    <span></span>
                                    <dt><{$zy.ComName}></dt>
                                    <dd>
                                        <p>公司地址：<{$zy.Address}></p>
                                        <p>联系电话：<{$zy.ContactTel}></p>
                                        <p><{$smarty.const.kefu}><{$smarty.const.PHONENUM}></p>
                                    </dd>
                                </dl>
							</td>
                            <td><span id="jjcg<{$k}>4_VarietyName_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.VarietyName|truncate:12:"":true:false }></a></span></td>
                            <td><span id="jjcg<{$k}>4_MaterialCode_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.MaterialCode|truncate:6:"":true:false }></a></span></td>
                            <td><span id="jjcg<{$k}>4_SpecCode_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.SpecCode|truncate:10:"":true:false }></a></span></td>
                            <td><span id="jjcg<{$k}>4_SalesMinPrice_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{if $zy.SalesMinPrice !='0.00' && $zy.SalesMinPrice != ""}><{$zy.SalesMinPrice|string_format:'%.0f'}><{else}>协议价<{/if}></a></span></td>
                            <td align="center"><span id="jjcg<{$k}>4_QuantitySales_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.QuantitySalesed}>/<{$zy.QuantitySales}></a></span></td>
                            <td><span id="jjcg<{$k}>4_OriginCode_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.OriginCode|truncate:12:"":true:false}></a></span></td>
                            <td><span id="jjcg<{$k}>4_PickUpCity_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.PickUpCity }></a></span></td>
                            <td>
                            	<span id="jjcg<{$k}>4_contact_<{$smarty.foreach.a.iteration}>" >
									<a href="javascript:checkHasAndChatTo('<{$zy.CreateUser}>')"><img class="img_<{$zy.CreateUser}>" id="img_<{$zy.CreateUser}>" src="images/gmt4.gif" style="border:0;" ></a>
									<{if $zy.QQNum!=""}><a href="//wpa.qq.com/msgrd?v=3&uin=<{$zy.QQNum}>&site=qq&menu=yes"><img src="img/talk_qq.png" /></a><{/if}>
								</span>
                            </td>
                            <td><span id="jjcg<{$k}>4_liuyan_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>&talk=1"  target="_blank">留言</a></span></td>
						</tr>   
						
<{/foreach}>
                	</tbody>
                </table>
</div>
				<div class="clear"></div>
                
                <div class="select"><!--  全选  -->
                
                	<div class="fl">
                    	<label><input type="checkbox" onclick="checkallzy()"  id="check" />全选</label>
                		<a href="#"  onclick="tj( 'res_id[]' );return false;"><img src="img/btn_jrcgc.png" /></a>
                        <a href="member.php?view=jymanage" target="_blank"><img src="img/btn_fbzy.png" /></a>
                    </div>
                    
                    <div class="page fr"><span class="disabled">&lt; Prev</span>第<span id="jjcg<{$k}>4_cpage"><{$page}></span>/<{$totalpzz[$k]}>页&nbsp;&nbsp;&nbsp;&nbsp;<a  onclick="if(page_<{$k}>4=='1') return false;gopre2(8,'<{$totalpzz[$k]}>',2,'<{$k}>4','<{$k}>');">上一页</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a onclick="if(page_<{$k}>4=='<{$totalpzz[$k]}>') return false;gonext2(8,'<{$totalpzz[$k]}>',2,'<{$k}>4','<{$k}>');">下一页</a>&nbsp;&nbsp;&nbsp;&nbsp;跳 至&nbsp;&nbsp;<b><input  id="searchpage_<{$k}>4" onblur="searchpage2(8,'<{$totalpzz[$k]}>',2,'<{$k}>4','<{$k}>');" value=""  type="text" style="width:50px;text-align:center; height:18px; border:1px solid #ccc;"/></b>&nbsp;&nbsp;页</div>



                </div><!--  全选 end  -->

</div>
<{/foreach}>	

 <div id="wsxh_ot" style="display:none;"   >   
 <div style="height:460px">
            	<table>
                	<thead>
                    	<tr>
                        	<td width="42" align="center">选择</td>
                            <td width="75" >公司名称</td>
                            <td width="65" >品名</td>
                            <td width="50" >材质</td>
                            <td width="60" >规格</td>
                            <td width="42" >价格</td>
                            <td width="62"  align="center">成交量/资源量</td>
                            <td width="42" >生产厂家</td>
                            <td width="42" >交货地</td>
                            <td width="42" >&nbsp;&nbsp;&nbsp;在线</td>
                            <td width="40" >洽谈</td>
                        </tr>
                    </thead>
                    <tbody>
<{foreach from=$other item=zy key=k  name="a"}>

                    	<tr onMouseOver="jjcg6_<{$smarty.foreach.a.iteration}>.style.visibility='visible';" onMouseOut="jjcg6_<{$smarty.foreach.a.iteration}>.style.visibility='hidden';">
                        	<td align="center"><span id="jjcg6_xuanz_<{$smarty.foreach.a.iteration}>" ><input type="checkbox"  name="res_id[]" type="checkbox" value="<{$zy.ID}>"/></span></td>
                            <td><span id="jjcg6_ComNameShort_<{$smarty.foreach.a.iteration}>" ><a  href="listing.php?view=zylist&mid=<{$zy.Mid}>" target="_blank"><{$zy.ComNameShort|truncate:12:"":true:false}></a></span>
							<dl class="pop"  id="jjcg6_<{$smarty.foreach.a.iteration}>" style="visibility:hidden;">
                                    <span></span>
                                    <dt><{$zy.ComName}></dt>
                                    <dd>
                                        <p>公司地址：<{$zy.Address}></p>
                                        <p>联系电话：<{$zy.ContactTel}></p>
                                        <p><{$smarty.const.kefu}><{$smarty.const.PHONENUM}></p>
                                    </dd>
                                </dl>				
							</td>
                            <td><span id="jjcg6_VarietyName_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.VarietyName|truncate:12:"":true:false }></a></span></td>
                            <td><span id="jjcg6_MaterialCode_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.MaterialCode|truncate:6:"":true:false }></a></span></td>
                            <td><span id="jjcg6_SpecCode_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.SpecCode|truncate:8:"":true:false }></a></span></td>
                            <td><span id="jjcg6_SalesMinPrice_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{if $zy.SalesMinPrice !='0.00' && $zy.SalesMinPrice != ""}><{$zy.SalesMinPrice|string_format:'%.0f'}><{else}>协议价<{/if}></a></span></td>
                            <td align="center"><span id="jjcg6_QuantitySales_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.QuantitySalesed}>/<{$zy.QuantitySales}></a></span></td>
                            <td><span id="jjcg6_OriginCode_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.OriginCode|truncate:12:"":true:false}></a></span></td>
                            <td><span id="jjcg6_PickUpCity_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>" target="_blank"><{$zy.PickUpCity }></a></span></td>
                            <td>
                            	<span id="jjcg6_contact_<{$smarty.foreach.a.iteration}>" >
									<a href="javascript:checkHasAndChatTo('<{$zy.CreateUser}>')"><img class="img_<{$zy.CreateUser}>" id="img_<{$zy.CreateUser}>" src="images/gmt4.gif" style="border:0;" ></a>
									<{if $zy.QQNum!=""}><a href="//wpa.qq.com/msgrd?v=3&uin=<{$zy.QQNum}>&site=qq&menu=yes"><img src="img/talk_qq.png" /></a><{/if}>
								</span>
                            </td>
                            <td><span id="jjcg6_liuyan_<{$smarty.foreach.a.iteration}>" ><a href="index.php?view=jinjia_info&cat=<{$zy.SalesType}>&id=<{$zy.ID}>&talk=1"  target="_blank">留言</a></span></td>
						</tr>   
						
<{/foreach}>
                	</tbody>
                </table>
</div>


				<div class="clear"></div>
                
                <div class="select"><!--  全选  -->
                
                	<div class="fl">
                    	<label><input type="checkbox" onclick="checkallzy()"  id="check" />全选</label>
                		<a href="#"  onclick="tj( 'res_id[]' );return false;"><img src="img/btn_jrcgc.png" /></a>
                        <a href="member.php?view=jymanage" target="_blank"><img src="img/btn_fbzy.png" /></a>
                    </div>
                    
                    <div class="page fr"><span class="disabled">&lt; Prev</span>第<span  id="jjcg6_cpage"><{$page}></span>/<{$totalot}>页&nbsp;&nbsp;&nbsp;&nbsp;<a  onclick="if(page_6=='1') return false;gopre2(6,'<{$totalot}>',3);">上一页</a>&nbsp;&nbsp;　<a onclick="if(page_6=='<{$totalot}>') return false;gonext2(6,'<{$totalot}>',3);">下一页</a>&nbsp;&nbsp;&nbsp;&nbsp;跳 至&nbsp;&nbsp;<b><input  id="searchpage_6" onblur="searchpage2(6,'<{$totalot}>',3);" value=""  type="text" style="width:50px;text-align:center; height:18px; border:1px solid #ccc;"/></b>&nbsp;&nbsp;页</div>
                
                </div><!--  全选 end  -->
            	
 </div>    
 <input type="hidden" id="params" value="<{$str}>">

<script>
function show_cgbj_item(item){

<{foreach from=$bigpz item=v key=k}>
	
	document.getElementById("cgbj_<{$k}>").style.display = "none";
	document.getElementById("cgbj_<{$k}>_bar").className = "";

<{/foreach}>
	document.getElementById("cgbj_ot").style.display = "none";
	document.getElementById("cgbj_ot_bar").className = "";

	document.getElementById("cgbj_0").style.display = "none";
	document.getElementById("cgbj_0_bar").className = "";

document.getElementById(item).style.display = "block";	

	var item_bar=item+"_bar";
	document.getElementById(item_bar).className="on";		


}

function show_wsxh_item(item){

<{foreach from=$bigpz item=v key=k}>
	
	document.getElementById("wsxh_<{$k}>").style.display = "none";
	document.getElementById("wsxh_<{$k}>_bar").className = "";

<{/foreach}>
	document.getElementById("wsxh_ot").style.display = "none";
	document.getElementById("wsxh_ot_bar").className = "";

	document.getElementById("wsxh_0").style.display = "none";
	document.getElementById("wsxh_0_bar").className = "";

document.getElementById(item).style.display = "block";	

	var item_bar=item+"_bar";
	document.getElementById(item_bar).className="on";		


}
 
function show_wsjp_item(item){

<{foreach from=$bigpz item=v key=k}>
	
	document.getElementById("wsjp_<{$k}>").style.display = "none";
	document.getElementById("wsjp_<{$k}>_bar").className = "";

<{/foreach}>
	document.getElementById("wsjp_ot").style.display = "none";
	document.getElementById("wsjp_ot_bar").className = "";

	document.getElementById("wsjp_0").style.display = "none";
	document.getElementById("wsjp_0_bar").className = "";

document.getElementById(item).style.display = "block";	

	var item_bar=item+"_bar";
	document.getElementById(item_bar).className="on";		


}

function show_ccys_item(item){

<{foreach from=$bigpz item=v key=k}>
	
	document.getElementById("ccys_<{$k}>").style.display = "none";
	document.getElementById("ccys_<{$k}>_bar").className = "";

<{/foreach}>
	document.getElementById("ccys_ot").style.display = "none";
	document.getElementById("ccys_ot_bar").className = "";

	document.getElementById("ccys_0").style.display = "none";
	document.getElementById("ccys_0_bar").className = "";

document.getElementById(item).style.display = "block";	

	var item_bar=item+"_bar";
	document.getElementById(item_bar).className="on";		


}



</script>
<script src="/js/ajax.js" ></script>
<script src="/js/common.js" ></script>
<script charset="gbk2312">
var page = 1; 
var page_1 = 1; 
var page_2 = 1; 
var page_3 = 1; 
var page_4 = 1; 
var page_5 = 1; 
var page_6 = 1; 
var page_7 = 1; 

var page_14 = 1; 
var page_24 = 1; 
var page_34 = 1; 
var page_44 = 1; 
var page_54 = 1; 
var page_64 = 1; 
var page_74 = 1; 

var page_gcxs = 1;
var page_gccg = 1;
var page_sjxs = 1;
var page_sjcg = 1;
var page_x = 1;



function gopre2(tab,total,types,d,k){ //去上一页


	if(d == "gcxs"){
		var nextpage = parseInt( page_gcxs ) - 1;
		page_gcxs= nextpage;
	}
	if(d == "gccg" ){
		var nextpage = parseInt( page_gccg ) - 1;
		page_gccg= nextpage;
	}
	if(d == "sjxs" ){
		var nextpage = parseInt( page_sjxs ) - 1;
		page_sjxs= nextpage;
	}
	if( d == "sjcg" ){
		var nextpage = parseInt( page_sjcg ) - 1;
		page_sjcg= nextpage;
	}

	if(tab==2){ 
		var prepage = parseInt( page_2 ) - 1;
		page_2= prepage;
	}
		
	if(tab==6){
		var prepage = parseInt( page_6 ) - 1;
		page_6= prepage;			
	}
	
	if(tab==1){
		var prepage = parseInt( page_1 ) - 1;
		page_1= prepage;			
	}
	if(tab==5){
		var prepage = parseInt( page_5 ) - 1;
		page_5= prepage;			
	}
	if(tab==7){
		var prepage = parseInt( page_x ) - 1;
		page_x= prepage;
	}
	if(tab==8){
		var prepage = parseInt( page_x ) - 1;
		page_x= prepage;
	}

	if( prepage < 1 ){
		return false;
	} 
	//page = prepage;
	cd_type=tab;
	vid=types;
	mainsearch2(tab,d,k);

}

function gonext2(tab,total,types,d,k){ //去下一页


		if(tab==1){
			var nextpage = parseInt( page_1 ) + 1;
			page_1= nextpage;			
		}
		if(tab==2){
			var nextpage = parseInt( page_2 ) + 1;
			page_2= nextpage;
		}
		if(tab==5){
			var nextpage = parseInt( page_5 ) + 1;
			page_5= nextpage;
		}
		if(tab==6){
			var nextpage = parseInt( page_6 ) + 1;
			page_6= nextpage;
		}
		if(tab==7){
			var nextpage = parseInt( page_x ) + 1;
			page_x= nextpage;
		}
		if(tab==8){
			var nextpage = parseInt( page_x ) + 1;
			page_x= nextpage;
		}



	// alert(nextpage);
	//var nextpage = parseInt( page ) + 1;
	if( nextpage  > total ){
		return false;
	} 
	//page= nextpage;
	cd_type=tab;
	vid=types;
	mainsearch2(tab,d,k);
}


function searchpage2(tab,total,types,d,k){
//alert("s");
			if(tab==7 || tab==8 || d == "gcxs" || d == "gccg" || d == "sjxs"  || d == "sjcg" ){
				var p = document.getElementById( "searchpage_"+d ).value;
			//	var page_x = "page_" + d;
			}else{
				var p = document.getElementById( "searchpage_"+tab ).value;
			}
			


		//	alert(x);
			if( p == '' ){
				return false;
			}
			
			if( isNaN( p ) || p == '' ){
			    alert( "请输入合法数字" );
				return false;
			}
			p = parseInt( p );
			if( p < 1 || p > total ){
			    alert( "请输入合法的页码" );
				return false;
			}
			if(tab==1){  page_1 = p;}
			if(tab==2){  page_2 = p;}	
			if(tab==7 || tab==8){ page_x = p; }			
			if(tab==5){  page_5 = p;}
			if(tab==6){  page_6 = p;}

//
			if(d == "gcxs" ){ page_gcxs= p;}
			if(d == "gccg" ){ page_gccg= p;}	
			if(d == "sjxs" ){ page_sjxs= p;}	
			if(d == "sjcg" ){ page_sjcg= p;}
           // page = p;
			cd_type=tab;
			vid=types;
			mainsearch2(tab,d,k);

		}



function mainsearch2(tab,d,k){	

	if(tab==1){ var pages = page_1;}
	if(tab==2){ var pages = page_2;}

	if(tab==7 || tab==8){var pages = page_x;}	

	if(tab==5){ var pages = page_5;}
	if(tab==6){ var pages = page_6;}

	if(  d == "gcxs"){var pages = page_gcxs;}
	if( d == "gccg"){var pages = page_gccg;}	
	if(  d == "sjxs"){var pages = page_sjxs;}	
	if( d == "sjcg"){var pages = page_sjcg;}	

	//tab==3if(tab==3){ var pages = x;}

 // var p = document.getElementById( "comname").value;
var ss=encodeURIComponent("<{$params.comname}>");
//alert("<{$params.comname}>");
	var param = "view=ajaxgetindexinfo&page=" + pages + "&type=" + cd_type +"&vid=" + vid +"&d="+d+"&k="+k +"<{$str}>" ;

//alert(param);

	var ajax = new Ajax( "index.php", setData2, param );

}
function setData2( returnStr ){
	//	 alert(returnStr);

		   var tmptype = returnStr.split("|T|");
		   var type = tmptype[0];
		    var tmp = tmptype[2].split( "|H|" );
		    var typ="";

			var typx= tmptype[1];
			//var x="page"+tmptype[1]+"_";

		 if(type == 1){typ="jjxs1_";}		
		 if(type == 2){typ="jjcg2_";}
		 if(type == 5){typ="jjxs5_";}
		 if(type == 6){typ="jjcg6_";}
		 if(type == 7 ){typ="jjxs"+tmptype[1]+"_";}
		 if(type == 8){typ="jjcg"+tmptype[1]+"_";}

		 if(typx == "gcxs" || typx == "gccg" || typx == "sjxs"  || typx == "sjcg" ){
			typ =tmptype[1]+"_";
		 }
// alert(typ);
		   var records = tmp[0];
		 
		   if( records == '' ){
		   		for( var i = 0; i < 10; i++ ){
		         var id = i + 1;
				}
		      // waitover();
			   return false;
		   }
	   
		   var others = tmp[1];
		   records = records.split( "|O|" );
		  // alert(typ);

		   tmp = others.split( "|A|" );
		   page = tmp[1];
		 //  alert(page);
		   totalpage = tmp[0];
		   document.getElementById(typ + "cpage" ).innerHTML = page;
		  // document.getElementById(typ + "cpages" ).innerHTML = page;
// alert(typeof(records[99])); 	


		   for( var i = 0; i < 12; i++ ){
			   var id = i + 1;		
			  // alert(records[i]);
			   if( typeof(records[i]) != 'undefined' ){
				
		         var tmp = records[i].split( "|X|" );	
				//alert(typ + "contact" + id  );
				 document.getElementById( typ + "xuanz_" + id ).innerHTML = tmp[13];
			     document.getElementById( typ + "ComNameShort_" + id ).innerHTML = tmp[0];		
			     document.getElementById( typ + "VarietyName_" + id ).innerHTML = tmp[1];
			     document.getElementById( typ + "MaterialCode_" + id ).innerHTML = tmp[2];
			     document.getElementById( typ + "SpecCode_" + id ).innerHTML = tmp[3];
			     document.getElementById( typ + "OriginCode_" + id ).innerHTML = tmp[4];
			     document.getElementById( typ + "PickUpCity_" + id ).innerHTML = tmp[5];
			     document.getElementById( typ + "SalesMinPrice_" + id ).innerHTML = tmp[6];
				 document.getElementById( typ + "QuantitySales_" + id ).innerHTML = tmp[7];
			    // document.getElementById( typ + "QuantitySalesed_" + id ).innerHTML = tmp[8];
			    // document.getElementById( typ + "fbdate_" + id ).innerHTML = tmp[9];
				// document.getElementById( typ + "jjtype_" + id ).innerHTML = tmp[10];
				 document.getElementById( typ + id ).innerHTML = tmp[11];
				 document.getElementById( typ + id ).style.display = "block";
				// document.getElementById( typ + id + "_2").innerHTML = tmp[12];
				document.getElementById( typ + "contact_" + id ).innerHTML = tmp[14];
				document.getElementById( typ + "liuyan_" + id ).innerHTML = tmp[15];
				 

			   }
			   if( typeof(records[i]) == 'undefined' ){
				 document.getElementById( typ + "xuanz_" + id ).innerHTML = '';
			     document.getElementById( typ + "ComNameShort_" + id ).innerHTML = '';				
			     document.getElementById( typ + "VarietyName_" + id ).innerHTML = '';
			     document.getElementById( typ + "MaterialCode_" + id ).innerHTML = '';
			     document.getElementById( typ + "SpecCode_" + id ).innerHTML = '';
			     document.getElementById( typ + "OriginCode_" + id ).innerHTML = '';
			     document.getElementById( typ + "PickUpCity_" + id ).innerHTML = '';
			     document.getElementById( typ + "SalesMinPrice_" + id ).innerHTML = '';
				 document.getElementById( typ + "QuantitySales_" + id ).innerHTML = '';
			   //  document.getElementById( typ + "QuantitySalesed_" + id ).innerHTML = '';
				// document.getElementById( typ + "fbdate_" + id ).innerHTML = '';
			    // document.getElementById( typ + "jjtype_" + id ).innerHTML = '';
				 document.getElementById( typ + id ).innerHTML = '';
				 document.getElementById( typ + id ).style.display = "none";
				// document.getElementById( typ + id + "_2" ).style ="none";
				// document.getElementById( typ + id ).style = "position:absolute;left:580px;padding-top:0px;width:290px;  visibility:hidden;";
				// document.getElementById( typ + id + "_2" ).style  = "position:absolute;left:580px;padding-top:0px;width:290px;  visibility:hidden;";
				document.getElementById( typ + "contact_" + id ).innerHTML = '';
				document.getElementById( typ + "liuyan_" + id ).innerHTML = '';
			   }
		   } 

}


function show_ww(item){
	    document.getElementById("zxgg").style.display = "none";
		document.getElementById("sell").style.display = "none";
		document.getElementById("buy").style.display = "none";
		document.getElementById("cjyb").style.display = "none";

	    document.getElementById(item).style.display = "block";

 		document.getElementById("zxgg_tag").className = "";
		document.getElementById("sell_tag").className = "";
		document.getElementById("buy_tag").className = "";
		document.getElementById("cjyb_tag").className = "";

				
		document.getElementById(item+"_tag").className = "vv";

}



//固定购物车
function fixedgwc(){
 //   document.getElementById( "ajaxdivgwc" ).style.top = document.body.scrollTop + 500 ;
document.getElementById("ajaxdivgwc").style.top=(parseInt(document.documentElement.clientHeight,10)/2)+parseInt(document.documentElement.scrollTop,10)-120+"px";    
	//alert(document.body.scrollTop + 800);
 setTimeout("fixedgwc()",6); 
// alert("ss");
}

fixedgwc();

</script>

