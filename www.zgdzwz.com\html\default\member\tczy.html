<{include file="../header.html" }>

<script language="javascript" type="text/javascript" src="js/My97DatePicker/WdatePicker.js"></script>

<script src="./js/ajax.js" ></script>
<script src="./js/ajaxRequest.js" ></script>
<script src="./js/ajaxfrom.js?a="+Math.random() ></script>
 <script src="./js/ajaxupload.js" ></script>

 <script src="./js/ajaxtcexcupload.js" ></script>	
 <script src="./js/jquery.min.js" ></script>
  <script src="./js/ajaxpage.js" ></script>	
  <!-- <script src="./js/ajaxtender.js" ></script>	 -->

<script type="text/javascript" src="./js/tinybox.js"></script>
<script type="text/javascript" src="./js/ajaxsendmsgbox.js" ></script>
<link type="text/css" rel="stylesheet" href="css/resourceInfo.css" />
<link type="text/css" rel="stylesheet" href="css/autolist.css" />
<script src="./js/newautolist.js" ></script>
 <!--Added for quanxian sheding(leftmenu)  by hxy started  2015/1/13-->
<script src="./js/md5.js" ></script>	
 <!--Added for quanxian sheding(leftmenu)  by hxy ended  2015/1/30-->
<style type="text/css">
<!--

#mini_nava{border:1px solid #bad6e3; background-color:#edf9ff; height:30px; line-height:30px;}
#mini_nava ul {float:left; list-style-type:none;}
#mini_nava li {float:left; padding:0 4px 0 8px; font-size:13px;}
.cbox{border:1px solid #bad6e3; background-color:#edf9ff; }
.cut3{float:left; width:78px; background-color:#fff; line-height:30px; border-right:1px solid #bad6e3; border-bottom:1px solid #fff;font-size:14px; font-weight:bold; color:#000;}
.cut4{float:left; width:78px; font-size:14px; line-height:30px; font-weight:bold;border-right:1px solid #bad6e3; color:#666;}
.StatusCut1{float:left; width:78px; background-color:#fff; line-height:30px; border-right:1px solid #bad6e3; border-bottom:1px solid #fff;font-size:14px; font-weight:bold; color:#000;cursor:pointer; }
.StatusCut2{float:left; width:78px; font-size:14px; line-height:30px; font-weight:bold;border-right:1px solid #bad6e3; color:#666;cursor:pointer;}
.dxbox{background-color:#fff; margin:0px 5px; padding:0 5px; line-height:22px;}
-->
</style>
<style>
div.loading{
	position: fixed;
	top: 50%;
	left: 50%;
	width: 220px;
	height: 48px;
	padding: 19px 0 0 36px;
	margin: -33px 0 0 -128px;
	border: 1px solid #d6d6d6;
	background: #fff;
	z-index: 999;
	line-height: 30px;
	-webkit-box-shadow: 0 0 3px #ccc;
	-moz-box-shadow: 0 0 3px #ccc;
	box-shadow: 0 0 3px #ccc;
}
div.loading img{
	float: left;
	vertical-align: middle;
}
div.loading span{
	float: left;
	margin-left: 10px;
	vertical-align: middle;
}

</style>
<div class="loading" id="loading" style="display:none;">
	<img src="img/loading.gif" />
    <span>正在提交中，请稍后...</span>
</div>

<div id="container" class="container"><!--------------------------  container  ------------------------------>

	<div class="location"><!--  当前位置  -->
    	<span style="margin-right: -3px;">当前位置：</span>
        <span>会员中心</span> > 
        <span>销售与采购管理</span>
    </div><!--  当前位置 end  -->
    <div class="clear"></div>
    
    <div class="progressBar wrap mt30"><!--  进度条  -->
    	<!--<ul>
 <{if  $zy.SalesType == "1" ||  $zy.SalesType == "5" || $params.type == "2"}>
        	<dl <{if !$params.id}>class="on"<{/if}>>
            	<span>1</span>
            	<dt>销售资源发布</dt>
                <dd></dd>
            </dl>
        	<dl <{if $zy.Status == "1"}>class="on"<{/if}>>
            	<span>2</span>
            	<dt>上架</dt>
                <dd></dd>
            </dl>
        	<dl <{if $zy.Status == "2" }> class="on"<{/if}>>
            	<span>3</span>
            	<dt>订购中</dt>
                <dd></dd>
            </dl>
        	<dl <{if $zy.Status == "2" || $zy.Status == "8" || $zy.Status == "9"}>class="on"<{/if}>>
            	<span>4</span>
            	<dt>订购确认<{$zy.Status}></dt>
                <dd></dd>
            </dl>
        	<dl>
            	<span>5</span>
            	<dt>合同确认</dt>
                <dd></dd>
            </dl>
        	<dl>
            	<span>6</span>
            	<dt>合同执行</dt>
                <dd></dd>
            </dl>
        	<dl>
            	<span>7</span>
            	<dt>完成交割</dt>
                <dd></dd>
            </dl>
<{/if}>

<{if  $zy.SalesType == "2" || $zy.SalesType == "6" || $params.type == "4" }>
        	<dl <{if !$params.id}>class="on"<{/if}>>
            	<span>1</span>
            	<dt>采购资源发布</dt>
                <dd></dd>
            </dl>
        	<dl <{if $zy.Status == "1"}>class="on"<{/if}>>
            	<span>2</span>
            	<dt>上架</dt>
                <dd></dd>
            </dl>
        	<dl <{if $zy.Status == "2" }> class="on"<{/if}>>
            	<span>3</span>
            	<dt>订购中</dt>
                <dd></dd>
            </dl>
        	<dl <{if $zy.Status == "2" || $zy.Status == "8" || $zy.Status == "9"}>class="on"<{/if}>>
            	<span>4</span>
            	<dt>订购确认</dt>
                <dd></dd>
            </dl>
        	<dl>
            	<span>5</span>
            	<dt>合同确认</dt>
                <dd></dd>
            </dl>
        	<dl>
            	<span>6</span>
            	<dt>合同执行</dt>
                <dd></dd>
            </dl>
        	<dl>
            	<span>7</span>
            	<dt>完成交割</dt>
                <dd></dd>
            </dl>
<{/if}>
        </ul>-->
		
		
		<ul  id="xkhh" <{if $zy.fkfs!=4}>style="display:none;"<{/if}>>
		<dl class="on" style="width:110px;">
            	<span>1</span>
            	<dt>资源发布</dt>
            </dl>
        	<dl style="width:110px;">
            	<span>2</span>
            	<dt>草约生成</dt> 
            </dl>
        	<dl style="width:110px;">
            	<span>3</span>
            	<dt>订单洽谈</dt>
            </dl>
        	<dl style="width:110px;" >
            	<span>4</span>
            	<dt>电子合同</dt>
            </dl>
        	
        	<dl style="width:110px;">
            	<span>5</span>
            	<dt>发货</dt>
            </dl>
			<dl style="width:110px;">
            	<span>6</span>
            	<dt>收货</dt>
            </dl>
        	<dl style="width:110px;">
            	<span>7</span>
            	<dt>结算</dt>
            </dl>
			<dl style="width:110px;">
            	<span>8</span>
            	<dt>付款</dt>
            </dl>
		</ul>
		
		<ul  id="xhhk" <{if $zy.fkfs==4}>style="display:none;"<{/if}> >
		<dl class="on">
            	<span>1</span>
            	<dt>资源发布</dt>
            </dl>
        	<dl >
            	<span>2</span>
            	<dt>草约生成</dt> 
            </dl>
        	<dl>
            	<span>3</span>
            	<dt>订单洽谈</dt>
            </dl>
        	<dl >
            	<span>4</span>
            	<dt>电子合同</dt>
            </dl>
        	<dl >
            	<span>5</span>
            	<dt>付款</dt>
            </dl>
        	<dl >
            	<span>6</span>
            	<dt>发货</dt>
            </dl>
			<dl >
            	<span>7</span>
            	<dt>收货</dt>
            </dl>
        	<dl >
            	<span>8</span>
            	<dt>结算</dt>
            </dl>
			<dl >
            	<span>9</span>
            	<dt>付尾款</dt>
            </dl>
		</ul>
		
		
    </div><!--  进度条 end  -->
    <div class="clear"></div>
    
<div>
   <div>



    <div class="dxbox" style="background-color:#e5f3ff; padding:10px; margin:10px;<{if $params.id || $params.type }>display:none;<{/if}>" id ="jbdiv" >
    
    <p><strong>基础信息</strong>&nbsp;<a href=javascript:void(0) onclick='closejb()' style="color:#0075e1;">[关闭]</a><p/>
    
    <div style="background-color:#eef9ff; border:1px solid #c8d8e1; margin:0 3px; padding:10px; line-height:22px;">
    <table border="0"  cellspacing="0" width="100%" style="margin:10px;">
     <tr>
        <td align="right" width="10%" ><span style="color:#F00;"></span>发布资源类型：</td>
    <td width="90%">
    
    
    
    
    
    <input type="hidden" value="<{$params.id}>" id="typeid">
         </td>
         </tr>
     <tr>
        <td align="right" width="10%" ><span style="color:#F00;"></span>选择销售方式：</td>
    <td width="90%">
    <div id="saletype">    
    </div>   
    </td>
      </tr>    
    </table>   
    <img src="images/xyb.gif" type="button" style="cursor: hand;" width="114" height="30" onclick="gettypediv();" />
    </div>
    
    </div>

<!--div id="showjb" class="dxbox" <{if $params.id || $params.type}>style="display:block; "<{else}>style="display:none; "<{/if}> >

     <p><strong>基础信息</strong>&nbsp;<{if !$params.id && !$params.type}><a href=javascript:void(0) onclick='editjb()' style="color:#0075e1;">[修改]</a><{/if}><p/>
	 <{if $params.id}>
     <p>发布资源类型： <{$zytype[$zy.SalesType]}></p>
	 <p>销售资源方式： <{$zyalltypes[$zy.TradeType]}></p>
    <{else}>
<{if $params.type == "1"}>    
     <p>发布资源类型： 竞价销售</p>
	 <p>销售资源方式： 锁定价格</p>
<{/if}>
<{if $params.type == "2"}>    
     <p>发布资源类型： 竞价销售</p>
	 <p>销售资源方式： 增价竞买</p>
<{/if}>
<{if $params.type == "3"}>    
     <p>发布资源类型： 竞价采购</p>
	 <p>销售资源方式： 锁定价格</p>
<{/if}>
<{if $params.type == "4"}>    
     <p>发布资源类型： 竞价采购</p>
	 <p>销售资源方式： 减价竞卖</p>
<{/if}>
<{if $params.type == "5"}>    
     <p>发布资源类型： 远期协议销售</p>
	 <p>销售资源方式： 锁定价格</p>
<{/if}>

<{if $params.type == "6"}>    
     <p>发布资源类型： 远期协议销售</p>
	 <p>销售资源方式： 协议价</p>
<{/if}>

<{if $params.type == "7"}>    
     <p>发布资源类型： 现货销售</p>
	 <p>销售资源方式： 锁定价格</p>
<{/if}>

<{if $params.type == "8"}>    
     <p>发布资源类型： 现货采购</p>
	 <p>销售资源方式： 锁定价格</p>
<{/if}>


	<{/if}>
	 


		
</div>

<div style="border-bottom:1px dashed #e3e5e6; margin:10px 0 10px 0;"></div-->  


<!---->

<{if ($params.type == "12" || $zy.TradeType == "12")}>  

<div id="showxy" class="dxbox" <{if   $zy.Status != "2" &&  $zy.Status != "8" && $zy.Status != "9"}> style="display:none; " <{/if}>>


     <p><strong>协议信息</strong>&nbsp;<{if $zy.Status != "2" &&  $zy.Status != "8" && $zy.Status != "9"}><a href=javascript:void(0) onclick="editdiv('xy')" style="color:#0075e1;">[修改]</a><{/if}><p/>
	 <div style="height:250px; overflow:auto">


<P>&nbsp;&nbsp; 竞价资源发布方（以下简称甲方）：<{$smarty.session.SYS_COMPANYNAME}></P>
<P>&nbsp;&nbsp;参与竞价方（以下简称乙方）：</P>
<P>&nbsp;&nbsp;根据《中华人民共和国合同法》及相关法律法规的规定，甲乙双方依照平等互利的原则，为明确双方的权利义务关系，经双方友好协商，达成如下协议，供双方遵守执行。</P>
<P>&nbsp;&nbsp;第一条、甲方指定本交易保证金 <span  id="JyBzj2"> <{$xy.JyBzj}></span>元，乙方汇入钢之家超越帐号，开户行中国建设银行股份有限公司上海东方路支行，账户名称<{$smarty.const.WEBNAME}>，帐号31001587615050024351.
<BR>&nbsp;&nbsp;第二条、甲方确认竞价成功后乙方于<span  id="PayDays2" ><{$xy.PayDays}></span>个工作日内付足全额货款到甲方指定账户。甲方账户名称<span  id="AccountName2"><{$xy.AccountName}></span>，开户行<span  id="AccountBank2"><{$xy.AccountBank}></span>，帐号<span  id="PayAccount2"><{$xy.PayAccount}></span>。
<BR>&nbsp;&nbsp;第三条、甲方收到货款后<span  id="JhDays2" ><{$xy.JhDays}></span>个工作日内转移货权至乙方。
<BR>&nbsp;&nbsp;第四条、甲方收到货款后<span  id="KpDays2" ><{$xy.KpDays}></span>个工作日内开具增值税发票给乙方。
<BR>&nbsp;&nbsp;第五条、价格为甲方指定仓库自提价，出库费、运费等相关费用均由乙方承担。
<BR>&nbsp;&nbsp;第六条：<{$smarty.const.WEBNAME}>按交易量向乙方收取交易服务费0元/吨。
<BR>&nbsp;&nbsp;第七条、本协议所指款项均为现款。
<BR>&nbsp;&nbsp;第八条、验收质量标准：执行生产厂家出厂标准。
<BR>&nbsp;&nbsp;第九条、提出异议的期限：根据产品生产厂家的约定执行。
<BR>&nbsp;&nbsp;第十条、违约责任
<BR>&nbsp;&nbsp;乙方逾期付款，本协议自动终止，<{$smarty.const.WEBNAME}>在扣除10%的交易保证金后，剩余90%的交易保证金用于偿付甲方的损失。
<BR>&nbsp;&nbsp;如有其它违约，由违约方承担责任，依照《中国人民共和国协议法》的相关规定执行。
<BR>&nbsp;&nbsp;第十一条、争议的解决方式
<BR>&nbsp;&nbsp;双方在执行本协议过程中发生争议的，由双方协商解决，协商不成的，提交协议签订地的人民法院诉讼解决。
<BR>&nbsp;&nbsp;第十二条、其它约定事项：
<BR>&nbsp;&nbsp;任何一方由于不可抗力的原因不能履行协议时，应当及时向对方通报，在取得相关证明以后，可部分或者全部免予承担违约责任。本协议所称不可抗力是指双方在订立协议时不能预见并且不能避免、不能克服的客观情况，包括重大自然灾害、战争等。
<BR>&nbsp;&nbsp;第十三条本协议在<{$smarty.const.WEBNAME}>点击确认提交竞价后协议生效。</P>



						  </div>
    
</div>

  <div class="dxbox" style="background-color:#e5f3ff; padding:10px; margin:10px;<{if $zy.Status != 2 &&  $zy.Status != "8" && $zy.Status != "9"}>display:block<{else}>display:none<{/if}>;" id ="xydiv"  >
      <form action="member.php?action=addxy" method="post" name="xyform"  >
	  <p><strong>协议信息</strong>&nbsp;<a href=javascript:void(0) onclick="closediv('xy')" style="color:#0075e1;">[关闭]</a><p/>  
      
	  <div style="background-color:#eef9ff; border:1px solid #c8d8e1; margin:0 3px; padding:10px; line-height:22px; height:250px; overflow:auto">


<P>&nbsp;&nbsp; 竞价资源发布方（以下简称甲方）：<{$smarty.session.SYS_COMPANYNAME}></P>
<P>&nbsp;&nbsp;参与竞价方（以下简称乙方）：</P>
<P>&nbsp;&nbsp;根据《中华人民共和国合同法》及相关法律法规的规定，甲乙双方依照平等互利的原则，为明确双方的权利义务关系，经双方友好协商，达成如下协议，供双方遵守执行。</P>
<P>&nbsp;&nbsp;第一条、甲方指定本交易保证金 <input type="text"  name="JyBzj" <{if $xy.ID>''}> value="<{$xy.JyBzj}>"<{else}>value="<{$defaultxy.JyBzj}>" <{/if}> style="text-align:center; " size="10"  >元，乙方汇入钢之家超越帐号，开户行中国建设银行股份有限公司上海东方路支行，账户名称<{$smarty.const.WEBNAME}>，帐号31001587615050024351.
<BR>&nbsp;&nbsp;第二条、甲方确认竞价成功后乙方于<input type="text" name="PayDays" <{if $xy.ID>''}> value="<{$xy.PayDays}>"<{else}>value="<{$defaultxy.PayDays}>" <{/if}> style="text-align:center; " size="10">个工作日内付足全额货款到甲方指定账户。甲方账户名称<input type="text" name="AccountName" <{if $xy.ID>''}> value="<{$xy.AccountName}>"<{else}>value="<{$defaultxy.AccountName}>" <{/if}> style="text-align:center; " size="20">，开户行<input type="text" name="AccountBank" <{if $xy.ID>''}> value="<{$xy.AccountBank}>"<{else}>value="<{$defaultxy.AccountBank}>" <{/if}> style="text-align:center; " size="20">，帐号<input type="text" name="PayAccount" <{if $xy.ID>''}> value="<{$xy.PayAccount}>"<{else}>value="<{$defaultxy.PayAccount}>" <{/if}> style="text-align:center; " size="20">。
<BR>&nbsp;&nbsp;第三条、甲方收到货款后<input type="text" name="JhDays"  <{if $xy.ID>''}> value="<{$xy.JhDays}>"<{else}>value="<{$defaultxy.JhDays}>" <{/if}> style="text-align:center; " size="10">个工作日内转移货权至乙方。
<BR>&nbsp;&nbsp;第四条、甲方收到货款后<input type="text" name="KpDays" <{if $xy.ID>''}> value="<{$xy.KpDays}>"<{else}>value="<{$defaultxy.KpDays}>" <{/if}> style="text-align:center; " size="10">个工作日内开具增值税发票给乙方。
<BR>&nbsp;&nbsp;第五条、价格为甲方指定仓库自提价，出库费、运费等相关费用均由乙方承担。
<BR>&nbsp;&nbsp;第六条：<{$smarty.const.WEBNAME}>按交易量向乙方收取交易服务费0元/吨。
<BR>&nbsp;&nbsp;第七条、本协议所指款项均为现款。
<BR>&nbsp;&nbsp;第八条、验收质量标准：执行生产厂家出厂标准。
<BR>&nbsp;&nbsp;第九条、提出异议的期限：根据产品生产厂家的约定执行。
<BR>&nbsp;&nbsp;第十条、违约责任
<BR>&nbsp;&nbsp;乙方逾期付款，本协议自动终止，<{$smarty.const.WEBNAME}>在扣除10%的交易保证金后，剩余90%的交易保证金用于偿付甲方的损失。
<BR>&nbsp;&nbsp;如有其它违约，由违约方承担责任，依照《中国人民共和国协议法》的相关规定执行。
<BR>&nbsp;&nbsp;第十一条、争议的解决方式
<BR>&nbsp;&nbsp;双方在执行本协议过程中发生争议的，由双方协商解决，协商不成的，提交协议签订地的人民法院诉讼解决。
<BR>&nbsp;&nbsp;第十二条、其它约定事项：
<BR>&nbsp;&nbsp;任何一方由于不可抗力的原因不能履行协议时，应当及时向对方通报，在取得相关证明以后，可部分或者全部免予承担违约责任。本协议所称不可抗力是指双方在订立协议时不能预见并且不能避免、不能克服的客观情况，包括重大自然灾害、战争等。
<BR>&nbsp;&nbsp;第十三条本协议在<{$smarty.const.WEBNAME}>点击确认提交竞价后协议生效。</P>

				  <input type="hidden" name="xid" id="xid" value="<{$xy.ID}>">
				  <input type="hidden" name="zyid" value="<{$params.id}>">
				  <input type="hidden" name="istc" value="1">
				  <input type="hidden" name="default" value="0">

  </div> <div  style="margin-top:10px;"><img src="images/bcjjxy.gif" type="button" align="absmiddle" style="cursor: pointer;" width="114" height="30" onclick="xypostData();" /><span style="margin-left:10px;"><input type="checkbox" name="Isdefault" onclick="if(Isdefault.checked) xyform.default.value=1;else xyform.default.value=0">设置为缺省</span></div>

</div>
</form>

<!--end-->
<div style="border-bottom:1px dashed #e3e5e6; margin:10px 0 10px 0;"></div>  

<{/if}>


<!---->


<{if $params.type == "2" }> 

<div id="isjjshow" style="display:none">
</div>
<{/if}>
<div ><!--  资源信息  -->
	


<div class="resourceInfo mt10 bd_d6 oh"><!--  资源信息  -->
<div id="showjbzy"  <{if $params.id }>style="display:block; "<{else}>style="display:none; "<{/if}>>

    	<div class="title sameT1">
        	<h1>资源基本信息</h1><h1 style="color:red;">&emsp;(注:没有对应项可不填)</h1>
            <{if $zy.Status != "2" &&  $zy.Status != "8" && $zy.Status != "9"}><a href=javascript:void(0) onclick="editdiv('jbzy')" style="color:#0075e1;">展开修改>>></a><{/if}>
        </div>
        <div class="clear"></div>
    <div class="ordercontent">
            <table border="0"  cellspacing="0" width="100%" >
   <!--
						  <tr style="display:none;">
                              <th>销售单位:</th>
                              <td id="SalesUnit"><{$units[$zy.SalesUnit]}></td>
                            </tr>

													  
					 <tr>
						<th>资源类型：</th>
                        <td  colspan="3">
						<{if $params.xs || $zy.SalesType==5}>
							现货销售
							<input type="hidden" name="SalesType"  value="5"  / >
						<{/if}>
						<{if $params.cg || $zy.SalesType==2}>
						   竞价采购
					<input type="hidden" name="SalesType"  value="2"  / >
						<{/if}>
						</td>
						</tr>
-->
                             <tr>

						    <th >国家标准:</th>
                            <td   id="Standard"><{$zy.Standard}></td>
						
                              <th >表面状况:</th>
                              <td  id="bmzk"><{$zy.bmzk}></td>
                            
							  <th >包装情况:</th>
                              <td  id="bzqk"><{$zy.bzqk}></td>
							  </tr>
							  <tr>
                              <th >热处理状况:</th>
                              <td  id="rclzk"><{$zy.rclzk}></td>
                         
                          
							  <th >件重:</th>
                              <td  id="WeightPerOne"><{if $zy.WeightPerOne == 0.00}><{else}><{$zy.WeightPerOne}><{/if}></td>
                              <th >件数:</th>
                              <td  id="PerNumber"><{if $zy.PerNumber == 0}><{else}><{$zy.PerNumber}><{/if}></td>
                            </tr>
							<tr>

                              <th >起订重量(吨):</th>
                              <td  id="QuantityMin"><{if $zy.QuantityMin != 0.00}><{$zy.QuantityMin}><{/if}>&emsp;<font color="red">注：此条件针对现货资源</font></td>
 
						
							   	<th>批号：</th>
							<td id="ResourceNum"  colspan="3"><{$zy.ResourceNum}></td>  </tr>
							<tr>
							<th>打包备注:</th>
                              <td id="dbDesc" colspan="5"><{$zy.dbDesc}>
							  </tr>
							<tr>
                    <td colspan=6>&nbsp;</td>
				            </tr>

							<tr>
							<th>资源类型：</th>
                        <td  >
						<{if $params.type==2 || $zy.SalesType==5}>
							现货销售
						<{/if}>
						<{if $params.type==4 || $zy.SalesType==2}>
						   现货采购
						<{/if}>
						<{if $params.type==8 || $zy.SalesType==8}>
						   工程物流采购计划
						<{/if}>
						</td>
							
							 
							  
					<th>报价方式：</th>
                    <td id="TradeType" >
					<{$zyalltypes[$zy.TradeType]}>				
					</td>
                    <th>是否可议价：</th>
                    <td id="Yijia"><{$yijia[$zy.Yijia]}></label>
	
					</td>
                            </tr>
				<!--added by hezp for 17274 started 2015/09/23-->
				<{if $zy.TradeType =="2"}>
				<tr>
					<th>竞价确认:<{$zy.jjqr}></th>
					 <td colspan=5>
						
						<input type="radio" value="0" id="jjqr0" disabled <{if $zy.jjqr=='' || $zy.jjqr=='0'}>checked <{/if}>>可选择任何竞价会员
						<input type="radio" value="1" id="jjqr1" disabled <{if $zy.jjqr=='1'}>checked <{/if}>>只能选择<{if $params.type==2 || $zy.SalesType==5}>最高<{else}>最低<{/if}>2家竞价会员
						 
					</td>
				</tr>
				<{/if}>
				<!--added by hezp for 17274 ended 2015/09/23-->
						
                            <tr>
							<th>发布日期：</th>
                              <td id=""><{if $zy.CreateDate != "" }> <{$zy.CreateDate}><{else}> <{$smarty.now|date_format:'%Y-%m-%d %H:%M:%S'}><{/if}></td>
                              <th >交易开始日期:</th>
                              <td  id="SalesStartDate"><{if $zy.SalesStartDate=='0000-00-00 00:00:00'}><{else}><{$zy.SalesStartDate}><{/if}></td>
                              <th >交易结束日期:</th>
                              <td  id="SalesEndDate"><{if $zy.SalesEndDate=='0000-00-00 00:00:00'}><{else}><{$zy.SalesEndDate}><{/if}></td>
                            </tr>

							<tr>
							<th>授权交易会员类型:</th>
							 <td id="EmpowerType"  colspan=5>
								 <!--added by hzp for ksdj started 2015/09.08-->
								 <{if $zy.EmpowerType=='4'}>
								 集团指定交易会员可交易
								 <{else}>
								 <{$EmpowerType[$zy.EmpowerType]}>
								 <{/if}>
								 <!--added by hzp for ksdj ended 2015/09.08-->
							</td>
							</tr>
                <!--added by hezp started 2016/12/22-->
                <tr>
                    <td colspan=6>&nbsp;</td>
				</tr>
                <tr>
                    <input type="hidden" id="FuKuanXingShi" value="<{$zy.FuKuanXingShi}>">
                    <input type="hidden" id="fkxss" value="<{$zy.fkxss}>">
                    <th>付款形式：</th>
                    <td id="FuKuanXingShi2"><{if $zy.FuKuanXingShi=='-1' || $zy.FuKuanXingShi=='0'}>无<{else}><{$fkxs[$zy.fkxss]}>(<{$zfkxs[$zy.FuKuanXingShi]}>)<{/if}></td>
                    <th>开票方式：</th>
                    <td id="KaiPiaoFangShi"><{$kpz[$zy.KaiPiaoFangShi]}></td>
                    <th>计量方式：</th>
                    <td id="JiLiangFangShi"><{$jlfs[$zy.JiLiangFangShi]}></td>
                </tr>
                <!--added by hezp ended 2016/12/22-->

							<tr>
                    <td colspan=6>&nbsp;</td>
				</tr>
					<!--Update by xiakang started 2015/05/06-->	
							<!--if $params.type==8-->
							<{if $params.type==8 || $zy.SalesType==8}>
					<!--Update by xiakang ended 2015/05/06-->
							<tr>
							
							<th>是否定向采购：</th>
                    <td id="Isdxcgss" colspan=2 >
			
					<input type="checkbox" disabled  <{if $zy.Isdxcg==1 }>checked<{/if}> >是定向采购					
					</td>
					<th>是否有工程物流供应合同：</th>
                    <td id="Isgcwlhtss"colspan=2 ><input type="checkbox"  disabled <{if $zy.Isgcwlht==1 }>checked<{/if}> >有工程物流供应合同</td>
                </tr>				
				 <tr>
				 <th>定向采购厂家：</th>
                    <td id="dxcggcss" colspan=2>
					<{$listgys[$zy.dxcggc]}>
					</td>
                    <th>工程物流供应合同：</th>
                    <td id="gcwlhtss" colspan=2>
						<{$listgcht[$zy.gcwlht]}>
					</td>
                </tr>
					<{/if}>		
                         </table>
			</div>
</div>
 <div id ="jbzydiv"  style="<{if !$params.id && $params.type}>display:block;<{else}>display:none;<{/if}>"  >

	  <div class="title sameT1">
		<h1>资源基本信息</h1><h1 style="color:red;">&emsp;(注:没有对应项可不填)</h1> <{if $params.id && $params.type}><a href=javascript:void(0) onclick="closediv('jbzy')" style="color:#0075e1;">关闭</a><{/if}>
	  </div>
	  
	  

 <div class="ordercontent" id="div_ziyu">
	  <form action="member.php?action=tcfb&Vid=<{$params.Vid}>" method="post" name="zyform"  >
		  <table border="0"  cellspacing="0" width="100%" >
					<!--			<th style="display:none;"  >销售单位：</th>
                              <td style="display:none;" >
							  	   <{foreach from = $units item=v key=k}>
								   <input style="display:none;" type="radio" name="SalesUnit"  value="<{$k}>" <{if $zy.SalesUnit == $k}>checked <{/if}> ><{$v}>
								   <{/foreach}>
								   </td>
                            </tr>
				<tr>
						<th>资源类型：</th>
                        <td  colspan="3">
						<{if $params.xs || $zy.SalesType==5}>
							现货销售
							<input type="hidden" name="SalesType"  value="5"  / >
						<{/if}>
						<{if $params.cg || $zy.SalesType==2}>
						   竞价采购
					<input type="hidden" name="SalesType"  value="2"  / >
						<{/if}>
						</td>
						</tr>-->
							<tr>							  
								<th>国家标准:</th>
								<td><input type="text" name="Standard" value="<{$zy.Standard}>"></td>							
                             
								<th>表面状况:</th>
								<td><input type="text" name="bmzk"  value="<{$zy.bmzk}>" ></td>
                          
                          
							  <th>包装情况:</th>
                              <td><input type="text" name="bzqk"  value="<{$zy.bzqk}>" ></td>
							    </tr>
							<tr>
                              <th>热处理状况:</th>
                              <td><input type="text" name="rclzk"  value="<{$zy.rclzk}>" ></td>                          							                         
							  <th>件重:</th>
                              <td ><input type="text" name="WeightPerOne" <{if $zy.WeightPerOne=='0.0' }> value=""<{else}> value="<{$zy.WeightPerOne}>" <{/if}> onblur="setbzj(this);"></td>
                              <th>件数:</th>
                              <td ><input type="text" name="PerNumber" <{if $zy.PerNumber=='0' }> value=""<{else}> value="<{$zy.PerNumber}>" <{/if}> onblur="setbzj(this);"></td>
                            </tr>
							<tr>
                              <th>起订重量(吨):</th>
                              <td  ><input type="text" name="QuantityMin" style="width:70px" <{if $zy.QuantityMin=='0.00' }> value=""<{else}> value="<{$zy.QuantityMin}>"<{/if}>onblur="setbzj(this);" >&emsp;<font color="red">仅针对现货资源</font></td>

							  <th>批号：</th>
                           <td id=""><input type="text"  name="ResourceNum" value="<{$zy.ResourceNum}>" /></td>
						   <th>生产日期:</th>
                              <td  >
							     <input type="text"  name="MfgDate" <{if $zy.MfgDate == '0000-00-00'}>value=""<{else}> value="<{$zy.MfgDate}>"<{/if}> style="border:solid 1px #8eade0; width:160px; height:20px;" onclick="WdatePicker()" class="Wdate">
							</td>
							</tr>


<tr >
							<th nowrap>打包备注:</th>
                              <td colspan="5"  id="">
								<textarea rows="2" cols="10" name="dbDesc"><{$zy.dbDesc}></textarea><br />
							
								</td>
							</tr>
						
							

							<tr>
                    <td colspan=6>&nbsp;</td>
				</tr>
							
							<tr>
							<th>资源类型:</th>
                    <td  >
						<{if $params.type==2 || $zy.SalesType==5}>
							现货销售
							<input type="hidden" name="SalesType" id="SalesType2" value="5"  / >
						
						<{elseif $params.type==4 || $zy.SalesType==2}>
						   现货采购
					<input type="hidden" name="SalesType" id="SalesType2" value="2"  / >
						
						<{elseif $params.type==8 || $zy.SalesType==8}>
						   工程物流采购计划
					<input type="hidden" name="SalesType" id="SalesType2" value="8"  / >
						<{/if}>
						</td>
							
							<th>报价方式：</th>
                       <td   >
					    <{foreach from = $zyalltypes item=v key=k}>
						   <input type="radio" name="TradeType"  value="<{$k}>"   <{if $zy.TradeType == $k ||($zy.TradeType =="" && $k==1) }>checked<{/if}> onclick="doyijia(this.value)"  /><{$v}>
						   						   
						   <{/foreach}>										
					   </td>


                       <th>是否可议价：</th>
                       <td id=""><{foreach from = $yijia item=v key=k}><label>
							
						<input type="radio" name="Yijia" value="<{$k}>" <{if $zy.Yijia == $k || ($zy.Yijia =='' && $k=="2" )}>checked <{/if}>  ><{$v}></label>
						<{/foreach}>
					</td>
                </tr>
				<!--added by hezp for 1727 started 2015/09/23-->
				<tr <{if $zy.TradeType =="2"}><{else}>style="display:none;"<{/if}> id="jjqr">
					<th>竞价确认:</th>
					 <td colspan=5>
						
						<input name="jjqr" type="radio" value="0"  <{if $zy.EmpowerType=='' || $zy.EmpowerType=='0'}>checked <{/if}>>可选择任何竞价会员
						<input name="jjqr" type="radio" value="1" <{if $zy.EmpowerType=='1'}>checked <{/if}>>只能选择<{if $params.type==2 || $zy.SalesType==5}>最高<{else}>最低<{/if}>2家竞价会员
						 
					</td>
				</tr>
				<!--added by hezp for 1727 ended 2015/09/23-->
                           
							
							<!--   <tr>
							   
                    <th>履约保证金类型：</th>
                    <td id="" ><{foreach from = $guarpaytype item=v key=k}><label>
	                      <input type="radio" name="bzjflag"  value="<{$k}>" <{if $zy.bzjflag == $k ||($zy.bzjflag =="" && $k==1)}>checked <{/if}> onclick="setbzjflag(this.value)"   <{if $k==2}>disabled<{/if}> ><{$v}><{if $k==2}>(<{$fbbzj}>%)<{/if}></label>
	                    <{/foreach}></td>
                    <th><font style="color:red" id="baozj">*</font>履约保证金(元)：</th>
                    <td id=""><input type="text" name="LyMoney"  value="<{$zy.LyMoney}>" <{if $zy.bzjflag == "2"}>disabled<{/if}> ></td>
				</tr>-->	
                            <tr>
							<th>发布日期：</th>
                    <td id=""><{if $zy.CreateDate != "" }> <{$zy.CreateDate}><{else}> <{$smarty.now|date_format:'%Y-%m-%d %H:%M:%S'}><{/if}></td>
                              <th><font style="color:red">*</font>交易开始日期:</th>
                              <td ><input type="text" name="SalesStartDate" id="SalesStartDate" onchange="sethour(this.value);" <{if $zy.SalesStartDate != "0000-00-00 00:00:00" }> value="<{$zy.SalesStartDate}>"<{else}> value=""<{/if}> style="border:solid 1px #8eade0;width:160px; height:20px;"  class="Wdate"   onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm'})"></td>

                              <th><font style="color:red">*</font>交易结束日期:</th>
                              <td ><input type="text" name="SalesEndDate" id="SalesEndDate" <{if $zy.SalesEndDate != "0000-00-00 00:00:00" }>value="<{$zy.SalesEndDate}>"<{else}>value=""<{/if}> style="border:solid 1px #8eade0;width:160px; height:20px;" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm'})" class="Wdate"></td>
                            </tr>
							  <tr>
							<tr>
				<th>授权交易会员类型:</th>
                 <td colspan=5>
					 <!--判断是否是集团账号，集团的工程物流只能选择第四项集团指定交易会员可交易-->
					 <{foreach from = $EmpowerType item=v key=k}>
						<input name="EmpowerType" type="radio" value="<{$k}>" <{if ($smarty.session.SYS_GroupID== '126' || $smarty.session.SYS_COMPANYID == '126') && ($params.type==8 || $zy.SalesType =='8')}> disabled <{else}><{if ($zy.EmpowerType=='' && $k=='1') || $zy.EmpowerType==$k}>checked <{/if}><{/if}>  ><{$v}>
					 <{/foreach}>
					 <!--added by hezp for ksdj started 2015/09/08-->
					 <{if ($smarty.session.SYS_GroupID== '126' || $smarty.session.SYS_COMPANYID == '126') && ($params.type==8 || $zy.SalesType =='8')}>
					 <input name="EmpowerType" type="radio" value="4" <{if ($smarty.session.SYS_GroupID== '126' || $smarty.session.SYS_COMPANYID == '126') && ($zy.EmpowerType=='4' || $zy.EmpowerType=='')}>checked readonly="readonly"<{/if}> >集团指定交易会员可交易
					 <{/if}>
					 <!--added by hezp for ksdj ended 2015/09/08-->
				</td>
				</tr>

                <!--added by hezp started 2016/12/22-->
                <tr>
                    <td colspan=6>&nbsp;</td>
				</tr>

                <tr>
                    <th>付款形式：</th>
                    <td><{foreach from = $fkxs item=v key=k}>
                            <label style="margin-right:0px;"><input type="radio" name="fkxss"  value="<{$k}>"  onclick="setcdhp(this.value)" <{if $zy.fkxss == $k}>checked id="fkclick"<{/if}>><{$v}></label>
                            <{/foreach}>
                            <label style="margin-right:0px;"><input type="radio" name="fkxss"  value="-1"  onclick="setcdhp(this.value)" <{if $zy.FuKuanXingShi == '-1' || $zy.FuKuanXingShi == ''}>checked<{/if}>>无</label>
                    </td>
                    <th>开票方式:</th>
                    <td><{foreach from = $kpz item=v key=k}>
                    <label><input type="radio" name="KaiPiaoFangShi"  value="<{$k}>" <{if  $zy.KaiPiaoFangShi==$k}>checked<{/if}> ><{$v}></label>
                    <{/foreach}>
                    </td>
                    <th>计量方式:</th>
                    <td><{foreach from = $jlfs item=v key=k}>
                        <label><input type="radio" name="JiLiangFangShi"  value="<{$k}>" <{if  $k==$zy.JiLiangFangShi }>checked <{/if}> ><{$v}></label>
                        <{/foreach}>
                    </td>
                </tr>
                <tr id="xjhz" style="display:none;">
                    <th>现款：</th>
                    <td colspan=5><{foreach from = $xkxs item=v key=k}>
                    <label><input type="radio" name="FuKuanXingShi" onclick="setxk(this.value)" value="<{$k}>" <{if $zy.FuKuanXingShi == $k}>checked<{elseif $k==2 && $zy.FuKuanXingShi==0}>checked<{/if}> ><{$v}></label>
                    <{/foreach}></td>
                </tr>


                <tr id="yhcd" style="display:none;">
                    <th>承兑：</th>
                    <td colspan=5><{foreach from = $cdxs item=v key=k}>
                            <label><input type="radio" name="FuKuanXingShi"  value="<{$k}>" <{if $zy.FuKuanXingShi == $k}>checked <{/if}>  ><{$v}></label>
                            <{/foreach}></td>
                    
                </tr>

                <tr id="other" style="display:none;">
                    <th>其他：</th>
                    <td colspan="5">
                    <{foreach from = $qtxs item=v key=k}>
                    <{if $k!='9'}>
                    <label><input type="radio" name="FuKuanXingShi"  value="<{$k}>"  onclick="danbao(this.value);"><{$v}></label>
                    <{/if}>
                    <{/foreach}>
                    </td>

                </tr>

                <tr id="other3" style="display:none;">
                    <th>类型:</th>
                    <td colspan="5"><{foreach from = $dbxs item=v key=k}>
                    <label><input type="radio" name="FuKuanXingShi2"  value="<{$k}>" <{if $zy.FuKuanXingShi == $k}>checked <{/if}>  ><{$v}></label>
                    <{/foreach}></td>
                    
                </tr>

                <tr id="fktx" style="display:none;">
                    <th>付款产生的贴息承担方：</th>
                    <td  colspan="5">		
                    <{foreach from = $htsf2 item=v key=k}>
                    <label><input type="radio" name="txcd" value="<{$k}>" <{if $zy.txcd == $k || ($zy.txcd=='' && $k=='0')}>checked <{/if}> ><{$v}></label>
                    <{/foreach}></td>
                </tr>

                <tr>
                    
                </tr>
                <!--added by hezp ended 2016/12/22-->
							  
                <!--<{if $params.type==8}>
		<tr>
                    <th>是否定向采购：</th>
                    <td id="" colspan=2 >
			
					<input type="checkbox" value="<{if $zy.Isgcwlht==1 }>1<{else}>0<{/if}>" name="Isdxcg" onclick="isdxcg(this);"  <{if $zy.Isdxcg==1 }>checked<{/if}> >是定向采购
					
					
					
					</td>
					
					<th>是否有工程物流供应合同：</th>
                    <td id=""colspan=2 ><input type="checkbox" value="<{if $zy.Isgcwlht==1 }>1<{else}>0<{/if}>" name="Isgcwlht" onclick="isgcwlht(this);"  <{if $zy.Isgcwlht==1 }>checked<{/if}> >有工程物流供应合同</td>                 
                </tr>
				
				 <tr>
				 <th>定向采购钢厂：</th>
                    <td  colspan=2>
					<select <{if $zy.Isdxcg!=1 }>disabled<{/if}> id="dxcggc" name="dxcggc">
					<option value="0">选择</option>
					<{foreach from=$listgys item=v key=k}>
					<option value="<{$k}>" <{if $zy.dxcggc==$k}>selected<{/if}> ><{$v}></option>
					<{/foreach}>
					</select>
					</td>
                    <th>工程物流供应合同：</th>
                    <td id="" colspan=2>
						<select  <{if $zy.Isgcwlht!=1 }>disabled<{/if}> id="gcwlht" name="gcwlht">
					<option value="0">选择</option>
					<{foreach from=$listgcht item=v key=k}>
					<option value="<{$k}>" <{if $zy.Isgcwlht==$k}>selected<{/if}> ><{$v}></option>
					<{/foreach}>
					</select>
					</td>
                </tr>
		
<{/if}>-->
		
							<{if $params.type==8}>
		<tr>


		<tr>
                    <td colspan=6>&nbsp;</td>
				</tr>
                    <th>是否定向采购：</th>
                    <td id="" colspan=2 >
			
					<input type="checkbox" value="<{if $zy.Isdxcg==1 }>1<{else}>0<{/if}>" name="Isdxcg" onclick="isdxcg(this);"  <{if $zy.Isdxcg==1 }>checked<{/if}> >是定向采购
										</td>
					
					<th>是否有工程物流供应合同：</th>
                    <td id=""colspan=2 ><input type="checkbox" value="<{if $zy.Isgcwlht==1 }>1<{else}>0<{/if}>" name="Isgcwlht" onclick="isgcwlht(this);"  <{if $zy.Isgcwlht==1 }>checked<{/if}> >有工程物流供应合同</td>
               
                </tr>
				
				 <tr>
				 <th>定向采购厂家：</th>
                    <td  colspan=2>
					<select <{if $zy.Isdxcg!=1 }>disabled<{/if}> id="dxcggc" name="dxcggc">
					<option value="0">选择</option>
					<{foreach from=$listgys item=v key=k}>
					<option value="<{$k}>" <{if $zy.dxcggc==$k}>selected<{/if}> ><{$v}></option>
					<{/foreach}>
					</select>
					</td>
                    <th>工程物流供应合同：</th>
                    <td id="" colspan=2>
						<select  <{if $zy.Isgcwlht!=1 }>disabled<{/if}> id="gcwlht" name="gcwlht">
					<option value="0">选择</option>
					<{foreach from=$listgcht item=v key=k}>
					<option value="<{$k}>" <{if $zy.Isgcwlht==$k}>selected<{/if}> ><{$v}></option>
					<{/foreach}>
					</select>
					</td>
                </tr>
		
<{/if}>
		<tr >
							
                              <td colspan="6"  id="">
								
								<img src="images/btn_bc.png" type="button" style="cursor: pointer; margin: 10px 0 10px 20px;" onclick="ctzyjbpostData();" />
								</td>
							</tr>

<!--
<td width="100"  align="right">竞价方式:</td>
 <td width="259" >
 						<{foreach from = $tcjjtype item=v key=k}>
	                      <input type="radio" name="jjtype"  value="<{$k}>" <{if $zy.jjtype == $k}>checked <{/if}>  ><{$v}>
	                    <{/foreach}>
 </td>

 -->
<input type="hidden" name="jjtype"  value="2"  >

</tr>                       
                         <input type="hidden" name="zyflag" id="zyflag" value="<{$params.id}>">
						 <input type="hidden" name="salety" id="salety" value="<{$zy.SalesType}>">
						  <input type="hidden" name="typedif" id="typedif" value="<{$params.type}>">
						  <input type="hidden" name="jbplid" id="jbplid" value="">
						  <input type="hidden" name="IsNoFinance" value="<{$IsNoFinance}>">
						  <input type="hidden" name="jjxyid" id="jjxyid" value="">
                          </table>
</div>
</form>
</div>
</div>
<div class="resourceInfo mt10 bd_d6 oh"><!-- 资源信息  -->


 		<div style="background-color: #000000"></div>

 			 <!--Added by quanjw for meijiao start 2014/12/30 form -->
				 <!--煤炭-->
				<{if $params.Vid == $MEITAN_VID  }>
				 	 <{include file="member/inc_tc_meitan.html" }>
				 <!--焦炭-->
				   <{elseif $params.Vid == $JIAOTAN_VID }>
				 	 <{include file="member/inc_tc_jiaotan.html" }>
				 <!--Added by quanjw for shuini start 2015/2/5-->
				 	<!--水泥-->
				 <{elseif $params.Vid == $SHUINI_VID }>
				 	 <{include file="member/inc_tc_shuini.html" }>
				 <!--Added by quanjw for shuini end 2015/2/5-->
				 <!--Added by quanjw for jinshu start 2015/2/13 金属-->
				 <{elseif $params.Vid == $JINSHU_VID }>
				 	<{include file="member/inc_tc_jinshu.html" }>
				 <!--Added by quanjw for jinshu end 2015/2/13-->
				<!--Added by zfy for hanwang start 2019/9/29 焊网-->
				<{elseif $params.Vid == $HW_VID }>
				<{include file="member/inc_tc_hw.html" }>
	            <!--Added by zfy for hanwang end 2019/9/29 焊网-->
	            <{else}>
					<!--钢材-->
				 	<{include file="member/inc_tc_ziyu.html" }>
				 <{/if}>

				 <!--Added by quanjw for meijiao end 2014/12/30 form-->






<div class="resourceInfo jiaogeInfo mt10 oh"><!--  交割信息  -->


<div id="show" style="<{if $blist || $zy.Status == "2"}> display:block;<{else}>display:none;<{/if}>">

        <div class="title sameT1 bd_d6 oh">
            <h1>付款与交割信息</h1><{if $zy.Status != "2" &&  $zy.Status != "8" && $zy.Status != "9"}><a href=javascript:void(0) onclick='editjg2()' style="color:#0075e1;">展开修改>>></a><{/if}>
        </div>
        <div class="clear"></div>
        
        <div class="ordercontent oh" style="border-left: 1px solid #d6d6d6;">
        		
	  <table border="0"  cellspacing="0" width="100%" >
                <tr>
					<th>付款方式：</th>
                    <td id="jgfkfs"><{$paytype[$zy.fkfs]}></td>
					<th>预付款比例：</th>
                    <td id="LyMoney"><{$zy.LyMoney}>%</td>

                    <th>可洽谈：</th>
                    <td id="IsTalk"><input type="checkbox" value="1" name="IsTalk" disabled <{if $zy.IsTalk==1 }>checked<{/if}>  >付款及交割可洽谈
					</td>	
                  </tr>
                <tr>  
				<th>提货方式：</th>
                    <td id="jgDelivery"> <{$Delivery[$zy.Delivery]}>
					</td>
					<th>所在城市：</th>
                    <td id="jgStoreCity"><{if $zy.StoreCity!=""}><{$zy.StoreCity}> <{else}> <{$citys[$zy.StoreCityCode]}> <{/if}></td>
                
					 <th>交割类型：</th>
                    <td id="jgtype"><{$jgtypes[$zy.PickUpType]}></td>
					
				 </tr>
                <tr> 	
					
					<th>交割日期：</th>
                    <td id="jgdate"><{if $zy.PickUpDate != '0000-00-00'}> <{$zy.PickUpDate}><{/if}></td>
                     <th>交割城市：</th>
                    <td id="jgcity"><{$zy.PickUpCity}></td>
               
                
               
                    <th>交货仓库：</th>
                    <td id="jgjhck"  ><{$zy.jhck}></td> 
					</tr>
				 <tr>
					<th>交货地址：</th>
                    <td colspan="5" id="jgadress"><{$zy.PickUpAddress}></td>
                </tr>
                
            </table> 		
        </div>
</div>

  <div class="" style="<{if $blist || $zy.Status == "2"}>display:none;<{else}>display:block;<{/if}>" id ="jgdiv" >
     
        <div class="title sameT1 bd_d6 oh">
            <h1>交割信息</h1><a href=javascript:void(0) onclick='closejg()' style="color:#0075e1;">关闭</a>
        </div>
		<div class="clear"></div>
		<form action="member.php?action=tcjgup" method="post" name="jgform" >
        <div class="ordercontent bd_d6 oh" style="border-top: none;">


				  <table border="0"  cellspacing="0" width="100%" >
                <tr>
				  <tr>
                    <th>付款方式：</th>
                    <td id=""><{foreach from = $paytype item=v key=k}><label>
	                      <input type="radio" name="fkfs"  value="<{$k}>"  onclick="chejdt(this.value);"  <{if $zy.fkfs == $k || (($zy.fkfs =='0'||$zy.fkfs =='') && $k==1 )}>checked <{/if}> ><{$v}></label>
	                    <{/foreach}></td>
						
					<th>预付款比例：</th>
                    <td id=""><input type="text" name="LyMoney" id="LyMoneyid" onblur="checkval(this);" maxlength="3" value="<{if $zy.LyMoney==""}>100<{else}><{$zy.LyMoney}><{/if}>" >%</td>

                    <th>可洽谈：</th>
                    <td id=""><input type="checkbox" value="1" name="IsTalk" <{if $zy.IsTalk==1 || $zy.IsTalk==0}>checked<{/if}>  >付款及交割可洽谈
					</td>	
				
			</tr>
				<tr>				
                    <th>提货方式：</th>
                    <td id=""> <{foreach from = $Delivery item=v key=k}><label>
					   <input type="radio" name="Delivery"  value="<{$k}>" <{if $zy.Delivery == $k ||(($zy.Delivery =='0'||$zy.Delivery =='') && $k==1 ) }>checked <{/if}> ><{$v}></label>
					   <{/foreach}>
					</td>
				
					<th>所在城市：</th>
                    <td id=""> 
					<select  name="StoreCityCode" id="StoreCityCode" >
					<option value="">-请选择-</option>
					<{foreach from = $citys item=v key=k}>
					   <option  value="<{$k}>" <{if $zy.StoreCityCode == $k && $zy.StoreCityCode!=""}>selected<{else}><{if $AddressCity==$v}>selected <{/if}><{/if}> ><{$v}></option>
					   <{/foreach}>
					 </select>
					 录入
					     <input type="text" name="StoreCity" id="StoreCity" size="8" value="<{$zy.StoreCity}>"  />
					</td>
					<script>
					if(document.getElementById('StoreCityCode').value=="" && document.getElementById('StoreCity').value==""){
						 document.getElementById('StoreCity').value="<{$AddressCity}>";
					}
					</script>
			
                    <th>交割类型：</th>
                    <td>
						<{foreach from = $jgtypes item=v key=k}>
					   <label><input type="radio" name="PickUpType"  value="<{$k}>" <{if $zy.PickUpType == $k}>checked <{/if}>  ><{$v}></label>
					   <{/foreach}>

<script>

if("<{$zy.PickUpType}>" == "0"){
$(document).ready(function() {$("input[name=PickUpType]:eq(0)").attr("checked",'checked'); });}
</script></td>
			</tr>
				 <tr>
                    <th>交割日期：</th>
                    <td><input type="text" name="PickUpDate" <{if $zy.PickUpDate == '0000-00-00'}> value="<{$jgdata}>"<{else}>value="<{$zy.PickUpDate}>"<{/if}>  onclick="WdatePicker()" class="Wdate"></td> 
				</tr>
				 <tr>
                   
                    <td id="" colspan="6" style="border-bottom: 0px solid #D6D6D6;border-right: 0px solid #D6D6D6; padding-left: 0px;text-align: left; ;">
				
			<div class="contactInfo2 contactInfo mt10 oh" style="margin-top: 0px;" >
            <div class="contactContent bd_d6 o h">
					<div class="contactList">
                    <div class="tit">常用交货地列表 <a style="margin-left:50px;color:#0075e1" href="user.php?view=deliveraddr_add" target='_blank'>添加常用交货地</a></div>
					<div class="clear"></div>
			
                   <table>
				   <tr>
					<td>仓库名称:<input type="text" name="ckname" id="ckname" size="17" value=""></td>
					<td>省份:
                    <select  name="province" id="province" >
					        <option value="">请选择
	       				           <{foreach from=$AddressState key=k item=v}>
								      <option value="<{$k}>"><{$v}>
									  </option>
								   <{/foreach}>
	       			</select> 
					</td>
					<td>城市:<input type="text" name="city" id="city" maxlength="" value=""></td>
					<td>地址:<input type="text" name="address" id="address" maxlength="" value=""></td>
			    <!--<td><input type="submit" name="serach" id="serach";" maxlength="" value="">搜索</td>-->
				    <td><a style="margin-left:50px;color:#0075e1" href="javascript:;" onclick="searchck()" >搜索</a></td>
					 </tr>
				   </table>
					 
                    <table id="contacttab" border="0" cellspacing="0" width="100%">
                    
                        <tr>
                            <td class="td1">仓库名称</td>
                            <td>省份</td>
							<td>城市</td>
                            <td>地址</td>
                            
                     
                        </tr>
                        
                        <{foreach name=hot from=$cklist item=v}>
                        <tr id="ck<{$v.ID}>">
                            <td class="td1"><label><input name="Isckxz" type="radio" value="<{$v.ID}>" onclick="cangkuclick()" <{if $v.IsDefault==1 || $cklist|@count==1}>checked<{/if}> /><strong><{$v.CkName }></strong></label></td>
              
                              <td ><{$AddressState[$v.AddressState]}></td>
                              <td ><{$v.AddressCity}></td>
                              <td ><{$v.Address}></td>
                          </tr>
                        <{/foreach}>
                    </table>
					 </div></div></div></td>
                </tr>
		<input type="hidden" name="PickUpCity" id="PickUpCity" value="<{$zy.PickUpCity}>">
	  <input type="hidden" name="PickUpAddress" id="PickUpAddress" value="<{$zy.PickUpAddress}>">
	  <input type="hidden" name="jhck" id="jhck" value="<{$zy.jhck}>">	
	  <script>
	  function cangkuclick() {
	   var id=$("input[name=Isckxz]:checked").val();	
    
            jQuery.ajax({
                url: 'user.php?view=vckinfo',
                data: "id="+id+"&time="+new Date().getTime(), // 从表单中获取数据
                type: 'POST', // 设置请求类型为 ‘POST’，默认为 ‘GET’
                dataType:"json",
                success: function(data) {
				
                    if(parseInt(data.error)){
                        alert(data.errorinfo);
                    }
                    else{		
				
                        $("input[name='PickUpCity']").val(data.datainfo.AddressCity);
                        $("input[name='PickUpAddress']").val(data.datainfo.Address);
                        $("input[name='jhck']").val(data.datainfo.CkName);
    
                    }
                }
            });
	  }
	  var id=$("input[name=Isckxz]:checked").val();	
    
            jQuery.ajax({
                url: 'user.php?view=vckinfo',
                data: "id="+id+"&time="+new Date().getTime(), // 从表单中获取数据
                type: 'POST', // 设置请求类型为 ‘POST’，默认为 ‘GET’
                dataType:"json",
                success: function(data) {
				
                    if(parseInt(data.error)){
                        alert(data.errorinfo);
                    }
                    else{		
				
                        $("input[name='PickUpCity']").val(data.datainfo.AddressCity);
                        $("input[name='PickUpAddress']").val(data.datainfo.Address);
                        $("input[name='jhck']").val(data.datainfo.CkName);
    
                    }
                }
            });
	  </script>
	  
				
      <input type="hidden" name="flag" id="flag" value="">
	  <input type="hidden" name="jgflagid" id="jgflagid" value="<{$params.id}>">
        </table>
        
        <img src="images/btn_bcjgxx.png" type="button"  style="cursor: pointer; margin: 10px 0 10px 135px;" onclick="pljgpostData();" /></div>  
	</div>
</form>
</div>


</div>




<!---->

<!--联系人信息-->  



     <div class="contactInfo mt10 oh"  id="showfh" <{if $params.id }>style="display:block; "<{else}>style="display:none; "<{/if}>>
	
            <div class="title sameT1 bd_d6 oh">
                <h1>联系人信息</h1>
                <{if $zy.Status != "2" &&  $zy.Status != "8" && $zy.Status != "9"}> <a href=javascript:void(0) onclick='editfh()' style="color:#0075e1;" id="lxr">展开修改>>></a><{/if}>
            </div>
            <div class="clear"></div>
            <div class="contactContent oh" style="border-left: 1px solid #d6d6d6;">
                <table border="0"  cellspacing="0" width="100%">
                    <tr>
                        <th>联系人姓名：</th>
                        <td id="ContactMan2" colspan="3"><{$zy.ContactMan}></td>
                        <th>手机号码：</th>
                        <td id="ContactMobile2" width="20%"><{$zy.ContactMobile}></td>
                        <th>固定电话：</th>
                        <td id="ContactPhone2"><{$zy.ContactPhone}></td>
                    </tr>
                    <tr>
                        <th>传真：</th>
                        <td id="ContactFax2" colspan="3"><{$zy.ContactFax}></td>
                        <th>电子邮件：</th>
                        <td id="ContactEmail2" width="20%"><{$zy.ContactEmail}></td>
                        <th>QQ号：</th>
                        <td id="QQNum2"><{$zy.QQNum}></td>
                    </tr>     
                    <input type="hidden" name="fhflag" id="fhflag" value="<{$params.id}>">
                </table>
            </div>
        </div>
 



        <div class="contactInfo2 contactInfo mt10 oh" style="display:none;" id="fhdiv">
            <div class="title sameT1 bd_d6 oh">
                <h1>联系人信息</h1>
                <a href=javascript:void(0) onclick='closefh()' style="color:#0075e1;">关闭</a>
            </div> 
            <div class="clear"></div>
            <div class="contactContent bd_d6 o h">
                <div class="contactList">
                    <div class="tit">常用联系人列表</div>
                    <div class="clear"></div>
                    <table id="contacttab" border="0" cellspacing="0" width="100%">
                    
                        <tr>
                            <td class="td1">姓名</td>
                            <td>电话</td>
                            <td>手机</td>
                            <td>传真</td>
                            <td>邮箱</td>
                            <td>QQ</td>
                        </tr>
                        
                        <{foreach name=hot from=$con_user item=v}>
                        <tr id="contact<{$v.ID}>">
                            <td class="td1"><label><input name="IsDefault" type="radio" value="<{$v.ID}>" /><strong><{$v.ARealName}></strong></label></td>
                            <td><{$v.TelePhone}></td>
                            <td><{$v.Mobile}></td>
                            <td><{$v.Fax}></td>
                            <td ><{$v.Email}></td>
                            <td><{$v.QQNum}></td>
                        </tr>
                        <{/foreach}>
                    </table>
                </div>
                <form action="member.php?action=tcedituser" method="post" name="userform22"> 
			<input name="ContactMan" value="<{$zy.ContactMan}>" type="hidden" />
			<input name="ContactMobile" type="hidden" value="<{$zy.ContactMobile}>" />
			<input name="ContactPhone" value="<{$zy.ContactPhone}>" type="hidden" />
			<input name="ContactFax" type="hidden" value="<{$zy.ContactFax}>" />
			<input name="ContactEmail" value="<{$zy.ContactEmail}>" type="hidden" />
			<input name="QQNum" type="hidden" value="<{$zy.QQNum}>" />
		<input type="hidden" value="" name="usid">
		<input type="hidden" value="<{$params.id}>" name="lxrid">
            <img src="images/btn_bclxrxx.png" id="savelxr" style="cursor: pointer; margin: 10px 0 10px 135px;" onclick="pladduser();" />
        </form>
            </div>
        </div>
        
        
<script>
	  
	var autoCompletecz=new AutoComplete('res_pm','autores_pm'); 
	  </script>
<script>

//if("<{$zy.IsStaMoney}>" == "2"){
//setdj(2);
//}
function getpz(obj){

    if( obj.value !=''){
	     var param = "action=ajaxgetpz&id="+obj.value;
	     var ajax = new Ajax("member.php", setMember, param );
	}
}
function setMember( returnstr ){
//alert(returnstr);
 document.getElementById("VarietyCode").options.length=0;

var str = returnstr;
var obj = eval('(' + str + ')');

for(var i=0;i<obj.length;i++){ 

 var option = new Option(obj[i]['VarietyName'],obj[i]['VarietyCode']);
 document.getElementById("VarietyCode").options[i]=option; 

 if("<{$zy.VarietyCode}>" == obj[i]['VarietyCode']){document.getElementById("VarietyCode").options[i].selected = true;}
 } 

}
</script>
<script>

bmarr=[];

<{$secjsstr}>
function changeSecParent( obj ){
    var pdiv = document.getElementById( 'pd2' );
    var pid = obj.value;
	var str = '';
	if( sectypecontents[pid] != null ){
	
	  for( var i = 0; i < sectypecontents[pid].length; ++i ){
	  
	      str += "<option value='" + sectypecontents[pid][i][0]+ "'>" + sectypecontents[pid][i][1] + "</option>";
	  
	  }
	  pdiv.innerHTML = "<select style='width: 80px;' name='VarietyCode' onchange='setother(this);' id='secparentid' ><option value='0'>可选</option>" + str + "</select>";
	 
	 setother(obj);
		
	}else{
	  pdiv.innerHTML = "<select style='width: 80px;' name='VarietyCode' id='secparentid' ><option value='0'>无</option></select>";
	  
	  setother(obj);	  
	}
}

function setother(obj){
	
	if(obj.name=='VarietyCode'){
	
	
		if(obj.value==0){
		
		
			obj=document.getElementById( 'parentid' );
		}
	}

		jQuery.ajax({
                url: ' member.php?action=getbmlist',
                data: "id="+obj.value+"&time="+new Date().getTime(), // 从表单中获取数据
                type: 'POST', // 设置请求类型为 ‘POST’，默认为 ‘GET’
                dataType:"text",
                success: function(data) {
                    if(parseInt(data.error)){
                        alert(data.errorinfo);
                    }
                    else{				
                        bmarr=data.split("#");
							
						document.getElementById("tmparr").innerHTML=bmarr[0];
						document.getElementById("tmparrwu").innerHTML=bmarr[1];
						//deleteDiv("rlist");
						 //my_on_init();

                    }
                }
            });

}

</script>
							  
<script>    


function setbzj(obj){
	if(obj.value<0)
	{
		alert("该值无法为负,请重新填写！");
		obj.value="";
	}
	}


function doyijia(s){

	if(s==2|| s==3){
	  var yijia=document.getElementsByName("Yijia");
	  for(var i=0;i<yijia.length;i++){
			if(yijia[i].value==1){
			yijia[i].checked=true;
			}
	  }
	  //added by hezp for 17274 started 2015/09/23
	  if(s==2){
			document.getElementById("jjqr").style.display="";
	  }else{
			document.getElementById("jjqr").style.display="none";
	  }
	  //added by hezp for 17274 ended 2015/09/23
	}else{
	  document.getElementById("jjqr").style.display="none";
	  var yijia=document.getElementsByName("Yijia");
	  for(var i=0;i<yijia.length;i++){
			if(yijia[i].value==2){
			yijia[i].checked=true;
			}
	  }		
	}
	
	
}

function sethour(h){
	 var d = Date.parse(h.replace(/\-/g,"/"));
	 //var a=d+(1000*60*60*4);//增加3小时。
     <{if $params.type==4 || $zy.SalesType==2}>
	 var a=d+(1000*60*60*4);//增加4小时。
	 <{else}>
	 var a=d+(1000*60*60*2);//增加2小时。
	 <{/if}>
	 var b=new Date(a);
	 var yyyy=b.getFullYear();
	 var MM=(b.getMonth()+1);
	 var dd=b.getDate();
	 var hh=b.getHours()<10 ? "0"+b.getHours() : b.getHours();
	 var mm=b.getMinutes()<10 ? "0"+b.getMinutes() : b.getMinutes();
	 var c=yyyy+"-"+MM+"-"+dd+" "+hh+":"+mm;
	 document.forms['zyform'].SalesEndDate.value=c;
	//alert(c);
}
function setje(s){
if (s == "2")
{
	document.forms['zyform'].AssureGuarpayMoney.value = "";
}
}

function isdxcg(obj){
if(obj.checked){
	document.getElementById("dxcggc").disabled=false;
	obj.value=1;
	}else{
	document.getElementById("dxcggc").disabled=true;
	obj.value=0;
	document.getElementById("dxcggc").value=0;
	}
	
}
function isgcwlht(obj){
if(obj.checked){
	document.getElementById("gcwlht").disabled=false;
	obj.value=1;
	}else{
	document.getElementById("gcwlht").disabled=true;
	obj.value=0;
	document.getElementById("gcwlht").value=0;
	}
	
}

</script>
<script>



$(document).ready(function() {
$("input[name=IsDefault]").click(function(){
	var id=$("input[name=IsDefault]:checked").val();	
		jQuery.ajax({
            url: 'user.php?view=vusercontact',
            data: "id="+id+"&time="+new Date().getTime(), // 从表单中获取数据
            type: 'POST', // 设置请求类型为 ‘POST’，默认为 ‘GET’
            dataType:"json",
			success: function(data) {
				if(parseInt(data.error)){
					alert(data.errorinfo);
				}
				else{			
 					$("input[name='ContactMan']").val(data.datainfo.ContactMan);
					$("input[name='ContactPhone']").val(data.datainfo.ContactPhone);
					$("input[name='ContactMobile']").val(data.datainfo.ContactMobile);
					$("input[name='ContactFax']").val(data.datainfo.ContactFax); 
					$("input[name='ContactEmail']").val(data.datainfo.ContactEmail); 
					$("input[name='QQNum']").val(data.datainfo.QQNum); 
				}
            }
        });
	
});

		 $("input[name=Isckxz]").click(function(){
        var id=$("input[name=Isckxz]:checked").val();	
    
            jQuery.ajax({
                url: 'user.php?view=vckinfo',
                data: "id="+id+"&time="+new Date().getTime(), // 从表单中获取数据
                type: 'POST', // 设置请求类型为 ‘POST’，默认为 ‘GET’
                dataType:"json",
                success: function(data) {
				
                    if(parseInt(data.error)){
                        alert(data.errorinfo);
                    }
                    else{		
				
                        $("input[name='PickUpCity']").val(data.datainfo.AddressCity);
                        $("input[name='PickUpAddress']").val(data.datainfo.Address);
                        $("input[name='jhck']").val(data.datainfo.CkName);
    
                    }
                }
            });
        
    });	 

$("#addcontact").click(function(){
			var ContactMan=$("input[name='ContactMan']").val();
			var ContactPhone=$("input[name='ContactPhone']").val();
			var ContactMobile=$("input[name='ContactMobile']").val();
			var ContactFax=$("input[name='ContactFax']").val(); 
			var ContactEmail=$("input[name='ContactEmail']").val(); 
			var QQNum=$("input[name='QQNum']").val(); 
	        jQuery.ajax({
            url: 'user.php?action=usercontact',
            data:"ContactMan="+ContactMan+"&ContactPhone="+ContactPhone+"&ContactMobile="+ContactMobile+"&ContactFax="+ContactFax+"&ContactEmail="+ContactEmail+"&QQNum="+QQNum+"&t=2&time="+new Date().getTime(),
            type: 'POST',
			dataType:"json",			
			success: function(data) {
				if(parseInt(data.error)){
					alert(data.errorinfo);
				}
				else{	
					alert(data.successinfo);
					
					$("#contacttab tbody").append(data.datainfo);	
				}
            }
        });
});


});

function radioclick(id){
		jQuery.ajax({
            url: 'user.php?view=vusercontact',
            data: "id="+id+"&time="+new Date().getTime(), // 从表单中获取数据
            type: 'POST', // 设置请求类型为 ‘POST’，默认为 ‘GET’
            dataType:"json",
			success: function(data) {
				if(parseInt(data.error)){
					alert(data.errorinfo);
				}
				else{			
 					$("input[name='ContactMan']").val(data.datainfo.ContactMan);
					$("input[name='ContactPhone']").val(data.datainfo.ContactPhone);
					$("input[name='ContactMobile']").val(data.datainfo.ContactMobile);
					$("input[name='ContactFax']").val(data.datainfo.ContactFax); 
					$("input[name='ContactEmail']").val(data.datainfo.ContactEmail); 
					$("input[name='QQNum']").val(data.datainfo.QQNum); 
				}
            }
        });
}

function delcontact(id){
	if(confirm('请确认是否删除！')){
	jQuery.ajax({
            url: 'user.php?action=delusercontact',
            data: "id="+id+"&time="+new Date().getTime(), // 从表单中获取数据
            type: 'POST', // 设置请求类型为 ‘POST’，默认为 ‘GET’
            dataType:"json",
			success: function(data) {
			
				if(parseInt(data.error)){
					alert(data.errorinfo);
				}
				else{			
					alert(data.successinfo);
					
					$("#contact"+id).remove();
				}
            }
        });
	}
}
</script>		


<{if $params.type == "2" }> 

</div>

<{/if}>

<{if $zy.Status != "2" &&  $zy.Status != "8" && $zy.Status != "9"}>
<div id="sj" class="tijiaoBJ mt10 oh bd_d6" id="shangjia">


<{if $params.id}>
					<!--<input type="button" name="piclistbutton" value="短信通知" onclick="window.open('member.php?view=qydxtz&sid=<{$params.id}>','','scrollbars=no,width=550,height=600,left=500,top=50')">-->
			<input type="image"   id="btn_sjtj" src="images/btn_sjtj.png" onclick="shangjia(this);" class="fr" >
						<{else}><img src="images/btn_sjtj_gray.png" class="fr">
<{/if}>


<label class="fr">
			输入交易密码:<input type="password" name ="mima" id="mima">
			<input type="hidden" name="id" id="zyid" value="<{$zy.ID}>" ></label>


</div>


</div></div>
<{/if}>
<{if ($zy.Status == "2" &&  $zy.Status != "8" && $zy.Status != "9") || ($blist) }>
<{if $zy.TradeType == "2" || ($blist) }>
    <div class="dxbox" style="margin: 10px auto 0 auto; padding: 0;">
        <div class="title sameT1 oh bd_dc">
            <h1>竞价公司列表</h1>
            <a href="javascript:;" id="showalltdmx" onclick="showalltdmx(this)" class="fr">+全部展开</a>
			<td><a href="pdf.php?action=jjtc_print&id=<{$params.id}>" target="_blank" class="fl" style="margin: 0 30px 0  0;">竞价打印</a></td>
        </div>
        <div class="clear"></div>
        
        <style type="text/css">
		.jjgslist{
            border: 1px solid #d6d6d6;
            border-top: none;
        }
		.jjgslist thead tr{
			background: #f8f8f8;
		}
		.jjgslist td{
            height: 35px;
            border: 1px solid #d6d6d6;
        }
		
		
		</style>
         
       <table class="jjgslist tc">
       <thead>
            <tr>
			  <td width="40">选择</td>
			  <td>竞价公司</td>
			  <td>总价</td>
			  <td>担保类型</td>
			  <td>竞价日期</td>
			  <td>状态</td>
			  <td>操作</td>
		    </tr>
       </thead>
       <tbody>
			<{foreach from=$blist item=i key=index}>
            <tr>
                <td>    
							  
			    <{if $i.Status == "2"}>
                    <input type="checkbox" id = "jjed" name ="jjed" value="<{$i.ID}>" checked="true" disabled>
                <{elseif $i.Status == "3" || $i.Status == "21" || $i.Status == "22"}> 
                    <input type="checkbox" id = "jjed" name ="jjed" value="<{$i.ID}>"  disabled>
                <{else}>
			
			        <!-- input type="checkbox" id = "jj" name ="jj" value="<{$i.ID}>" <{if (($i.Tmoney < $zuigaom[1].Tmoney) && $zy.TradeType == "2") || (($i.Tmoney >  $zuidim[1].Tmoney) && $zy.TradeType == "3")  }> disabled <{/if}> -->
			        <input type="checkbox" id = "jj" name ="jj" value="<{$i.ID}>" <{if ((($i.Tmoney < $zuigaom[1].Tmoney) && $zy.SalesType == "5") || (($i.Tmoney >  $zuidim[1].Tmoney) && ($zy.SalesType == "2" || $zy.SalesType == "8"))) && $zy.jjqr == '1'  }> disabled <{/if}> >
			    <{/if}>
                    <!--   <input name="jj" type="checkbox"  value="<{$i.ID}>"  >-->
                </td>
					<!-- tender.php?view=tender_bidding_detail&td_id=<{$i.ID}>&PID=<{$base.ID}> -->
                <td><a target="_blank" href="tender.php?view=tender_bid&td_id=<{$i.ID}>&id=<{$base.ID}>&comid=<{$i.ComID}>"><{$i.Comname}></a> <a href="javascript:;" id="stdmxshow<{$i.ID}>" onclick="showtdmx(this,<{$i.ID}>)">+展开</a></td>
                <!--td><-{$i.Tmoney}-></td-->
				<td><{$i.money}></td>
                <td><{$dblx[$i.AssureType]}></td>
                <td><{$i.CreateDate|date_format:"%Y-%m-%d"}></td>
                <td><{$jjtype[$i.Status]}></td>
				<td><{if $i.Status == "1"}><a href="member.php?view=tradeoff&id=<{$i.ID}>&type=tczy" target="_blank">申请取消交易</a><{/if}> <a href="#" id="click_sendmsg" onclick="fsxx('<{$i.Mid}>',1);" style="color:red;">发送消息</a>	  </td>
            </tr>
            <tr style="display:none;" id="tdmx<{$i.ID}>">
                <td colspan="8">
                    <table id="detailtablist" style="border: none;">
                        <thead>
                            <tr>
                                <td width="39">序号</td>
                                <td>名称</td>
                                <td>材质</td>
                                <td>规格</td>
                                <td>厂家</td>
                                <td>报价</td>
                                <td>单位</td>
                                <td>重量</td>
                            </tr>
                        </thead>
                        <tbody>
                            <{foreach from=$i.arr_mx item=j}>
                            <tr id="<{$j.ID}>"><!-- <{if $i.BID!=""}>checked<{/if}> -->
                                <td><input name="BidID[]" checked type="checkbox" disabled value="<{$j.ID}>"  ></td>
                                <td><{$j.VarietyName}></td>
                                <td><{$j.MaterialCode}></td>
                                <td><{$j.SpecCode}></td>
                                <td><{$j.OriginCode}></td>
                                <td><{if $j.PriceContention == "0"}><{else}><{$j.PriceContention}><{/if}></td>
                                <td>吨</td>
                                <td><{$j.BuyQuantity}></td>
                            </tr>
                            <{/foreach}>
                        </tbody>		
                    </table>
                    <!--added by hezp started 2016/11/08-->
                    <{if $i.fkxs!='' && $i.fkxs!=null }>
                    <table >
                        <tbody>
                            <tr>
                                <td style="background:#f8f8f8;width:12.5%">付款形式：</td>
                                <td style="width:20.8%"><{if $i.fkxs=='-1'}>无<{else}><{$fkxs[$i.fkxss]}>(<{$zfkxs[$i.fkxs]}>)<{/if}></td>
                                <td style="background:#f8f8f8;width:12.5%">开票方式:</td>
                                <td style="width:20.8%"><{$kpz[$i.KaiPiaoFangShi]}></td>
                                <td style="background:#f8f8f8;width:12.5%">计量方式:</td>
                                <td style="width:20.8%"><{$jlfs[$i.JiLiangFangShi]}></td>
                            </tr>
                            <tr>
                                <td style="background:#f8f8f8;width:12.5%">付款产生的贴息承担方：</td>
                                <td style="width:20.8%"><{if $i.fkxs!='1' &&  $i.fkxs!='2' &&  $i.fkxs!='-1'}><{$htsf2[$i.txcd]}><{/if}></td>
                                <td style="background:#f8f8f8;width:12.5%">备注:</td>
                                <td colspan=3><{$i.ReMark}></td>
                            </tr>
                        </tbody>	
                    </table>
                    <!--added by hezp ended 2016/11/08-->
                    <{/if}>
				</td>
			</tr>
        <{/foreach}>
	</tbody>
    </table>

	<input name="PID" type="hidden" value="<{$base.ID}>">
    <{if $zy.Status == '2' }>	     
        <{if $blist &&  $isqueren == "1" }>
        <!--Added for quanxian sheding(leftmenu)  by hxy started  2015/1/13-->
        <!--Added for css  by qh started  2015/2/3-->
        <div class="tijiaoBJ mt10 oh bd_dc">
        <div class="fr">输入交易密码：
        <input type="password" name="qrjjpwd" id="qrjjpwd">

        <!--Added for quanxian sheding(leftmenu)  by hxy ended  2015/1/30-->
        <input type="image" src="img/btn_qrcj.png" onclick="return qrjj();" style="margin-top: -1px;" >
        </div></div>
        <!--Added for css  by qh ended  2015/2/3-->
        <{else}>
        <img src="img/btn_qrcj_gray.png" style="margin: 10px 0 0 0px;"><span style=" margin: 10px 0 0 10px; color: #f00;">需在<{$zy.SalesEndDate}>之后才可确认成交</span>&nbsp;&nbsp;当前时间: <{$nowDate}>
        <{/if}>		 
    <{/if}>		 
		  
		<{/if}>
		
    <table>
    <tr><td width="90%"></td>
        <td><{if $blist && !$zyxj}><{else}><input type="image" src="img/btn_zyxj.png" onclick="xiajia(<{$params.id}>);" ><{/if}>

        </td>
    </tr>
    </table>
</div>

<{/if}>


</div>

</div>
<span id="tmparr" style="display:none;" ></span>

<span id="tmparrwu" style="display:none;" ></span>

<span id="advancd_search_czs" style="display:none;"></span>

<!---->




<!--div class="cbox" style="margin-top:10px;margin-bottom:10px;">
<div id="TwoStatus" style="border-bottom:1px solid #bad6e3; height:30px;">
    <div>我的动态</div>
    <div>竞价人动态</div>
</div>
<div style="clear:both;"></div>
<div id="TwoStatusDiv">
<div id="TwoStatusDiv0" style="background-color:#FFF; padding:10px 0 10px 0;">
  <table width="981" border="0" cellspacing="0" style="color:#333333; line-height:22px;">
  </table>
  <div style="margin-top:12px;" id="pageLink"></div>
</div>

<div id="TwoStatusDiv1" style="background-color:#FFF; padding:10px 0 10px 0;">
  <table width="981" border="0" cellspacing="0" style="color:#333333; line-height:22px;">
  </table>
   <div style="margin-top:12px;" id="pageLink2"></div>
</div>
</div>
</div>
<script type="text/javascript">BindData("TwoStatus");Csh("TwoStatus");</script>
 <script type="text/javascript" language="javascript">
           var  firstPageIndex=1;
           var  linkHTML=""; 
		   var  totalpage="<{$totalpage}>";
		   var  totalpage2="<{$totalpage2}>";
		   var  total="<{$total}>";
		   var  total2="<{$total2}>";
		   var  urls= "member.php?view=ajaxtenderstatus&id=<{$params.id}>&t=1";
		   var  urls2= "member.php?view=ajaxtenderstatus&id=<{$params.id}>&t=2";
			$(document).ready(function(){    
				getHouseInfo(firstPageIndex,totalpage,total,urls,"TwoStatusDiv0","pageLink");  
				getHouseInfo(firstPageIndex,totalpage2,total2,urls2,"TwoStatusDiv1","pageLink2");  
			 }); 
      </script>










</div-->
<!--</div>-->
<{include file="../footer.html" }>	


	
<script>
 	var totalpage = "<{$totalpage}>";
function xiajia(a){
	if(confirm("是否确认下架?" )){
		var myDate = new Date();
		var strDate1 = myDate.format('yyyy-MM-dd hh:mm:ss');

		var strDate2= "<{$zy.CreateDate}>";

		endDate=Date.parse(strDate1.replace(/\-/g, "/"));
		startDate=Date.parse(strDate2.replace(/\-/g, "/"));
		var sjc= (endDate-startDate)/(60*60*1000);
	//	if(sjc < 2){
	//		alert("资源上架时间不足两个小时，暂时不能下架");
	//		return false;
	//	}else{
			location.href="member.php?action=xiajiatc&id="+a;
	//	}
	}
}

//getpz(document.getElementById("VarietyCode2"));

function fsxx(flag,ddtype){

//	alert(flag);
	window.open("member.php?view=fsxx&id="+flag + "&type="+ddtype ,'','scrollbars=yes,width=780,height=500,left=300,top=30');return false;
}


//资源上架

function shangjia(obj){
//document.getElementById("loading").style.display="block";

	var array = document.getElementsByName("plzyid");
	
	 var id = "";
//	 var jg = document.getElementById("pljg").value;
//	 var lxr = document.getElementById("pllxr").value;

if(document.getElementById("xid")){
if(document.getElementById("xid").value == ""){
alert("请保存协议信息");
document.getElementById("loading").style.display="none";
return false;
}
}
	
	 for (var i = 0; i < array.length; i++) {
		id += array[i].value+",";
		}
if(id == "," || id == ""){
	alert("资源信息不能为空");
	document.getElementById("loading").style.display="none";
	return false;
}
//if(jg == ""){
//	alert("交割信息不能为空");
//	return false;
//}
//if(lxr == ""){
//	alert("联系人信息不能为空");
//	return false;
//}
var ed = document.zyform.SalesEndDate.value;
if(CompareDate("<{$enddate}>",ed)){
alert("结束日期已过期，请修改销售日期");
document.getElementById("loading").style.display="none";
return false;
}

	//added by hezp saletype=2 started 2015/11/23
	var sta = document.zyform.SalesStartDate.value;
	var trade = $("input[name=TradeType]:checked").val();
	if(trade==2){
		<{if $params.type==4 || $zy.SalesType==2}>
		var ed = document.zyform.SalesEndDate.value;
		var date1 = new Date(sta.replace(/-/g,"/"));
		var date2 = new Date(ed.replace(/-/g,"/"));
		var diff = ((date2.getTime()) - (date1.getTime()))/(60*60*1000);
		if(diff >= 4){}else{
			alert("竞价结束日期应大于开始日期4小时以上");
			return false;
		}
		<{/if}>
	}
	//added by hezp saletype=2 ended 2015/11/23	

 if(document.getElementById("jgcity").innerText == ""){
	 alert("交割信息不能为空");
	 document.getElementById("loading").style.display="none";
	 return false;
	 }
// if(document.userform22.ContactMan.value == ""){
//	 alert("联系人信息不能为空");
//	 return false;
//	 }


if(document.getElementById("mima").value == ""){
alert("交易密码不能为空");
document.getElementById("loading").style.display="none";
return false;
}

//obj.style.display="none";
 document.getElementById("btn_sjtj").src="images/btn_sjtj_gray.png";
 document.getElementById("btn_sjtj").onclick="";
//document.getElementById("loading").style.display="none";
 document.getElementById("loading").style.display="";

	 var mima = document.getElementById("mima").value;
	     var param = "action=shangjiatc&pid=<{$params.id}>&mima="+mima;

	     var ajax = new Ajax("member.php", setSj, param );
 }
 function setSj(returnstr){
		//alert(returnstr);
		String.prototype.trim=function() {

			return this.replace(/(^\s*)|(\s*$)/g,'');
		}
		document.getElementById("loading").style.display="none";
		var str = returnstr.trim();
		var str1=str.split("&&");
		if(str1[0] == "1"){
			alert("已上架");
			window.location.href="member.php?view=newzylist";
			//added by shizg for qydxtz started 2016/11/16
        <{if ($zy.SalesType == "8" || $params.type == "8" )&& $zy.Isdxcg==1}>

		<{else}>
			<{if $zy.TradeType==2 }>
				<{if $AllowSendJingJiaSms==1}>
				 window.open('member.php?view=qydxtz&sid=<{$params.id}>&isdantiao=0','','scrollbars=no,width=650,height=650,left=500,top=50');
				 //window.open("member.php?view=qydxtz&mid="+'<{$params.id}>','','scrollbars=no,width=850,height=550,left=400,top=50');
				<{/if}>
			<{/if}>
		<{/if}>
			//added by shizg for qydxtz ended 2016/11/16


		}else if(str1[0] == "2"){
			alert("交易密码有误，请重新输入");
			location.reload();
		}else if(str1[0] == "P0001"){
			alert("不满足集团要求！"+ str1[1] +"\n资源无网价！请您与管理员联系！");
			location.reload();
		}else{
			alert("推送资源不能修改");
			location.reload();
		}

}



function CompareDate(d1,d2)
{
  return ((new Date(d1.replace(/-/g,"/"))) > (new Date(d2.replace(/-/g,"/"))));
}





function qrjj(){

var dels="";
var m=1;
var s = false;
 var h = document.getElementsByName("jj");
//Added for quanxian sheding  by hxy started  2015/1/13
 var pwd=document.getElementById("qrjjpwd").value;
//Added for quanxian sheding  by hxy ended  2015/1/30
if(h.length) 
{
		for (var i=0;i<h.length;i++){
			var e=h[i];
			if (e.checked){
			dels+=e.value+",";
			m++;
			s=true;
			}
		}
	}
	else{
	if(h.checked){
	dels += h.value+",";
			s=true;}
	}

	if(m > 2 ){
	
	alert("竞价资源只能同时选择一家竞价公司");
	return false;
	}

	
	
if(s){ 
	//Added for quanxian sheding  by hxy started  2015/2/3
	if(pwd==''){
	   alert('交易密码不能为空');
	   return false;
	   }
	 //Added for quanxian sheding  by hxy ended  2015/2/3
if(window.confirm("是否确认?" ))
{
		var myDate = new Date();
		var strDate1 = myDate.format('yyyy-MM-dd hh:mm:ss');

		var strDate2= "<{$zy.CreateDate}>";
      //Added for quanxian sheding  by hxy started  2015/1/13
        pwd=faultylabs.MD5(pwd);
		endDate=Date.parse(strDate1.replace(/\-/g, "/"));
		startDate=Date.parse(strDate2.replace(/\-/g, "/"));
		var sjc= (endDate-startDate)/(60*60*1000);
		/*if(sjc < 2){
			alert("上架两小时内不可确认成交");
			return false;
		}else{*/

        //alert("member.php?action=tcqrjj&ID="+dels+"&mid="+'<{$params.id}>');
		 //window.location.href="member.php?action=tcqrjj"&ID="+dels+"&mid="+'<{$params.id}>';
			window.location.href="member.php?action=tcqrjj&pwd="+pwd+"&ID="+dels+"&mid="+'<{$params.id}>';
       //Added for quanxian sheding  by hxy ended  2015/1/30 
		//}
		
	

}else{
return false;
}
}else{
alert("请选择一条记录");
return false;
}

}


function show_sta(item){
	    document.getElementById("othsta").style.display = "none";
        document.getElementById("mysta").style.display = "none";

	    document.getElementById(item).style.display = "block";
        
		document.getElementById("mysta_item").className = "cut4";
		document.getElementById("othsta_item").className = "cut4";
	

document.getElementById(item+"_item").className = "cut3";

		

}


function setbzjflag(m){
var f = document.forms["zyform"];
 if(m == "2"){

	document.getElementById("baozj").innerHTML="";
	f.LyMoney.value = "";
	f.LyMoney.disabled="disabled";

 }else{
 document.getElementById("baozj").innerHTML="*";
 f.LyMoney.value ="";
 f.LyMoney.disabled = false;
  //f.LyMoney.readonly = false;

 }
}

function chejdt(v){
	if(v=="4"){
		document.getElementById("xhhk").style.display = "none";
		document.getElementById("xkhh").style.display = "";	
		document.getElementById("LyMoneyid").value="0";
	}else{
		
		document.getElementById("xkhh").style.display = "none";
		document.getElementById("xhhk").style.display = "";
		document.getElementById("LyMoneyid").value="100";
	}
	
}
function checkval(obj){

	if(isNaN(obj.value) || parseInt(obj.value)>100 || parseInt(obj.value)<1){
		obj.value="";
		obj.focus();
	}

}

</script>
<!--added by shizg for searchck started 2016/09/20  -->
<script>
function searchck(){
 var ckname=document.getElementById("ckname").value;
 var province=document.getElementById("province").value;
 var city=document.getElementById("city").value;
 var address=document.getElementById("address").value;
 var url="member.php?view=searchck&ckname="+ckname+"&province="+province+"&city="+city+"&address="+address;
 //console.log(url);
  $.get(url, function(result){
    // console.log( result);
	 //document.getElementById("searchcklist").HTML=result;
	 //document.getElementById("searchcklist").innerHTML=
	 //$(".searchcklist").html(result);
	 $("#contacttab").html(result);
  });
 // $("#searchcklist").innerHTML=result;
}
</script>

<!--added by shizg for searchck ended 2016/09/20  -->

<{if ($params.type == "2" || $zy.TradeType == "2")}>  
<script>
<{if $params.id }>

//closediv('xy');

<{else}>
document.getElementById("jbzydiv").style.display="block";
document.getElementById("isjjshow").style.display="block";
<{/if}>

</script>
<{/if}>
<script>
//Added by quanjw for meijiao start 2015/1/28
function changeURL(){
	var radioValue=$('input[name="Vid"]:checked').val();
	var url = window.location.href;
	url=url.replace("&Vid=<{$MEITAN_VID}>","");
	url=url.replace("&Vid=<{$JIAOTAN_VID}>","");
	url=url.replace("&Vid=<{$SHUINI_VID}>","");
	url=url.replace("&Vid=<{$JINSHU_VID}>","");
	switch(radioValue){
		case '<{$MEITAN_VID}>':
		case '<{$JIAOTAN_VID}>':
		case '<{$SHUINI_VID}>':
		case '<{$JINSHU_VID}>':
		location.href=url+"&Vid="+radioValue;
		break;
		default :
		location.href=url;
	}
}

//Added by quanjw for meijiao end 2015/1/28	


$(document).ready(function () {
    setfkxs();
});
function setfkxs() {
    var ss= document.getElementById("fkxss").value;
    //alert(ss);

    if(ss=="1"){
    document.getElementById("xjhz").style.display='';
    }else if(ss=="2"){
    document.getElementById("yhcd").style.display='';
    }else if(ss=="3"){
    document.getElementById("other").style.display='';
    }
    setcdhp(ss,'1');

    var dd= document.getElementById("FuKuanXingShi").value;
    if(dd=="1" || dd=="2" || dd=="3"){
    }
    
    if( dd=="7" || dd=="8"){
      var obj = document.getElementsByName("FuKuanXingShi");
        for(i = 0; i < obj.length; i++)
        {  

          if(obj[i].value == "0")
          {  
            obj[i].checked = true;
          }  
        }
        danbao(0,1);
    }

    if(dd=="6" || dd=="9"){
        $("input[type=radio][name=FuKuanXingShi][value=6]").attr("checked","checked");
        danbao(dd);
    }
}
function setxk(s){
	if(s=="1" || s=="2" ){
        document.getElementById("fktx").style.display='none';
	}else {
        document.getElementById("fktx").style.display='';
    }
}
function checkedRadio(id){
	
	var chkObjs = document.getElementsByName(id);
	
                for(var i=0;i<chkObjs.length;i++){
                    if(chkObjs[i].checked){
                        chk = i;
						return chkObjs[i].value;
                    }
                }
}
function setcdhp(s,t){
    
    if(s == "1"){//付款方式
        if(t=='1'){}else{
        $("input[type=radio][name=FuKuanXingShi][value=1]").attr("checked","checked");
        }
        var i=checkedRadio("FuKuanXingShi");
        setxk(i);
        
        document.getElementById("xjhz").style.display='';
        
        document.getElementById("yhcd").style.display='none';
       
        document.getElementById("other").style.display='none';
        document.getElementById("other3").style.display='none';	

    }else if(s == "2"){
        if(t=='1'){}else{
        $("input[type=radio][name=FuKuanXingShi][value=4]").attr("checked","checked");
        }
        document.getElementById("fktx").style.display='';
        document.getElementById("yhcd").style.display='';
        
        document.getElementById("xjhz").style.display='none';

        document.getElementById("other").style.display='none';
        document.getElementById("other3").style.display='none';	

       
    }else if(s == "3"){
        if(t=='1'){}else{
        $("input[type=radio][name=FuKuanXingShi][value=6]").attr("checked","checked");
        }
        document.getElementById("fktx").style.display='';
        document.getElementById("other").style.display='';	
        
        document.getElementById("xjhz").style.display='none';

        document.getElementById("yhcd").style.display='none';
        
    }else{
        document.getElementById("xjhz").style.display='none';
        
        document.getElementById("yhcd").style.display='none';
       
        document.getElementById("other").style.display='none';
        document.getElementById("other3").style.display='none';	
        document.getElementById("fktx").style.display='none';
    }
}
function danbao(s,t){
	if(s=="0"){
        if(t=='1'){}else{
        $("input[type=radio][name=FuKuanXingShi2][value=7]").attr("checked","checked");
        }
		document.getElementById("other3").style.display='';
	}else{
		document.getElementById("other3").style.display='none';	
	}
}
</script>