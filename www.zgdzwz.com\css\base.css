﻿@charset "utf-8";
/* CSS Document */


body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,p,pre,code,form,fieldset,legend,input,textarea,p,blockquote,th,td,hr,button,article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{
    margin: 0;
    padding: 0;
}

h1,h2,h3,h4,h5,h6,i,b,em{ 
    font-style: normal; 
    font-weight: normal; 
    font-size: 12px;
}
body,button,input,select,textarea,table{ 
	font: 12px "宋体",Arial, Helvetica, sans-serif;
}
body{
	color: #515151;
	background: transparent;
}
ul,ol,dl,li{ list-style: none;}
a:link,a:visited{
	text-decoration: none;
	color: #515151;
}
a:hover{
	color: #c00;
}
input,textarea,button,select,label,span,em,b,img{
	vertical-align: middle;
	color: #000000;
}


/* 针对特定ID的select样式优化 */
#parentid,
#secparentid {
	padding: 6px 8px;
	font-size: 12px;
	line-height: 1.4;
	color: #333;
	background-color: #fff;
	border: 2px solid #ccc;
	border-radius: 4px;
	min-height: 32px;
	min-width: 60px;
	box-sizing: border-box;
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
	background-repeat: no-repeat;
	background-position: right 8px center;
	background-size: 12px;
	padding-right: 30px;
}

#parentid:hover,
#secparentid:hover {
	border-color: #999;
	background-color: #f9f9f9;
}

#parentid:focus,
#secparentid:focus {
	outline: none;
	border-color: #0075e1;
	box-shadow: 0 0 0 2px rgba(0, 117, 225, 0.2);
	background-color: #fff;
}

#parentid:active,
#secparentid:active {
	border-color: #0075e1;
}
table{
	border-spacing: 0;
	border-collapse: collapse;
	
	width: 100%;
}
img,iframe{ border: none;}
iframe{ background: #fff;}
.fl{ float: left;}
.fr{ float: right;}
.fb_btn_fr{ float: right;margin-left: 20px;height: 28px;}
.tc{ text-align: center;}
.tl{ text-align: left;}
.tr{ text-align: right;}
.oh{ overflow: hidden;}
.clear{
	clear: both;
	overflow: hidden;
	height: 0;
}

/*  margin  */
.mt5{ margin-top: 5px;}
.mb5{ margin-bottom: 5px;}
.mt10{ margin-top: 10px;}
.mb10{ margin-bottom: 10px;}
.mt15{ margin-top: 15px;}
.mb15{ margin-bottom: 15px;}
.mt20{ margin-top: 20px;}
.mb20{ margin-bottom: 20px;}

/*  width  */
.wrap{
	width: 100%;
	clear: both;
}
.w1000,.container{ 
	width: 1200px;
	margin-left: auto;
	margin-right: auto;
}
.w245,.area245{ width: 34%;}
.w745,.area745{ width: 65%;} 
.w845,.area845{ width: 70%;} 
.area790{ width: 70%;}

/*  border  */
.bd_dc,.bd_d6{	border: 1px solid #dcdddd;}

/*  background  */
.bg_f9,.bg_f8{ background: #f9f9f9;}
.bg_fff{ background: #fff;}

/*  color  */
.c00,.c00 a{ color: #c00;}

/*  line-height  */
.lh20{ line-height: 20px;}
.lh22{ line-height: 22px;}
.lh24{ line-height: 24px;}
.lh26{ line-height: 26px;}
.lh28{ line-height: 28px;}
.lh30{ line-height: 30px;}


/* =======================================  header  ========================================== */
.header .topNav{
	background: url(../img/topNav_bg.jpg) repeat-x;
	border: solid #dcdddd;
	border-width: 0 1px 1px 1px;
	width: 998px;
	height: 32px;
	line-height: 32px;
	position: relative;
}
/* ====== topNavLeft ====== */
.header .topNavLeft{
	_position: relative;
	_top: 5px;
	margin-left: 10px;
	_display: inline;
}
.header .topNavLeft label{ 
	float:left;
	margin-right: 15px;
}
.header .topNavLeft input[type=text],
.header .topNavLeft input[type=password]{
	width: 79px;
	height: 17px;
	line-height: 17px\9;
	border: 1px solid;
	border-color: #bababa #e3e3e3 #e3e3e3 #bababa;
	text-indent: 4px;
	position: relative;
	top: -1px;
}
.header .topNavLeft input.inputText{
	_width: 79px;
	_height: 17px;
	_border: 1px solid;
	_border-color: #bababa #e3e3e3 #e3e3e3 #bababa;
	_line-height: 17px;
	_text-indent: 4px;
}
.header .topNavLeft input[type=image]{
	position: relative;
	top: -1px;
	left: -6px;
}

/* ====== topNavRight ====== */
.header .topNavRight{
	margin-right: 3px;
	_display: inline;
}
.header .topNavRight li{
	float: left;
	background: url(../img/topNavRight_line.png) no-repeat 0 center;
	padding: 0 8px 0 10px;
}
.header .topNavRight li.shoppingCart{
	background: url(../img/icon_triangle_dw_gray.png) no-repeat 64px 14px;
	padding: 0 20px 0 0;
	margin-right: 2px;
	position: relative;
	z-index: 99;
}
.header .topNavRight li.shoppingCart:hover{
	background: #fff url(../img/icon_triangle_up_gray.png) no-repeat 74px 14px;
	border-left: 1px solid #dcdddd;
	padding-left: 10px;
}
.header .topNavRight li.shoppingCart:hover div{
	visibility: visible;
}
	
.header .topNavRight li.shoppingCart > a{
	background: url(../img/icon_shoppingCart_topNavRight.png) no-repeat 0 center;
	padding: 4px 0 4px 23px;
}
.header .topNavRight li.shoppingCart div{
	position: absolute;
	top: 32px;
	right: -266px;
	width: 354px;
	height: auto;
	background: #fff;
	z-index: 9999;
	padding: 5px 0 5px 0;
	*padding-bottom: 0;
	visibility: hidden;
}
.header .topNavRight li.shoppingCart table{
	margin-bottom: 5px;
}
.header .topNavRight li.shoppingCart table td{
	height: 26px;
	line-height: 26px;
}

/* ====== logoBar ====== */
.header .logoBar{
	height: 80px;
	margin: 10px auto;
}
.header .logoBar .logo{
	margin: 1px 0 0 10px;
	_display: inline;
}


/* ====== menuBar ====== */
.header .menuBar{
	background: url(../img/menuBar_bg.png) repeat-x;
	height: 38px;
}
.header .menuBar li{
	float: left;
	background: url(../img/menuBar_line.png) no-repeat 0 center;
	padding-left: 1px;
	text-align: center;
}
.header .menuBar li:first-child{
	background: none;
	width: 65px;
}
.header .menuBar li a{
	display: block;
	height: 38px;
	line-height: 38px;
	padding: 0 9px 0 9px;
	color: #fff;
	font-family: "微软雅黑","宋体";
	font-size: 13px;
	_width: 83px;
	_padding: 0;
}
.header .menuBar li a.on,
.header .menuBar li a:hover{
	background: url(../img/menuBar_on.png) no-repeat center 0;
}


.btn_tick a{
	display: block;
	background: url(../img/btn_tick.jpg) no-repeat;
	border: 1px solid #ff8c17;
	height: 20px;
	color: #fff;
	line-height: 20px;
	text-indent: 20px;
	overflow: hidden;
}

/* ====== dealNews ====== */
.header .dealNews{
	border-top: none;
	background: url(../img/topNav_bg.jpg) repeat-x;
	height: 31px;
	line-height: 31px;
	padding: 0 5px;
	width: 987px;
	font-family: Arial, Helvetica, sans-serif;
}
.header .dealNews b{
	color: #c00;
	margin: 0 5px;
}
.header .dealNews li.tel{
	background: url(../img/icon_telephone.png) no-repeat 0 center;
	text-indent: 21px;
}


.header .realtimePrice{
	border-top: none;
	-webkit-box-shadow: 0 2px 3px #eee;
	-moz-box-shadow: 0 2px 3px #eee;
	box-shadow: 0 2px 3px #eee;
}
/* ====== 
.header .realtimePrice table{
	border-left: 1px solid #dcdddd;
	line-height: 14px;
}
.header .realtimePrice table td{
	border-bottom: 1px solid #dcdddd;
	border-right: 1px solid #dcdddd;
}
.header .realtimePrice thead tr{
	background: #f9f9f9;
}
.header .realtimePrice thead td{
	padding: 4px 0;
}
.header .realtimePrice table th{
	font-weight: normal;
	border-bottom: 1px solid #dcdddd;
	border-right: 1px solid #dcdddd;
}实时工程价格 ====== */




/*  ================= location ==================  */
div.location{ 
	margin-top: 10px;
	background: url(../img/icon_location_bg.png) no-repeat 15px center;
	text-indent: 37px;
	height: 15px;
	line-height: 15px;
}
div.location span{
	vertical-align: inherit
}


/* ===============================================  footer ================================================== */
.footer{
	margin-top: 30px;
	padding-bottom: 20px;
}
.footer .helpCenter{
	padding: 30px 0 28px 0;
}
.footer .helpLeft dl{
	float: left;
	background: url(../img/icon_footer_helpCenter_line.png) no-repeat 0 10px;
	overflow: hidden;
	padding: 0 35px 0 40px;
}
.footer .helpLeft dl:first-child{
	background: none;
}
.footer .helpLeft dt{
	font-family: "微软雅黑";
	font-size: 14px;
	margin-bottom: 10px;
}
.footer .helpLeft dd{
	clear: both;
	line-height: 24px;
}
.footer .helpLeft p{
	clear: both;
}
.footer .helpRight li{
	float: left;
	text-align: center;
	width: 120px;
}
.footer .helpRight p{
	color: #7f7f7f;
	padding-top: 8px;
	line-height: 16px;
}
.footer .helpRight img{
	width: 100px;
	height: 100px;
	background: #fff;
	-webkit-box-shadow: 0 1px 5px #999;
	-moz-box-shadow: 0 1px 5px #999;
	box-shadow: 0 1px 5px #999;
}

.footer .links{
	border-top: 1px solid #dcdddd;
	border-bottom: 1px solid #dcdddd;
	height: 36px;
	line-height: 36px;
}
.footer .links a{
	margin: 0 4px;
}

.footer .footerNav{
	padding: 8px 0;
}
.footer .footerNav p{
	clear: both;
	height: 26px;
	line-height: 26px;
}
.footer .footerNav span{
	margin: 0 5px;
}

.footer .copyRight{
	*padding-left: 420px;
	*width: 580px;
}
.footer .copyRight li{
	display: inline-table;
	margin: 0 6px;
	*float: left;
	_width: 90px;
}
.footer .copyRight p{
	margin-top: 8px;
}







/* ====== 关键字搜索 ====== */
div.keySearch{
	position: relative;
}
div.keySearch .tabs{
	height: 31px;
	position: relative;
	width: 1000px;
	left: -1px;
}
div.keySearch .tabs li{
	float: left;
	height: 30px;
	line-height: 30px;
	background: url(../img/keySearch_title_li.jpg) repeat-x;
	border-left: 1px solid #dcdddd;
	border-bottom: 1px solid #dcdddd;
	text-align: center;
	cursor: pointer;
	width: 165px;
}
div.keySearch .tabs li a{
	display: block;
	color: #515151;
}
div.keySearch .tabs li.on{
	background: #fff;
	border-bottom: 1px solid #fff;
	font-weight: bold;
	width: 168px;
}
div.keySearch .tabs li.on a{
	font-weight: bold;
}

div.keySearch .content{
	padding: 5px 0;
}
div.keySearch .content input[type=text]{
	width: 620px;
	height: 26px;
	line-height:26px;
	//text-indent: 35px;
	padding-left:35px;
	background: #fff url(../img/icon_search_bg.jpg) no-repeat 15px center;
	border: 1px solid #dcdddd;
	*line-height: 26px;
	margin: 0 8px 0 7px;
}
div.keySearch .content input.inputText{
	_width: 620px;
	_height: 26px;
	_text-indent: 35px;
	_background: #fff url(../img/icon_search_bg.jpg) no-repeat 10px center;
	_border: 1px solid #dcdddd;
	_line-height: 26px;
	_margin: 0 8px 0 7px;
}
div.keySearch p{
	clear: both;
	overflow: hidden;
  /*del by xiakang for dazong started 2015/09/01
    margin-top: 11px;
  del by xiakang for dazong started 2015/09/01*/
	margin-bottom: 11px;
}
div.keySearch p.enter,
div.keySearch p.hot{
	margin-left: 115px;
}
div.keySearch p.hot{
	text-indent: 50px;
}
div.keySearch p.hot a{
	margin-right: 6px;
}


/*  ============================ 产品筛选  =============================== */
/* ====== 产品筛选 ====== */
div.productFilter{
	border-bottom: none;
}
div.productFilter dl{
	clear: both;
	display: block;
	overflow: hidden;
	border-bottom: 1px solid #dcdddd;
}
div.productFilter dt{
	float: left;
	width: 92px;
	text-align: right;
	padding: 11px 3px 0 0;
}
div.productFilter dd{
	float: left;
	border-left: 1px solid #dcdddd;
	background: #fff;
	width: 827px;
	position: relative;
	padding: 6px 70px 6px 5px;
}
div.productFilter a{
	display: inline-block;
	margin: 0 8px;
	white-space: nowrap;
	line-height: 24px;
}
div.productFilter a.on{
	color: #c00;
}
div.productFilter a.more{
	position: absolute;
	top: 5px;
	right: 5px;
	background: url(../img/icon_triangle_dw_gray.png) no-repeat right 10px;
	padding-right: 11px;
}
div.productFilter a.more:hover{
	background: url(../img/icon_triangle_up_red.png) no-repeat right 10px;
}

/*  已选择条件  */
div.productFilter .selected dt{
	padding-top: 12px;
}
div.productFilter .selected dd{
	padding: 8px 0 2px 0;
	width: 903px;
	border-left: none;
	background: #f9f9f9;
	min-height: 29px;
}
div.productFilter .selected a{
	border: 1px solid #dcdddd;
	height: 20px;
	line-height: 20px;
	background: #fff url(../img/icon_productFilter_delete.png) no-repeat right center;
	padding: 0 18px 0 6px;
	margin: 0 2px 7px 0;
}
div.productFilter .selected a:hover{
	border: 1px solid #c00;
	background: #fff url(../img/icon_productFilter_delete_hover.png) no-repeat right center;
	color: #c00;
}
div.productFilter .selected a.empty{
	position: absolute;
	top: 9px;
	right: 9px;
	background: url(../img/icon_productFilter_empty.png) no-repeat 0 center;
	padding: 0 0 0 18px;
	border: none;
}
div.productFilter .selected a.empty:hover{
	background: url(../img/icon_productFilter_empty_hover.png) no-repeat 0 center;
	color: #c00;
	border: none;
}

div.productFilter .selected a.empty1{
	border:none;
	height: 20px;
	line-height: 20px;
	background: url(../img/btn_plus.jpg) no-repeat left center;
	padding: 0 0 0 20px;
}
div.productFilter .selected a.empty1:hover{
	background: url(../img/btn_minus.jpg) no-repeat 0 center;
	color: #c00;
	border: none;
}



/* ==================  产品排序 ===================== */
div.productSort{
	height: 36px;
	line-height: 36px;
	position: relative;
	padding: 0 20px;
}
div.productSort .fl span{
	float: left;
	margin-right: 3px;
}
div.productSort .fl a{
	display: block;
	float: left;
	border: 1px solid #ccc;
	height: 20px;
	line-height: 20px;
	text-align: center;
	padding: 0 10px;
	margin-right: 6px;
	margin-top: 7px;
	background: #fff;
}
div.productSort .fl a:hover{
	border: 1px solid #c00;
	color: #c00;
}
div.productSort .fl a.on{
	background: #c00;
	border: 1px solid #c00;
	color: #fff;
	font-weight: bold;
}
div.productSort .fr span{
	float: left;
	display: block;
}
div.productSort .fr b{
	position: relative;
	top: -1px;
	*top: 0;
}
div.productSort .fr span.total{
	margin-right: 10px;
}
div.productSort .fr span.total b{
	color: #c00;
	margin: 0 6px 0 5px;
}
div.productSort .fr span.num{
	margin: 0 10px;
}
div.productSort .fr span.num b{
	color: #c00;
	font-weight: normal;
	margin: 0 4px 0 2px;
}
div.productSort .fr span.page a{
	display: block;
	width: 56px;
	height: 20px;
	border: 1px solid #ccc;
	line-height: 20px;
	margin-left: 6px;
	margin-top: 7px;
	float: left;
	color: #ccc;
}
div.productSort .fr span.page a:hover{
	color: #c00;
}
div.productSort .fr span.page a.pre{
	background: #fff url(../img/icon_triangle_left_gray.png) no-repeat 4px center;
	text-indent: 14px;
}
div.productSort .fr span.page a.pre:hover{
	background: #fff url(../img/icon_triangle_left_red.png) no-repeat 4px center;
}
div.productSort .fr span.page a.next{
	background: #fff url(../img/icon_triangle_right_gray.png) no-repeat 47px center;
	text-indent: 6px;
}
div.productSort .fr span.page a.next:hover{
	background: #fff url(../img/icon_triangle_right_red.png) no-repeat 47px center;
}


/* ==================  产品类型 =================== */
div.productType{
	border-top: none;
	height: 36px;
	line-height: 36px;
	padding: 0 20px;
}
div.productType span{
	float: left;
	margin-right: 3px;
}
div.productType a{
	display: block;
	float: left;
	background: url(../img/icon_productType_a.png) no-repeat 0 center;
	padding-left: 18px;
	margin-right: 12px;
}
div.productType a.on{
	background: url(../img/icon_productType_on.png) no-repeat 0 center;
}
div.productType a:hover{
	color: #515151;
}



/* ======  产品列表、已成交资源 productList、dealtResource ====== */
div.productList a{ color: #515151;}
div.productList table,
div.dealtResource table{
	width: 100%;
	background: url(../img/icon_productList_f9_32.jpg) repeat 0 32px;
}
div.productList thead tr,
div.dealtResource thead tr{
	background: url(../img/productList_table_thead.jpg) repeat-x;
}
div.productList thead tr td,
div.dealtResource thead tr td{
	border-bottom: 1px solid #dcdddd;
	background: #fff;
}
div.productList table tr,
div.dealtResource table tr{
	height: 32px;
	cursor: pointer;
}
div.productList tbody img,
div.dealtResource tbody img{
	margin: 0 3px;
}
div.productList tr:hover dl.pop,
div.dealtResource tr:hover dl.pop{
	visibility: visible;
}

/*  productList 弹出  */
div.productList dl.pop,
div.dealtResource dl.pop{
	position: absolute;
	border: 1px solid #3460B9;
	width: 325px;
	height: auto;
	padding-bottom: 4px;
	background: #fff;
	margin: 23px 0 0 40px;
	*margin-top: 24px;
	text-align: left;
	visibility: hidden;
	z-index: 9999;
}
div.productList dl.pop span,
div.dealtResource dl.pop span{
	position: absolute;
	background: url(../img/icon_triangle_up_blue.png) no-repeat;
	width: 7px;
	height: 4px;
	top: -5px;
	left: 10px;
	display: block;
}
div.productList dl.pop dt,
div.dealtResource dl.pop dt{
	height: 26px;
	line-height: 26px;
	color: #fff;
	background: #3460B9;
	text-indent: 10px;
}
div.productList dl.pop dt a,
div.dealtResource dl.pop dt a{
	color: #fff;
}
div.productList dl.pop dd,
div.dealtResource dl.pop dd {
	padding: 4px 10px 0 10px;
	line-height: 22px;
	overflow: hidden;
}
div.productList dl.pop dd p,
div.dealtResource dl.pop dd p{
	clear: both;
}
	
	
/*  productList 全选  */
div.productList .select{
	padding: 0 10px 0 13px;
	height: 45px;
	line-height: 45px;
}
div.productList .select .fl{
	_padding-top: 11px;
}
div.productList .select .fl label{
	*margin-left: -2px;
	_margin-left: -3px;
	margin-right: 8px;
}
div.productList .select .fl label input{
	margin-right: 14px;
	*margin-right: 10px;
} 
div.productList .select .fl a{
	margin-left: 4px;
}
div.productList .select .fr{
	_padding-top: 11px;
}
div.productList .select .fr input[type=text]{
	border: 1px solid #dcdddd;
	height: 18px;
	line-height: 18px;
	text-align: center;
	width: 40px;
	position: relative;
	top: -2px;
	*top: 0;
}
div.productList .select .fr input.inputText{
	_border: 1px solid #dcdddd;
	_height: 18px;
	_line-height: 18px;
	_text-align: center;
	_width: 40px;
}
div.productList .select .fr span{
	margin: 0 5px;
}
div.productList .select .fr b{
	position: relative;
	top: -1px;
	*top: 0;
	font-weight: bold;
	color: #c00;
	margin: 0 5px;
}
div.productList .select .fr a{
	cursor: pointer;
}


/* ====== 已成交资源 ====== */
div.dealtResource .tabs li{
	width: 105px;
}
div.dealtResource table tr td:first-child{
	padding-left: 10px;
}
div.dealtResource dl.pop{
	margin: 23px 0 0 0px;
}



div.productArea{
	width: 100%;
}
/* ======================== area245 ======================= */

/* ====== sameTit ====== */
.sameTit{
	background: url(../img/productList_table_thead.jpg) repeat-x;
	height: 52px;
	border-bottom: 1px solid #dcdddd;
	line-height: 52px;
	padding: 0 10px;
}
.sameTit h5{
	float: left;
	font-weight: bold;
	_display: inline;
	display: block;
    width: 120px;
    height: 36px;
    background: #5092f3;
    font-size: 18px;
    font-weight: normal;
    color: #ffffff;
    border-radius: 5px;
    margin: 7px 0 0 0;
    line-height: 36px;
    text-align: Center;
}
.sameTit img{
	float: right;
	margin-top: 11px;
}


div.area245 div.circle div.title,div.executedResource div.title,div.area745 div.member div.title{
	height: 41px;
	line-height: 41px;
	border-bottom: 1px solid #fd8000;
	padding: 0 12px 0 20px;
}
div.area245 div.circle div.title h1,div.executedResource div.title h1,div.area745 div.member div.title h1{
	float: left;
	font-size: 14px;
	font-weight: normal;
	font-family: "微软雅黑";
	background: url(../img/area245_circle3.png) no-repeat 24px bottom;
}
div.area245 div.circle div.title span,div.executedResource div.title span,div.area745 div.member div.title span{
	float: left;
	font-size: 14px;
	color: #ccc;
	margin: 1px 0 0 6px;
	font-family: Verdana, Arial, Helvetica, sans-serif;
	text-transform: capitalize;
}
div.area245 div.circle div.title img{
	float: right;
	margin-top: 18px;
}


/*  =================== page  =================== */
div.page{ 
	padding:3px; 
	font-FAMILY:Tahoma,Helvetica,sans-serif; 
	text-align:center;
}
div.page A{ 
	border:#ccdbe4 1px solid; 
	padding: 2px 8px; 
	background-POSITION:50% bottom; 
	color:#0061de; 
	margin-right:3px; 
}
div.page a:hover{ 
	border:#2b55af 1px solid;
	background-image:none; 
	color:#fff; 
	background-color:#3666d4;
}
div.page a:active{ 
	border:#2b55af 1px solid; 
	background-image:none; 
	color:#fff;  
	background-color:#3666d4;
}
div.page span.current{ 
	padding: 2px 6px;
	font-weight:bold; 
	color:#000; 
	margin-right:3px; 
}
div.page span.disabled{ 
	display:none;
}
div.page a.next{ 
	border:#ccdbe4 2px solid;
	margin:0px 0px 0px 10px; 
}
div.page a.next:hover{ 
	border:#2b55af 2px solid;
}
div.page a.prev{ 
	border:#ccdbe4 2px solid; 
	margin:0px 10px 0px 0px; 
}
div.page a.prev:hover{ 
	border:#2b55af 2px solid;
}



/*
#ajaxdivgwc {
    background-image: url("../images/bj0.gif");
    display: none;
    height: 200px;
    left: 30%;
    margin: 0 auto;
    position: absolute;
    text-align: right;
    top: 50%;
    width: 500px;
    z-index: 1005;
}
*/



/* ==================  弹出窗口  我的购物车 ================== */
div.popupShoppingCart{
	width: 827px;
	height: 450px;
	border: 2px solid #3E6BC3;
	position: fixed;
	_position: absolute;
	top: 50%;
	left: 50%;
	z-index: 10000;
	margin: -167px 0 0 -315px;
	background: #fff;
}
div.popupShoppingCart .change{
	background: url(../img/menuBar_bg.png) repeat-x;
	height: 35px;
	position: relative;
	padding-top: 2px;
	overflow: hidden;
}
div.popupShoppingCart .change li{
	float: left;
	font-size: 14px;
	font-family: "微软雅黑";
	color: #fff;
	width: 103px;
	height: 35px;
	text-align: center;
	line-height: 33px;
	cursor: pointer;
}
div.popupShoppingCart .change li.on{
	background: #fff;
	-o-border-radius: 4px 4px 0 0;
	-moz-border-radius: 4px 4px 0 0;
	-webkit-border-radius: 4px 4px 0 0;
	border-radius: 4px 4px 0 0;
	color: #2B56B0;
	font-weight: bold;
}
div.popupShoppingCart .change li.on a{
	color: #2B56B0;
}
div.popupShoppingCart .change li a{
	color: #fff;
}
div.popupShoppingCart img.close{
	position: absolute;
	top: 10px;
	right: 10px;
	z-index: 999;
}
div.popupShoppingCart div.tableName{
	height: 32px;
	border-bottom: 1px solid #dcdddd;
	background: #fff;
	line-height: 32px;
}
div.popupShoppingCart div.tableDetail{
	height: 219px;
	padding: 0 0 10px 0;
	overflow-y: auto;
	overflow-x: hidden;
}
div.popupShoppingCart div.tableDetail td{
	height: 28px;
}
div.popupShoppingCart div.tableDetail table{
	width: 810px;
}
div.popupShoppingCart div.tableDetail input[type=text]{
	width: 40px;
	height: 17px;
	border: 1px solid #ccc;
	background: #fff;
	text-indent: 3px;
}
div.popupShoppingCart div.tableDetail input.inputText{
	_width: 40px;
	_height: 17px;
	_border: 1px solid #ccc;
	_background: #fff;
	_text-indent: 3px;
}
div.popupShoppingCart div.bottom{
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 35px;
	padding-top: 8px;
	border-top: 1px solid #dcdddd;
	background: #fff;
}
div.popupShoppingCart div.bottom img{
	margin: 0 6px;
}
div.popupShoppingCart td.long{
	text-align: left;
	border-top: 1px solid #dcdddd;
	border-bottom: 1px solid #dcdddd;
	padding-left: 10px;
}
div.popupShoppingCart tr:first-child td.long{
	border-top: none;
}
div.popupShoppingCart td.td1{ width: 230px; text-align: left; padding-left: 10px;}
div.popupShoppingCart td.td2{ width: 95px;}
div.popupShoppingCart td.td3{ width: 95px;}
div.popupShoppingCart td.td4{ width: 65px;}
div.popupShoppingCart td.td5{ width: 60px;}
div.popupShoppingCart td.td6{ width: 45px;}
div.popupShoppingCart div.tableName td.td6{ padding-right: 17px;}


/* =========================== 会员中心 ================================ */
/*div.memberBody{
	background: url(../img/area200_bg.jpg) repeat-y;
}
div.memberBottom{
	background: url(../img/area200_bottom.jpg) no-repeat;
	height: 1px;
	overflow: hidden;
}*/
/* ============= area200 ================ */
div.area200{
	width: 198px;
	border: 1px solid #dcdddd;
}
div.area200 div.memberTit{
	background: url(../img/productList_table_thead.jpg) repeat-x;
	height: 32px;
	line-height: 32px;
	border-bottom: 1px solid #dcdddd;
}
div.area200 div.memberTit h1{
	font-size: 14px;
	font-weight: normal;
	font-family: "微软雅黑";
	background: url(../img/icon_area200_h1.png) no-repeat 55px center;
	text-indent: 77px;
}

/* ====================== newslistbody ========================= */
div.newslistbody{
	background: url(../img/area200_bg.jpg) repeat-y;
}
div.newslistbody div.area200{
	width: 196px;
	border-top: 1px solid #dcdddd;
	padding-left: 1px;
	padding-right: 1px;
	border-bottom: none;
}
div.newslistbody div.area200 div.title{
	background: url(../img/are200_memberTit.jpg) repeat-x;
	height: 36px;
	line-height: 36px;
	border-bottom: 1px solid #dcdddd;
}
div.newslistbody div.area200 div.title h1{
	font-size: 14px;
	font-weight: normal;
	font-family: "微软雅黑";
	text-align: center;
}
div.newslistbody div.area200 div.columnList li{
	clear: both;
	height: 37px;
	background: url(../img/aboutUs_area200_li.jpg) no-repeat center bottom;
	text-indent: 72px;
	line-height: 37px;
}
div.newslistbody div.area200 div.columnList li.on{
	background: url(../img/aboutUs_area200_on.jpg) no-repeat center bottom;
}
div.newslistbody div.area200 div.columnList li.on a{
	color: #fd8000;
}


/*  热点导读  */
div.newsRead div.content{
	border-top: none;
	padding: 12px 0;
}
div.newsRead div.content li{
	clear: both;
	background: url(../img/dian.jpg) no-repeat 12px center;
	text-indent: 25px;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
	width: 230px;
}


/* ================ w245 ================ */

/* ====== 推荐资源 ====== */
.tuijianResource_245 thead td{
	border-bottom: 1px solid #dcdddd;
}
.tuijianResource_245 table td{
	height: 26px;
}
.tuijianResource_245 table td.first{
	padding-left: 8px;
}
.tuijianResource_245 table td.last{
	padding-right: 8px;
}

/* ====== 推荐商家 ====== */
div.tuijianResource_245 .content li{
	clear: both;
	height: 26px;
	line-height: 26px;
	background:url(../img/icon_dian_gray.png) no-repeat 10px center;
	text-indent: 20px;
	text-overflow: ellipsis;
	width: 200px;
	overflow: hidden;
	white-space: nowrap;
}

/* ====== 钢厂专区 ====== */
.imgArea_245 .content li{
	clear: both;
	padding-left: 15px;
	height: 43px;
	overflow: hidden;
	border-top: 1px dashed #dcdddd;
}
.imgArea_245 .content li:first-child{
	border-top: none;
}
.imgArea_245 .content li img{
	height: 43px;
	float: left;
}

/* ====== sameTit ====== */
.sameTit{
	background: url(../img/productList_table_thead.jpg) repeat-x;
	height: 52px;
	border-bottom: 1px solid #dcdddd;
	line-height: 52px;
	padding: 0 10px;
}
.sameTit h5{
	float: left;
	font-weight: bold;
	_display: inline;
	display: block;
    width: 120px;
    height: 36px;
    background: #5092f3;
    font-size: 18px;
    font-weight: normal;
    color: #ffffff;
    border-radius: 5px;
    margin: 7px 0 0 0;
    line-height: 36px;
    text-align: Center;
}
.sameTit img{
	float: right;
	margin-top: 11px;
}

/* ====== 选项卡切换 tabsChange ====== */
div.tabsChange ul.tabs{
	width: 100%;
	clear: both;
	height: 30px;
	background: url(../img/keySearch_title_li.jpg) repeat-x;
	border-bottom: 1px solid #dcdddd;
	position: relative;
}
div.tabsChange ul.tabs li{
	display: block;
	cursor: pointer;
	float: left;
	height: 30px;
	line-height: 30px;
	border-right: 1px solid #dcdddd;
	text-align: center;
	/*Added for sjzq by wangcl started 2015/03/05*/
	width: 94px;
	/*Added for sjzq by wangcl end 2015/03/05*/
}
/*Added for sjzq by wangcl started 2015/03/05*/
div.tabsChange ul.tabs li.on{
	background-color: #5092f3;
	color:white;
}
.tabs .on a{
	color:white;
}
/*Added for sjzq by wangcl end 2015/03/05*/
div.tabsChange ul.tabs li.current{
	background: #fff;
	border-bottom: 1px solid #fff;
	font-weight: bold;
	position: relative;
	top: 0;
}
div.tabsChange .tabscontent{
	clear: both;
	display: none;
	position: relative;
}
div.tabsChange img.icon_more{
	position: absolute;
	top: -21px;
	right: 10px;
}


/* ====== 广告位 ====== */
.ad_header_700_80,
.ad_header_700_80 img{
	width: 700px;
	height: 80px;
}
div.ad_745_100,
div.ad_745_100 img{
	/*Update by xiang xiakang started 2015/11/30*/
	/*width: 745px;
	height: 100px;*/
    width: 1000px;
	height: 140px;
	/*Update by xiang xiakang ended 2015/11/30*/
}

div.ad_245_85,
div.ad_245_85 img{
	width: 245px;
	height: 85px;
}


b.price{
	font-size: 16px;
	margin: 0 7px 0 4px;
	color: #fd8000;
	font-family: "微软雅黑";
	font-weight: bold;
}



/* ==== 进度条 ==== */
div.progressBar{
	width: 1000px;
	margin: 30px auto 20px auto;
	text-align: center;
	height: 115px;
	background: url(../img/icon_progressBar.png) no-repeat center 49px;
}
div.progressBar dl,
div.progressBar dt,
div.progressBar dd{
	width: 100px;
}
div.progressBar dl{
	display: inline-table;
	position: relative;
}
div.progressBar dl dt{
	font-weight: bold;
	color: #999;
	position: absolute;
	top: 0;
}
div.progressBar dl dd{
	clear: both;
	color: #999;
	line-height: 16px;
	margin-top: 50px;
	position: absolute;
	top: 18px;
}
div.progressBar dl span{
	display: block;
	position: absolute;
	top: 25px;
	left: 50%;
	margin-left: -16px;
	width: 32px;
	height: 32px;
	line-height: 32px;
	background: url(../img/icon_progressBar_li.png) no-repeat;
	font-family: Arial, Helvetica, sans-serif;
	color: #fff;
	font-size: 20px;
}
div.progressBar dl.on span{
	background: url(../img/icon_progressBar_li_on.png) no-repeat;
}
div.progressBar dl.on dt{
	color: #c00;
}


/* ====== 钢厂专区logo展示 ====== */
div.factoryShow{
	position: relative;	
	margin-top: 5px;
}
div.factoryShow ul{
	position: relative;
	width: 1005px;
	left: -5px;
}
div.factoryShow li{
	float: left;
	width: 193.7px;
	height: 38px;
	overflow: hidden;
	border: 1px dashed #dcdddd;
	margin: 5px 0 0 5px;
	_display: inline;
}
div.factoryShow li img{
	height: 43px;
	margin-top: -2px;
}


