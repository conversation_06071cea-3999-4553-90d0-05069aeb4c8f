<?php

require_once(APP_DIR . "/bizorder/BizorderBaseAction.inc.php");
require_once(APP_DIR . "/bizorder/BizorderPayAction.inc.php");
require_once(APP_DIR . "/index/IndexAction.inc.php");

class BizorderAction extends BizorderPayAction
{

    // public $mysqli;
    public function __construct()
    {
        parent::__construct();
    }

    //add by xikang started 2016/05/03
    function create_guid()
    {
        $charid = strtoupper(md5(uniQid(mt_rand(), true)));
        $hyphen = chr(45); // "-"
        $uuid = chr(123) // "{"
            . substr($charid, 0, 8) . $hyphen
            . substr($charid, 8, 4) . $hyphen
            . substr($charid, 12, 4) . $hyphen
            . substr($charid, 16, 4) . $hyphen
            . substr($charid, 20, 12)
            . chr(125); // "}" 
        return $uuid;
    }
    //add by xikang ended 2016/05/03

    //$paction=new PaymoneyAction($this->_dao);
    public function checksession()
    {

        if ($_REQUEST['view'] == "order") {
        } else if ($_SESSION['SYS_COMPANYID'] == '') {
            $this->setLastPage();
            goURL("user.php?view=login");
        } else if ($_SESSION['SYS_ROOT'] == "SA") {
        } else {

            if ($_REQUEST['view'] == "apply" || $_REQUEST['action'] == "apply") {
            } else {
                //update for root by wangjian started 2014/09/14
                alert("您现在是一般登录，请切换到交易登录进行交易");
                //alert("交易登录未授权");
                //update for root by wangjian end 2014/09/14
                goURL("user.php?view=welcomelogin");
                exit;
            }
        }
    }

    public function gwc($params)
    {
        $type = $params['type'];
        $this->assign("params", $params);
        if ($_SESSION['SYS_COMPANYID'] == '') {
            //alert( "您还没有登录, 不能使用购物车" );
            //$ref=urlencode("bizorder.php?view=gwc");
            //$ref=urlencode(str_replace("&amp;","&",$params["ref"]));
            //goURL( "user.php?view=login&ref=".$ref  );
        }

        //取得该会员主联系人
        $masteruser = $this->_dao->get_sys_company($_SESSION['SYS_COMPANYID']);
        $comp2 = $this->_dao->get_sys_adminuser($_SESSION['SYS_USERID']);

        $this->assign("comp", $masteruser);
        $this->assign("comp2", $comp2);
        $resids = array_keys($_SESSION['mygwc_buy']);
        $resids2 = array_keys($_SESSION['mygwc_pro']);
        if (count($resids) > 0) {
            //include_once ( APP_DIR . "/ajax/AjaxDao.inc.php" );
            // $dao = new AjaxDao( "SYS" );
            $current_ids = implode(',', $resids);
            $current_ids2 = implode(',', $resids2);

            if ($current_ids2) {
                $data1 = $this->_dao->getResByIds($current_ids, 1);
            } else {
                $data1 = '';
            }

            $data1 = $this->_dao->getResByIds($current_ids, 1);
            if ($current_ids2) {
                $data2 = $this->_dao->getResByIds($current_ids2, 2);
            } else {
                $data2 = '';
            }
        }

        $data1 = $this->gwc_gl($data1);
        $data2 = $this->gwc_gl($data2);
        if (empty($data1) && empty($data2)) {
            echo "您的购物车里还没有加入任何商品";
        }

        $this->assign("gwc", $data1);
        $this->assign("gwc2", $data2);
        $connectinfo = '';
        $connectinfo .= '联系人: ' . $masteruser['ARealName'] . "\r\n";
        $connectinfo .= '联系地址: ' . $masteruser['Address'] . "\r\n";
        $connectinfo .= '联系电话: ' . $masteruser['Mobile'] . "\r\n";
        $this->assign('connectinfo', $connectinfo);
        $this->assign("page_title", "购物车_");
        //print_r($gwc);

    }

    //采购车归类
    public function gwc_gl($data)
    {
        $field = "mygwc_buy";
        return $this->gwc_gl_common($data, $field, "1");
    }

    //供货车归类
    public function gwc_gl2($data)
    {
        $field = "mygwc_pro";
        return $this->gwc_gl_common($data, $field, "2");
    }

    //供货车归类
    public function gwc_gl_common($data, $field, $type)
    {
        $newdata = array();
        $pz = array();
        $lb = array();
        $pzs = $this->_dao->query("select ID,PzName from sys_big_pz  where Status = 1  order by Ssort", 3600);
        // print_r($pzs);
        $npzs = array();
        if (is_array($pzs) || is_object($pzs)) {
            foreach ($pzs as $pz) {
                $npzs[$pz['ID']] = $pz['PzName'];
            }
        }
        foreach ($data as $d) {

			if($d['StoreType']=="2"){
				// $d['buy_sl'.$d['sdid']] =  $_SESSION[$field][$d['ID']]['buy_sl'.$d['sdid']] ==0 ? $d['Quantity']: $_SESSION[$field][$d['ID']]['buy_sl'.$d['sdid']];
                $d['buy_sl'.$d['sdid']] =  $_SESSION[$field][$d['ID']]['buy_sl'.$d['sdid']];

                $d['buy_jg' . $d['sdid']] =  $_SESSION[$field][$d['ID']]['buy_jg' . $d['sdid']] == 0 ? $d['SalesMinPrice'] : $_SESSION[$field][$d['ID']]['buy_jg' . $d['sdid']];

                $d['buy_sl2'] = $d['buy_sl' . $d['sdid']];
                $d['buy_jg2'] = $d['buy_jg' . $d['sdid']];
            }

            $d['pzname'] = $npzs[$d['VarietyCode']];
            // Added for huadong gangshi  by hzp started  2014/12/25
            $sessiongsid = $_SESSION[$field][$d['ID']]['gsid'];
            if ($GLOBALS['MODEMID'][$sessiongsid] != $_SESSION['SYS_COMPANYID']) {
                $d['SalesMinPrice'] = $_SESSION[$field][$d['ID']]['buy_jg' . $d['sdid']] == 0 ? $d['SalesMinPrice'] : $_SESSION[$field][$d['ID']]['buy_jg' . $d['sdid']];
            } else {
                $d['SalesMinPrice'] = $_SESSION[$field][$d['ID']]['SalesMinPrice'] == 0 ? $d['SalesMinPrice'] : $_SESSION[$field][$d['ID']]['SalesMinPrice'];
            }

            $d['ComName'] = $_SESSION[$field][$d['ID']]['ComName'];
            $d['ComName'] =  $d['ComName'];
            if ($type == "2") {
                if ($_SESSION['HDGS']['gsid'] != "") {
                    $VidSID = $d['Pid'] * (-1);
                    $C = $this->_dao->getOne("SELECT SUM(BuyQuantity) as c FROM sm_exc_dd,sm_exc_dd_tag WHERE sm_exc_dd_tag.ID = sm_exc_dd.Dtid and  Sid='" . $VidSID . "' and sm_exc_dd_tag.Did='" . $GLOBALS['MODEMID'][$sessiongsid] . "' and sm_exc_dd_tag.Status=2 ");
                    //查出不在华东钢市购买的数量
                    $CC = $this->_dao->getOne("SELECT SUM(BuyQuantity) as c FROM sm_exc_dd,sm_exc_dd_tag WHERE sm_exc_dd_tag.ID = sm_exc_dd.Dtid and  Sid='" . $d['Pid'] . "' and sm_exc_dd_tag.Did != '" . $GLOBALS['MODEMID'][$sessiongsid] . "' and sm_exc_dd_tag.Mid != '" . $GLOBALS['MODEMID'][$sessiongsid] . "' and sm_exc_dd_tag.Status=2 ");
                    if ($CC == null) {
                        $CC = 0;
                    }
                    if ($C == null) {
                        $C = 0;
                    }
                    $cc = $C + $CC;
                    if ($d['StoreType'] == 2 && $C != 0) {
                        $d['QuantitySalesed'] = $d['QuantitySales'];
                    } else {
                        $d['QuantitySalesed'] = $cc;
                    }
                }

                // Added for huadong gangshi  by hzp end  2014/12/25
                $d['sdid'] = $d['sdid'];
            }
            $d['salestype'] = $d['SalesType'];
            $d['Quantity'] = $d['QuantitySales'] - $d['QuantitySalesed'];
            //$d['lbname'] = $npzs[$d['lb']];
			// $d['buy_sl'] = $_SESSION[$field][$d['ID']]['buy_sl'] == 0 ? $d['Quantity'] : $_SESSION[$field][$d['ID']]['buy_sl'];
            $d['buy_sl'] = $_SESSION[$field][$d['ID']]['buy_sl'];
            $d['buy_jg'] = $_SESSION[$field][$d['ID']]['buy_jg'] == 0 ? $d['SalesMinPrice'] : $_SESSION[$field][$d['ID']]['buy_jg'];
            $d['dddate'] = $_SESSION[$field][$d['ID']]['dddate'];
            $d['ddyxq'] = (strtotime(date("Y-m-d H:i:s", time())) - strtotime($d['dddate'])) / 60; //订单有效期
            $d['zyyxq'] = (strtotime($d['SalesEndDate']) - strtotime(date("Y-m-d H:i:s", time()))) / 60; //资源有效期
            $d['buyzl'] = 0;
            $d['buyzj'] = 0;
            $d['buyzl'] = $d['buy_sl'] + $d['buyzl'];
            $d['buyzj'] =  $d['buy_jg'] * $d['buy_sl'] + $d['buyzj'];

            $newdata[$d['Mid']][] = $d;
        }
        return $newdata;
    }

    public function myorder($params)
    {
        $type = $params['type'] == ""?1:$params['type'];
        $sta  = $params['sta'] == "" ?1 : $params['sta'];
        $compid = $_SESSION['SYS_COMPANYID'];
        $gcm = $_SESSION['SYS_ZQID'];
        $where2 = "";
        $wherex = " where 1 ";
        $mids = "";

        //add by xiakang for zq started 2016/02/25
        if (isZone($params)) {
            $iszone = "1";
        } else {
            $iszone = "2";
        }
        //add by xiakang for zq ended 2016/02/25

        if ($params['type'] == "2" || $params['type'] == "") {
            $buy_field = "Did";
            $pro_field = "Mid";
        } elseif ($params['type'] == "1") {
            $buy_field = "Mid";
            $pro_field = "Did";
        }

        if ($gcm == "" || $compid == $gcm || $iszone == "2") {
            //$where2 .= "and sm_exc_dd_tag.".$buy_field."='".$compid."' ";
            $where2 .= $this->getwhere("sm_exc_dd_tag." . $buy_field, $compid, EQUAL);
        } else {
            //$where2 .= "and sm_exc_dd_tag.".$buy_field."='".$compid."' and sm_exc_dd_tag.".$pro_field."='".$gcm."'";
            $where2 .= $this->getwhere("sm_exc_dd_tag." . $buy_field, $compid, EQUAL);
            $where2 .= $this->getwhere("sm_exc_dd_tag." . $pro_field, $gcm, EQUAL);
        }

        $mid = "sm_exc_dd_tag." . $pro_field . " as mmid";
        //Added by quanjw for addComName start 2015/4/2
        if($params['type'] == "1")
		$leftjoin="left join sys_company on sys_company.id = sm_exc_dd_tag.Did";
	    elseif($params['type'] == "2") {
            $leftjoin="left join sys_company on sys_company.id = sm_exc_dd_tag.Mid";
            if ( $_SESSION['SYS_COMPANYID'] > 0 ) {
                //将未读订单标识为已读
                $this->_dao->execute( " update sm_exc_dd_tag set read_flag = 1 where Did= '".$_SESSION['SYS_COMPANYID']."'");
            }
        }

		//Added by quanjw for addComName end 2015/4/2

        if($sta=="1"){
            $ht_tab = "sm_order_transaction";
            $where2 .= " and sm_exc_dd_tag.Status=1"; //未确认
        } else {
            $ht_tab = "sm_contract_transaction";
        }

        $wherex = ",{$ht_tab} where sm_exc_dd_tag.ID={$ht_tab}.BID and sm_exc_dd_tag.Createdate>='2014-08-28 00:00:00' ";
        $mids = ",{$ht_tab}.Status as Status2";

        if ($sta == "2") { //未完成
            $where2 .= " and sm_exc_dd_tag.Status=2 and sm_contract_transaction.Status!=10  and sm_contract_transaction.Status>=5 ";
        }

        if ($sta == "3") { //一月之内
            $where2 .= " and sm_exc_dd_tag.Status=2 and sm_contract_transaction.Status =10  and TO_DAYS(now())- TO_DAYS(sm_exc_dd_tag.Createdate)<=30 ";
        }

        if ($sta == "4") { //超过一个月
            $where2 .= " and sm_exc_dd_tag.Status=2 and sm_contract_transaction.Status =10  and TO_DAYS(now())- TO_DAYS(sm_exc_dd_tag.Createdate)>30 ";
        }

		if($sta=="5"){ //已取消
            $where2 .=" and sm_exc_dd_tag.Status=4";
		}
        if($sta=="6"){ //欧冶资源已确认订单
            $where2 .=" and sm_exc_dd_tag.Status=6";
		}

        $where2 .= $this->getwhere("OrderNo", $params['orderID'], LIKE);

        //添加公司名称搜索时
        $where3 .= $this->getwhere("ComName", $params['com_Name'], LIKE);

        // added by quanjw start 2016/1/25
        $wherex .= $this->getwhere($ht_tab . ".Status", $params['HT_Status'], EQUAL);
        // added by quanjw end 2016/1/25
        if ($_SESSION['SYS_USERIsLimit'] == "1") {
            $where .= " and sm_exc_dd.CreateUser ='" . $_SESSION['SYS_USERNAME'] . "' ";
        }

        //我的订单列表的小图标===start==
        if ($gcm != "" && $compid != $gcm && $iszone1 = "2") {
            $where_iszone_mid = " and sm_exc_dd_tag.Did='" . $gcm . "' ";
            $where_iszone_did = "and sm_exc_dd_tag.Mid='" . $gcm . "'";
        }
        //未确认订单
        $where_total1 = "and sm_exc_dd_tag.Status=1";
        $total1 = $this->_dao->get_myorder_total_mid($compid, $where_total1, $where_iszone_mid, "sm_order_transaction");
        $total6 = $this->_dao->get_myorder_total_did($compid, $where_total1, $where_iszone_did, "sm_order_transaction");

        //未完成订单
        $where_total2 = " and sm_exc_dd_tag.Status=2 and sm_contract_transaction.Status!=10  and sm_contract_transaction.Status>=5 ";
        $total2 = $this->_dao->get_myorder_total_mid($compid, $where_total2, $where_iszone_mid);
        $total7 = $this->_dao->get_myorder_total_did($compid, $where_total2, $where_iszone_did);

        //一个月之内的订单
        $where_total3 = " and sm_exc_dd_tag.Status=2 and sm_contract_transaction.Status =10  and TO_DAYS(now())- TO_DAYS(sm_exc_dd_tag.Createdate)<=30 ";
        $total3 = $this->_dao->get_myorder_total_mid($compid, $where_total3, $where_iszone_mid);
        $total8 = $this->_dao->get_myorder_total_did($compid, $where_total3, $where_iszone_did);

        //超过一个月的订单
        $where_total4 = " and sm_exc_dd_tag.Status=2 and sm_contract_transaction.Status =10  and TO_DAYS(now())- TO_DAYS(sm_exc_dd_tag.Createdate)>30 ";
        $total4 = $this->_dao->get_myorder_total_mid($compid, $where_total4, $where_iszone_mid);
        $total9 = $this->_dao->get_myorder_total_did($compid, $where_total4, $where_iszone_did);

        //已取消的订单
        $where_total5=" and sm_exc_dd_tag.Status=4 ";
        $total5 = $this->_dao->get_myorder_total_mid($compid,$where_total5,$where_iszone_mid);
        $total10 = $this->_dao->get_myorder_total_did($compid,$where_total5,$where_iszone_did);

        //欧冶订单
        $total_ouyeel = $this->_dao->getOne("select count(1) from sm_exc_dd_tag where 1 and sm_exc_dd_tag.Status=6 $where3 and sm_exc_dd_tag.Createdate>='2014-08-28 00:00:00'  order by sm_exc_dd_tag.Status ASC,sm_exc_dd_tag.Createdate desc");
        $this->assign( "total_ouyeel", $total_ouyeel );
        $this->assign( "sta", $sta );
        //我的订单列表的小图标===end==

        $total = $this->_dao->getOne("SELECT COUNT(sm_exc_dd_tag.ID) as c FROM  sm_exc_dd_tag $leftjoin $wherex $where2 $where3");
        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 10;
        $start = ($page - 1) * $per;
        $url = "bizorder.php";
        unset($params['page']);

        $zys[] = array();

        //Updated by quanjw for addComName start 2015/4/2
        $ddall = array();
        if($sta=="6" || $sta==""){
            $ddall=$this->_dao->query("select sm_exc_dd_tag.Status,OrderNo,ouyeel_OrderNo,ouyeel_orderStatus,ouyeel_orderUrlAddress,SlType,Did,Tweight,Tmoney,sm_exc_dd_tag.CreateDate as CreateDate,sm_exc_dd_tag.ID as ddid,$mid from sm_exc_dd_tag where 1 $where2 $where3 and sm_exc_dd_tag.Createdate>='2014-08-28 00:00:00'  order by sm_exc_dd_tag.Status ASC,sm_exc_dd_tag.Createdate desc LIMIT $start,$per");
        }else{
            // $ddall=$this->_dao->query("select sm_exc_dd_tag.Status,OrderNo,SlType,Did,ComName,Tweight,Tmoney,{$ht_tab}.paytype,fkxs,dwname,PayStatus,sm_exc_dd_tag.CreateDate as CreateDate,sm_exc_dd_tag.ID as ddid,$mid $mids from sm_exc_dd_tag  $leftjoin $wherex   $where2 $where3 and sm_exc_dd_tag.Createdate>='2014-08-28 00:00:00'  order by sm_exc_dd_tag.Status ASC,sm_exc_dd_tag.Createdate desc LIMIT $start,$per");
            $ddall=$this->_dao->query("select sm_exc_dd_tag.Status,OrderNo,SlType,Did,Tweight,Tmoney,{$ht_tab}.paytype,fkxs,dwname,PayStatus,sm_exc_dd_tag.CreateDate as CreateDate,sm_exc_dd_tag.ID as ddid,$mid $mids from sm_exc_dd_tag  $leftjoin $wherex   $where2 $where3 and sm_exc_dd_tag.Createdate>='2014-08-28 00:00:00'  order by sm_exc_dd_tag.Status ASC,sm_exc_dd_tag.Createdate desc LIMIT $start,$per");
        }
	    
	    //Updated by hzp  ended 2015/4/16
	    //Updated by quanjw for addComName end 2015/4/2
//        echo "<pre/>";print_r($ddall);
	    foreach($ddall as $key=>&$tmp){
	        
	        $tmp['ComName'] = $this->_dao->get_ComName($tmp['mmid']);
            //订单明细列表
            $zys[$key] = $this->_dao->get_dd_detail_list($tmp['ddid']);

            //Added for huadong gangshi  by hzp start  2014/12/25
            foreach ($zys[$key] as &$tmp1) {
                if ($tmp1['Sid'] < 0) {
                    $tmp1['Sids'] = $tmp1['Sid'] * (-1);
                }
            }
            //Added for huadong gangshi  by hzp end  2014/12/25

            $tmp['htstatu'] = $tmp['Status2'];
            //付款形式大类 现款，承兑
            $tmp['fkxss'] = $this->get_fkxss($tmp['fkxs']);

            //判断是否是融资 add by xiakang started 2015/12/30
            $list = $this->_dao->getRow("select * from sm_contract_transaction where BID='" . $tmp['ddid'] . "' ");
            if ($list['Status'] == "14") {
                $tmp['ApprovalMode'] = "2"; //尾款支付
            } else if ($list['Status'] == "5" || $list['Status'] == "17") {
                $tmp['ApprovalMode'] = "1"; //预付款支付
            }
            $pingan = $this->_dao->getRow("select * from sm_pinganrongzi_contract_transcation where dzcontract_id = '" . $list['ID'] . "' and ApprovalMode='" . $tmp['ApprovalMode'] . "' and (ApprovalStatus1!=1 and ApprovalStatus2!=1 and ApprovalStatus2!=90) ");
            $pashusers = $this->_dao->getOnes("select userid from sys_pinganrongzi_shenpi_liucheng where Mid='" . $pingan['Mid_Consignee'] . "' ");

            if ($pingan['ApprovalStatus1'] == "-1" && ($list['Status'] == "17" || $list['Status'] == "5")) {
                $tmp['pastatus'] = "1"; //申请完成预付款
            } else if ($pingan['ApprovalStatus1'] == "-1" && $list['Status'] == "14") {
                $tmp['pastatus'] = "2"; //申请完成尾款
            }
            /*else if(($pingan['ApprovalStatus1']!="-1") && (in_array( $_SESSION['SYS_USERID'],$pashusers) || $pingan['UserID1']==$_SESSION['SYS_USERID'])){
             $tmp['pastatus']="2"; //第一次审核完成，第二次融资审核通过，要去支付。
            }*/

	    }
    
        //去除无效订单
        foreach($zys as $zys_key=>$zys_item){
            if(empty($zys_item)){
                unset($zys[$zys_key]);
                unset($ddall[$zys_key]);
                if($total >0)
                    $total--;
                if($total1 >0)
                    $total1--;
                if($total6 >0)
                    $total6--;
            }
        }
        array_values($zys);
        array_values($ddall);
	    //add by xiakang for pingan started 2016/01/12
        //获取我接受的新订单的数量
        $newOrderNum = 0;
        if ( $_SESSION['SYS_COMPANYID'] > 0 ) {
            $newOrderNum = $this->_dao->getOne("select count(1) from sm_exc_dd_tag  where Did = '".$_SESSION['SYS_COMPANYID']."' and read_flag =  0 ");
        }
        $this->assign("newOrderNum", $newOrderNum);
        $this->assign( "total1", $total1 );
        $this->assign( "total2", $total2 );
        $this->assign( "total3", $total3 );
        $this->assign( "total4", $total4 );
        $this->assign( "total5", $total5 );
        $this->assign( "total6", $total6 );
        $this->assign( "total7", $total7 );
        $this->assign( "total8", $total8 );
        $this->assign( "total9", $total9 );
        $this->assign( "total10", $total10 );

        $pagebar = $this->pagebar($url, $params, $per, $page, $total);
        $this->assign("pagebar", $pagebar);
        $this->assign("zys", $zys);
        $this->assign("ddall", $ddall);
        $this->assign("page_title", "订单列表_会员中心_");

        $this->assign("type", $type);
        $this->assign("params", $params);
        //取得该会员主联系人
        $masteruser = $this->_dao->get_sys_company($_SESSION['SYS_COMPANYID']);
        $comp2 = $this->_dao->get_sys_adminuser($_SESSION['SYS_USERID']);

        $this->assign("comp", $masteruser);
        $this->assign("comp2", $comp2);

        //权限字段
        $this->assign("userrole", $_SESSION['SYS_USERROLE']);
        $this->assign("ddtype", $GLOBALS['DDTYPE']);
        $this->assign("htstatus", $GLOBALS['HT_STATUS']);
        $this->assign("htpaystaus", $GLOBALS['HTPAYSTATUS']);

        $this->setvars();
    }

    //ADD BY XIAKANG FOR YUNQIAN STARTED 2015/05/21
    public function yqorder($params)
    {
        $sta = $params['sta'];
        $compid = $_SESSION['SYS_COMPANYID'];

        $userid = $_SESSION['SYS_USERID'];
        $this->assign("shipper_sign_status", $GLOBALS["SHIPPER_SIGN_STATUS"]);
        $this->assign("consignee_sign_status", $GLOBALS["CONSIGNEE_SIGN_STATUS"]);

        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 10;
        $start = ($page - 1) * $per;
        $url = "bizorder.php";
        unset($params['page']);

        $zys[] = array();

        $orderID = "";
        $order_sql = "";
        if (isset($params['orderID']) && $params['orderID']) {
            $orderID = $params['orderID'];
            $order_sql = " and sedt.OrderNo like '%$orderID%' ";
        }

        //added by qunajw for company search start 
        if ($params['comName'] != null) {
            $ComName = $params['comName'];
            if ($sta == "1" || $sta == null) {
			$wheresc1 .=" and ( sc.ComName like '%".$ComName."%' OR sc2.ComName like '%".$ComName."%')";
		} elseif($sta=="3") {
			$wheresc3 .=" and ( sc.ComName like '%".$ComName."%' OR sc2.ComName like '%".$ComName."%')";
		} elseif($sta=="2") {
			$wheresc2 .=" and ( sys_company.ComName like '%".$ComName."%' OR sc2.ComName like '%".$ComName."%')";
		} elseif($sta=="4") {
			$wheresc4 .=" and ( sc.ComName like '%".$ComName."%' OR sc2.ComName like '%".$ComName."%')";
		}
        }
        //added by qunajw for company search end

        // updated by quanjw start
        if ($sta == "1" || $sta == null) {
            $ddall1 = $this->_dao->query("select *,sys_yq_shenpi_status.mid m_id,sc.ComName as comname,sc2.ComName as comname2,sm_contract_transaction.CreateDate as CreateDate,sm_exc_dd_tag.Status,sm_exc_dd_tag.ID as ddid,sm_contract_transaction.Status as Status2, sm_contract_transaction.PayType from sm_exc_dd_tag,sm_contract_transaction, sys_yq_shenpi_status, sys_company, sys_company sc, sys_company sc2 where sys_yq_shenpi_status.dzcontract_id=sm_contract_transaction.ID and sc.ID=sm_contract_transaction.Mid_Consignee and sc2.ID=sm_contract_transaction.Mid_Shipper and sys_company.ID=sys_yq_shenpi_status.Umid and sm_contract_transaction.BID=sm_exc_dd_tag.ID and sys_yq_shenpi_status.userid='" . $userid . "' and sys_yq_shenpi_status.result=0 and sys_yq_shenpi_status.shenpi_status!='-1' and sm_exc_dd_tag.OrderNo like '%" . $orderID . "%' and sm_contract_transaction.status not in (21,22) $wheresc1 order by sm_contract_transaction.CreateDate  desc LIMIT $start,$per");
        }
        $total1 = $this->_dao->getOne("select count(*) from sm_exc_dd_tag,sm_contract_transaction, sys_yq_shenpi_status, sys_company ,sys_company sc , sys_company sc2 where sys_yq_shenpi_status.dzcontract_id=sm_contract_transaction.ID and sys_company.ID=sys_yq_shenpi_status.Umid and sm_contract_transaction.BID=sm_exc_dd_tag.ID and sys_yq_shenpi_status.userid='" . $userid . "' and sys_yq_shenpi_status.result=0 and sys_yq_shenpi_status.shenpi_status!='-1' and sm_exc_dd_tag.OrderNo like '%" . $orderID . "%' and sm_contract_transaction.status not in (21,22) and sc.ID=sm_contract_transaction.Mid_Consignee and sc2.ID=sm_contract_transaction.Mid_Shipper $wheresc1");
        // updated by quanjw end

        // updated by quanjw start
        if ($sta == "2") {
            $ddall2 = $this->_dao->query("select *,sys_yq_qianshu_status.mid m_id,sys_company.ComName as comname,sc2.ComName as comname2,sm_contract_transaction.CreateDate as CreateDate,sm_exc_dd_tag.Status,sm_exc_dd_tag.ID as ddid,sm_contract_transaction.Status as Status2, sm_contract_transaction.PayType from sm_exc_dd_tag,sm_contract_transaction, sys_yq_qianshu_status, sys_company, sys_company sc2 where sys_yq_qianshu_status.dzcontract_id=sm_contract_transaction.ID and sys_company.ID=sm_contract_transaction.Mid_Consignee and sc2.ID=sm_contract_transaction.Mid_Shipper and sm_contract_transaction.BID=sm_exc_dd_tag.ID and sys_yq_qianshu_status.userid='" . $userid . "' and sys_yq_qianshu_status.result=0 and sys_yq_qianshu_status.status=0 and sm_exc_dd_tag.OrderNo like '%" . $orderID . "%' and sm_contract_transaction.status not in (21,22) $wheresc2  order by sm_contract_transaction.CreateDate  desc LIMIT $start,$per");
        }
        $total2 = $this->_dao->getOne("select count(*) from sm_exc_dd_tag,sm_contract_transaction, sys_yq_qianshu_status,sys_company,sys_company sc2 where sys_yq_qianshu_status.dzcontract_id=sm_contract_transaction.ID and sm_contract_transaction.BID=sm_exc_dd_tag.ID and sys_yq_qianshu_status.userid='" . $userid . "' and sys_yq_qianshu_status.result=0 and sys_yq_qianshu_status.status=0 and sm_exc_dd_tag.OrderNo like '%" . $orderID . "%' and sm_contract_transaction.status not in (21,22) and sys_company.ID=sm_contract_transaction.Mid_Consignee and sc2.ID=sm_contract_transaction.Mid_Shipper $wheresc2");
        // updated by quanjw end

        foreach ($GLOBALS['canViewYunQianUser'] as $key => $val) {
            if (in_array($userid, $val)) {
                $viewpower = $key;
            }
        }

        $userinfo = $this->_dao->getRow("select IsMain,childroot from sys_adminuser where id=$userid");
        $ddall3_fields = "syctp.mid m_id,syctp.dzcontract_id as dzcontract_id,sct.Mid_Shipper,sct.Mid_Consignee,syctp.Mid mid,sedt.Tmoney as Tmoney,sedt.Tweight as Tweight,sc.ComName as comname,sc2.ComName as comname2,sct.CreateDate as CreateDate,sedt.Status,sedt.ID as ddid,sct.Status as Status2,sedt.OrderNo,PayType ";
        $ddall3_tables = " sm_yq_contract_transcation_partion syctp, sm_contract_transaction sct, sm_exc_dd_tag sedt,sys_company sc,sys_company sc2";
        $ddall3_sql = "select $ddall3_fields from $ddall3_tables ";
        $total3_sql = "select count(*) from $ddall3_tables ";
        $where_3 = "syctp.dzcontract_id = sct.ID and sct.BID = sedt.ID and sc.ID=sct.Mid_Consignee and sc2.ID=sct.Mid_Shipper and sct.status not in (21,22) ";

        if ($viewpower) {
            $mids = $this->_dao->getOnes("select ID from sys_company where GroupID='$viewpower' or ID='$compid'");
            $midstr = implode(",",$mids);
            $where_3 .= " and (syctp.mid in ($midstr) or syctp.userid='$userid' or syctp.Confirm_userid='$userid' or syctp.Last_shenpi_userid like '%[$userid]%')";
        } else if (($userinfo['IsMain'] == 1) || (strpos($userinfo['childroot'], "3") !== FALSE)) {
            $where_3 .= " and (syctp.mid='$compid' or syctp.userid='$userid' or syctp.Confirm_userid='$userid' or syctp.Last_shenpi_userid like '%[$userid]%')";
        } else {

            $where_3 .= " and (syctp.userid='$userid' or syctp.Confirm_userid='$userid' or syctp.Last_shenpi_userid like '%[$userid]%')";
        }
        $ddall3_sql .= " where $where_3 $order_sql $wheresc3 order by sct.CreateDate desc LIMIT $start,$per";
        $total3_sql .= " where $where_3 $order_sql $wheresc3 ";

        if ($sta == "3") {
            $ddall3 = $this->_dao->query($ddall3_sql);
        }
        $total3 = $this->_dao->getOne($total3_sql);

        // updated by quanjw start		
        if ($sta == "4") {
            $ddall4 = $this->_dao->query("select distinct(syct.ID) as id2,syss.mid m_id,syct.dzcontract_id as dzcontract_id,syct.Mid_Shipper,syct.Mid_Consignee,syss.Mid mid,sedt.Tmoney as Tmoney,sedt.Tweight as Tweight,sc.ComName as comname,sc2.ComName as comname2,sct.CreateDate as CreateDate,sedt.Status,sedt.ID as ddid,sct.Status as Status2,sedt.OrderNo,sm_contract_transaction.PayType from sm_yq_contract_transcation syct, sys_yq_shenpi_status syss, sm_contract_transaction sct, sm_exc_dd_tag sedt,sys_company sc,sys_company sc2
			where sc.ID=sct.Mid_Consignee and sc2.ID=sct.Mid_Shipper and syct.dzcontract_id = sct.ID and sct.BID = sedt.ID and syss.shenpi_status = -1 and
			syct.dzcontract_id = syss.dzcontract_id and case when (select IsMain from sys_adminuser sa where sa.ID='" . $userid . "')=1 then syss.Umid=(select ComID from sys_adminuser sa2 where sa2.ID='" . $userid . "') else syss.userid = '" . $userid . "' end and sct.status not in (21,22) and sedt.OrderNo like '%" . $orderID . "%' $wheresc4 order by CreateDate desc LIMIT $start,$per");
        }
        $total4 = $this->_dao->getOne("select count(distinct(syct.ID)) from sm_yq_contract_transcation syct, sys_yq_shenpi_status syss, sm_contract_transaction sct, sm_exc_dd_tag sedt,sys_company sc ,sys_company sc2
			where syct.dzcontract_id = sct.ID and sct.BID = sedt.ID and syss.shenpi_status = -1 and
			syct.dzcontract_id = syss.dzcontract_id and case when (select IsMain from sys_adminuser sa where sa.ID='" . $userid . "')=1 then syss.Umid=(select ComID from sys_adminuser sa2 where sa2.ID='" . $userid . "') else syss.userid = '" . $userid . "' end and sct.status not in (21,22) and sedt.OrderNo like '%" . $orderID . "%' and sc.ID=sct.Mid_Consignee and sc2.ID=sct.Mid_Shipper $wheresc4 ");
        // updated by quanjw start		

        //add by xiakang for yunqian started 2015/07/23

        if ($sta == "1" || $sta == null) {
            $ddall = $ddall1;
        } elseif ($sta == "3") {
            $ddall = $ddall3;
        } elseif ($sta == "2") {
            $ddall = $ddall2;
        } elseif ($sta == "4") {
            $ddall = $ddall4;
        }

        foreach ($ddall as $key => &$tmp) {

            $money = $tmp['TotalMoney'];
            $userlevel = $this->_dao->getOnes("SELECT level from sys_yq_shenpi_liucheng where userid='" . $userid . "' ");

            // $result = yqcontract::getShenpiPriceRange($tmp['dzcontract_id'], $userid);

            if (in_array($compid, $GLOBALS['JITUANID'])) {
                $selectmid = $this->_dao->getOne("select Mid FROM sys_yq_shenpi_status where dzcontract_id='" . $tmp['dzcontract_id'] . "'  and userid='" . $userid . "' and Mid = '" . $tmp['mid'] . "' ");
            } else {
                $selectmid = $compid;
            }

            $tmp['resultlc'] = $this->_dao->getOne("select result as resultlc from sys_yq_shenpi_status where userid='" . $userid . "' and Mid='" . $selectmid . "' ");

            $tmp['ComName'] = $this->_dao->get_ComName($selectmid);

            //added by tuxw started 20151125
            if ($tmp['Mid_Shipper'] == $tmp['m_id']) {
                $tmp['shipper_sign_status'] = $this->_dao->getOne("select shipper_sign_status from sm_yq_contract_transcation where dzcontract_id='" . $tmp['dzcontract_id'] . "'");
            }
            if ($tmp['Mid_Consignee'] == $tmp['m_id']) {
                $tmp['consignee_sign_status'] = $this->_dao->getOne("select consignee_sign_status from sm_yq_contract_transcation where dzcontract_id='" . $tmp['dzcontract_id'] . "'");
            }
            //added by tuxw ended 20151125

            //订单明细列表
            $zys[$key] = $this->_dao->get_dd_detail_list($tmp['ddid']);

            foreach ($zys[$key] as &$tmp1) {
                if ($tmp1['Sid'] < 0) {
                    $tmp1['Sids'] = $tmp1['Sid'] * (-1);
                }
            }
            $ht_data = $this->_dao->get_sm_contract_transaction($tmp['ddid']);

            $tmp['htstatu'] = $ht_data['Status'];
            $tmp['PayStatus'] = $ht_data['PayStatus'];
            $tmp['fkxs'] = $ht_data['fkxs'];

            //付款形式大类 现款，承兑
            $tmp['fkxss'] = $this->get_fkxss($tmp['fkxs']);
        }

        $this->assign("total1", $total1);
        $this->assign("total2", $total2);
        $this->assign("total3", $total3);
        $this->assign("total4", $total4);
        //分页判断-----------------------started
        if ($sta == "1" || $sta == "") {
            $pagebar = $this->pagebar($url, $params, $per, $page, $total1);
        }
        if ($sta == "2") {
            $pagebar = $this->pagebar($url, $params, $per, $page, $total2);
        }
        if ($sta == "3") {
            $pagebar = $this->pagebar($url, $params, $per, $page, $total3);
        }
        if ($sta == "4") {
            $pagebar = $this->pagebar($url, $params, $per, $page, $total4);
        }

        //分页判断-----------------------ended
        $this->assign("page", $page);
        $this->assign("pagebar", $pagebar);
        $this->assign("zys", $zys);
        $this->assign("ddall", $ddall);
        $this->assign("page_title", "订单列表_会员中心_");
        $this->assign("params", $params);

        //取得该会员主联系人
        $masteruser = $this->_dao->get_sys_company($_SESSION['SYS_COMPANYID']);
        $comp2 = $this->_dao->get_sys_adminuser($_SESSION['SYS_USERID']);

        $this->assign("comp", $masteruser);
        $this->assign("comp2", $comp2);

        //权限字段
        $this->assign("userrole", $_SESSION['SYS_USERROLE']);
        $this->assign("ddtype", $GLOBALS['DDTYPE']);
        $this->assign("htstatus", $GLOBALS['HT_STATUS']);
        $this->assign("htpaystaus", $GLOBALS['HTPAYSTATUS']);

        $this->setvars();
    }
    //ADD BY XIAKANG FOR YUNQIAN ENDED 2015/05/21

    //ADD BY XIAKANG FOR YUNQIAN ENDED 2015/06/10
    public function doyqorder($params)
    {
        $ucid = $_SESSION['SYS_USERID'];
        $mid = $_SESSION['SYS_COMPANYID'];
        $info = $this->_dao->getRow("select * from sys_yq_shenpi_liucheng where userid='" . $ucid . "'");
        if ($info['ID'] != "") {
            $contract = $this->_dao->getRow("select * from sm_contract_transaction where BID='" . $params['id'] . "' ");
            $contractID = $contract['ID'];
            $this->_dao->execute("insert into sys_yq_qianshu_status set userid='" . $info['userid'] . "',result=0,Mid='" . $info . "',dzcontract_id='" . $contractID . "' ");
            $flow = $this->_dao->getOnes("select userid from sys_yq_shenpi_liucheng where Mid='" . $_SESSION['SYS_COMPANYID'] . "' ORDER BY NO ASC ");
            $flow = implode(",", $flow);

            $Callback1 = WEB_SITE . "/bizorder.php?action=appcontract_a";
            $Callback2 = WEB_SITE . "/bizorder.php?action=appcontract_a";

            // $result = yqcontract::approvalContract($ucid, $contractID, $mid, $flow, $Callback1, $Callback2);
            $result = array('status' => '1');
            if ($result['status'] == "0") {
                alert("成功");
                goback();
            } else {
                alert("失败");
                goback();
            }
        } else {

            alert("您没有被设定权限！");
            goback();
        }
    }
    //ADD BY XIAKANG FOR YUNQIAN ENDED 2015/06/10


    public function appcontract_a($params)
    {
        //file_put_contents("aa.txt","123");
        $file = dirname(__FILE__);
        ob_start();

        print_r(json_decode(urldecode($params['info']), true));
        $contents = ob_get_contents();
        ob_end_clean();
        $fp = fopen("/tmp/1234567.txt", "w");
        fprintf($fp, $contents); //将数据写入文件
        fprintf($fp, "ccc\n");
        //fprintf($fp,APPID."&".FROMCUSTOM."&".$info."&".$time."&".YUNQIANKEY);//将数据写入文件
        fclose($fp); //关闭文件*/
        //ob_start();
        //alert("1111");
        $info2 = $params['info'];
        //$info = {"dzcontract_id":"001","userid":"121","result":1,"ucid":"00121","status":"success"};
        /*var_dump($info);
	echo "<pre>";
	echo "11111";*/
        //$info = json_decode(html_entity_decode(urldecode($info)), true);
        //$arr = json_decode(html_entity_decode(urldecode($info2)), true);
        $arr = json_decode(urldecode($params['info']), true);
        foreach ($arr as $info) {
            //$info = json_decode($params['info'],true);
            //$info = get_object_vars($info);
            //echo $info1[0];
            /*print_r($info);
	echo "<pre>";
	var_dump($info);*/
            /*$file=dirname(__FILE__); 

	$fp=fopen($file."/appcontract_a.txt","w");
	fwrite($fp,$info);//将数据写入文件
	fclose($fp);//关闭文件*/
            $file = dirname(__FILE__);
            ob_start();
            print_R($info);

            $contents = ob_get_contents();

            ob_end_clean();

            //echo "contents = $contents<br/>";

            $fp = fopen("/tmp/appcontract_a.txt", "a+");
            fprintf($fp, $contents); //将数据写入文件
            fprintf($fp, "ccc2\n"); //将数据写入文件
            fclose($fp); //关闭文件*/

            $dzcontract_id = substr($info['orderid'], 2);
            //$ucid = substr($info['ucid'],2);
            $ucid = substr($info['uid'], 2);
            $mid = substr($info['mid'], 2);
            //$mid = $info['mid'];
            $shenpino = $info['process'] - 1;
            $approvalCode = $info['approvalStatus'];
            //$status = $info['approvalInfo'];
            $status =  $info['remark'];
            $syncOrderId = $info['syncOrderId'];
            //added by hezp started 2015/12/11
            $approvalTime = $info['approvalTime'];
            //added by hezp ended 2015/12/11

            //add by xiakang for yunqian started 2016/02/05
            $sphtback = $this->_dao->getOne("select result from sys_yq_shenpi_status 
	where dzcontract_id = '" . $dzcontract_id . "' 
	and userid='" . $ucid . "' and mid='" . $mid . "' and No='" . $shenpino . "'");
            //added by hezp started 2016/07/14
            if ($sphtback != '0' && $sphtback != '1') {
                continue;
            }
            //added by hezp ended 2016/07/14
            if ($sphtback == "1") {
                if ($dzcontract_id &&  $ucid && $mid && $shenpino >= '0')
                    $data[$syncOrderId] = '1';
                continue;
            }
            //add by xiakang for yunqian ended 2016/02/05
            $this->_dao->execute("update sys_yq_shenpi_status SET result = '1',status='" . $status . "',approvalCode='" . $approvalCode . "',approvalTime='" . $approvalTime . "' where dzcontract_id = '" . $dzcontract_id . "' and userid='" . $ucid . "' and mid='" . $mid . "' and No='" . $shenpino . "' ");
            //add by xiakang for yunqian started 2015/11/10

            $shenpiusers = $this->_dao->getOnes("select userid FROM sys_yq_shenpi_status where dzcontract_id='" . $dzcontract_id . "' and result=0 and No='" . $shenpino . "' and mid='" . $mid . "' ");
            foreach ($shenpiusers as $tmp) {
                $this->_dao->execute("update sys_yq_shenpi_status SET result = '-1',status='" . $status . "',approvalCode='" . $approvalCode . "' where dzcontract_id = '" . $dzcontract_id . "' and userid='" . $tmp . "' and mid='" . $mid . "' and No='" . $shenpino . "' ");

                $file = dirname(__FILE__);
                ob_start();
                $contents = ob_get_contents();
                ob_end_clean();
                $fp = fopen("/tmp/appcontract_a1111.txt", "w");
                fprintf($fp, "update sys_yq_shenpi_status SET result = '-1',status='" . $status . "',approvalCode='" . $approvalCode . "' where dzcontract_id = '" . $dzcontract_id . "' and userid='" . $tmp . "' and mid='" . $mid . "' and No='" . $shenpino . "' "); //将数据写入文件
                fprintf($fp, "aaaaa\n"); //将数据写入文件
                fclose($fp); //关闭文件*/
            }
            //add by xiakang for yunqian ended 2015/11/10

            //Add by xiakang for yunqian started 2015/07/23
            $this->_dao->execute("Update sm_contract_transaction SET Status= '16' where ID='" . $dzcontract_id . "' ");
            //Add by xiakang for yunqian ended 2015/07/23

            $file = dirname(__FILE__);
            ob_start();

            $fp = fopen("/tmp/appcontract_a1.txt", "a+");
            fprintf($fp, "ccc\n"); //将数据写入文件
            fprintf($fp, date("Y-m-d h:i:s")); //将数据写入文件
            fprintf($fp, "ccc\n"); //将数据写入文件
            fprintf($fp, "update sys_yq_shenpi_status SET result = '1',status='" . $status . "',approvalCode='" . $approvalCode . "' where dzcontract_id = '" . $dzcontract_id . "' and userid='" . $ucid . "' and mid='" . $mid . "' and No='" . $shenpino . "' "); //将数据写入文件

            fprintf($fp, "ccc\n"); //将数据写入文件
            fclose($fp); //关闭文件*/

            $selectmid = $mid;


            //审批流程类型判断-------------started
            // $resultlc = yqcontract::getShenpiType($dzcontract_id, $selectmid);
            //print_r($resultlc['type']);
            //exit;
            //审批流程类型判断-------------ended 
            $contractlc = $this->_dao->getRow("select Mid_Shipper,Mid_Consignee,shipper_shenpi_level_no,consignee_shenpi_level_no,shipper_shenpi_level,consignee_shenpi_level from sm_yq_contract_transcation where dzcontract_id='" . $dzcontract_id . "' ");
            $xuanzelc = "";
            $level = "";
            if ($mid == $contractlc['Mid_Shipper']) {
                $xuanzelc = $contractlc['shipper_shenpi_level_no'];
                $level = $contractlc['shipper_shenpi_level'];
            }
            if ($mid == $contractlc['Mid_Consignee']) {
                $xuanzelc = $contractlc['consignee_shenpi_level_no'];
                $level = $contractlc['consignee_shenpi_level'];
            }
            $test1 = $this->_dao->getOne("select Count(No) from sys_yq_shenpi_liucheng where  Mid='" . $selectmid . "' and level='" . $level . "' and level_no='" . $xuanzelc . "' group by No");

            $test2 = $this->_dao->getOne("select Count(No) from sys_yq_shenpi_status where  Mid='" . $selectmid . "' and dzcontract_id='" . $dzcontract_id . "' and result !=0 group by No");

            if ($contractlc['Mid_Shipper'] == $selectmid && $test1 == $test2) {
                $this->_dao->execute("update sm_yq_contract_transcation SET shipper_sign_status=1 where dzcontract_id='" . $dzcontract_id . "' ");
            }
            if ($contractlc['Mid_Consignee'] == $selectmid && $test1 == $test2) {
                $this->_dao->execute("update sm_yq_contract_transcation set consignee_sign_status=1 where dzcontract_id='" . $dzcontract_id . "' ");
            }

            //add by tuxw for yunqian started 2015/07/01
            if ($approvalCode != 1) {
                //双方合同都不通过
                //$this->_dao->execute("update sys_yq_shenpi_status set shenpi_status=-1 where dzcontract_id='".$dzcontract_id."' and Mid='".$mid."'");
                $this->_dao->execute("update sys_yq_shenpi_status set shenpi_status=-1 where dzcontract_id='" . $dzcontract_id . "' ");
                //added by hezp started 2017/02/22
                //撤销合同
                $yqhtuser = $this->_dao->getRow("select Mid_Shipper,Mid_Consignee,shipper_userid,consignee_userid from sm_yq_contract_transcation where dzcontract_id='" . $dzcontract_id . "'");
                if ($yqhtuser['Mid_Shipper'] == $mid) {
                    $rejectid = $yqhtuser['shipper_userid'];
                } else {
                    $rejectid = $yqhtuser['consignee_userid'];
                }
                file_get_contents(WEB_SITE . "/bizorder.php?action=doqxyqdd2&rejectid=" . $rejectid . "&dzcontract_id=" . $dzcontract_id);
                //yqcontract::rejectContract($rejectid,$dzcontract_id);
                //added by hezp ended 2017/02/22

                //added by hzp for yunqian duanxin started 2015/07/20
                //审批不通过，发给合同创建人
                //updated by hezp for yunqian started 2015/11/06
                $htuser = $this->_dao->getRow("select Mid_Shipper,Mid_Consignee,ConfirmBoUser,ConfirmSoUser from sm_contract_transaction where ID='" . $dzcontract_id . "'");
                if ($htuser['Mid_Shipper'] == $mid) {
                    $htcreateuser = $htuser['ConfirmSoUser'];
                } else {
                    $htcreateuser = $htuser['ConfirmBoUser'];
                }
                //updated by hezp for yunqian ended2015/11/06
                $BID = $this->_dao->getOne("select BID from sm_contract_transaction where ID='" . $dzcontract_id . "'");
                $ContractNo = $this->_dao->getOne("select OrderNo from sm_exc_dd_tag where ID='" . $BID . "'");
                $mobile = $this->_dao->getRow("select sys_adminuser.yq_telnumber as mobile,sys_company.ComName from sys_adminuser,sys_company where sys_adminuser.ComID = sys_company.ID and sys_adminuser.AUserName ='" . $htcreateuser . "'");
                $smscontent = "您的" . $ContractNo . "订单未通过云签审批，请按照审批意见重新洽谈并提交订单";
                $data = array('mobile' => $mobile['mobile'], 'smscontent' => $smscontent);

                $this->yqspsend($data);
                //added by hzp for yunqian duanxin ended 2015/07/20

            } elseif ($approvalCode == 1) {
                $No = $this->_dao->getOne("select max(No) from sys_yq_shenpi_status where dzcontract_id='" . $dzcontract_id . "' and Mid='" . $mid . "'");
                $level_sp = $this->_dao->getOne("select max(level) from sys_yq_shenpi_status where dzcontract_id='" . $dzcontract_id . "' and Mid='" . $mid . "'");

                $this->saveNextShenpiUser($dzcontract_id, $mid, $level_sp, $No);
            }
            //$data[] = array('orderid'=>$info['orderid'], 'mid'=>$info['mid'], 'ucid'=>$info['uid'], 'code'=>'1', $syncOrderId=>'1'); // 0:回调失败， 1：回调成功
            $data[$syncOrderId] = '1'; // 0:回调失败， 1：回调成功
        }

        $result = array("status" => "1", "data" => $data);

        $file = dirname(__FILE__);
        ob_start();

        // print_r($data);
        fprintf($fp, "ccc\n"); //将数据写入文件
        // print_r($result);
        $contents = ob_get_contents();

        ob_end_clean();

        //echo "contents = $contents<br/>";

        $fp = fopen("/tmp/appcontract_a1.txt", "a+");
        fprintf($fp, $contents); //将数据写入文件

        fprintf($fp, "AAAAA\n"); //将数据写入文件
        fclose($fp); //关闭文件*/


        echo json_encode($result);
        //add by tuxw for yunqian ended 2015/07/01	
    }

    public function qxyqdd($params)
    {

        // $arr = yqcontract::rejectContract($_SESSION['SYS_USERID'], $params['id']);
        // if ($arr['status'] == '2') {
        //     alert("撤销云签合同成功!");
        //     goback();
        // } else if ($arr['status'] == '1') {
        //     alert("用户注册云签失败!");
        //     goback();
        // } else if ($arr['status'] == '3') {
        //     alert("云签合同已撤销!");
        //     goback();
        // } else {
        //     alert("撤销云签合同失败!");
        //     goback();
        // }
        alert("云签合同停止使用了!");
            goback();
    }

    public function doqxyqdd($params)
    {

        $data = $this->_dao->query("SELECT distinct sys_yq_shenpi_status.`dzcontract_id`,sys_yq_shenpi_status.`Mid`,Mid_Shipper,Mid_Consignee,shipper_userid,consignee_userid FROM `sys_yq_shenpi_status` left join sm_yq_contract_transcation on sm_yq_contract_transcation.dzcontract_id=sys_yq_shenpi_status.dzcontract_id WHERE `shenpi_status`='-1' and sm_yq_contract_transcation.shipper_sign_status!='3'");

        foreach ($data as $tmp) {
            if ($tmp['Mid_Shipper'] == $tmp['Mid']) {
                $rejectid = $tmp['shipper_userid'];
            } else {
                $rejectid = $tmp['consignee_userid'];
            }

            if ($rejectid != '0') {
                file_get_contents(WEB_SITE . "/bizorder.php?action=doqxyqdd2&rejectid=" . $rejectid . "&dzcontract_id=" . $tmp['dzcontract_id']);
            }
        }
    }
    public function doqxyqdd2($params)
    {
        $rejectid = $params['rejectid'];
        $dzcontract_id = $params['dzcontract_id'];
        // $status = yqcontract::rejectContract($rejectid, $dzcontract_id);
    }
    //ADD BY XIAKANG FOR YUNQIAN ENDED 2015/06/10
    public function signcontract_yqsend($userid, $BID, $type)
    {

        $ContractNo = $this->_dao->getOne("select OrderNo from sm_exc_dd_tag where ID='" . $BID . "'");
        $mobile = $this->_dao->getRow("select sys_adminuser.Mobile as mobile,sys_company.ComName from sys_adminuser,sys_company where sys_adminuser.ComID = sys_company.ID and sys_adminuser.AUserName ='" . $userid . "'");
        $smscontent = "您的订单" . $ContractNo . "已通过云签签署！";
        $data = array('mobile' => $mobile['mobile'], 'smscontent' => $smscontent);
        $this->yqspsend($data);
    }
    //ADD BY XIAKANG FOR YUNQIAN ENDED 2015/06/10
    public function signcontract_b($params)
    {

        $infos = $params['info'];
        $infos = json_decode(urldecode($infos), true);

        foreach ($infos as $info) {

            ob_start();
            print_R($info);
            $contents = ob_get_contents();
            ob_end_clean();
            $fp = fopen("/tmp/signcontract_b.txt", "a+");
            fprintf($fp, $contents); //将数据写入文件
            fprintf($fp, "ccc2\n"); //将数据写入文件
            fclose($fp); //关闭文件*/

            $orderid = substr($info['orderid'], 2);
            $signlog = json_decode($info['signlog'], true);
            //$updatetime = date('Y-m-d H:i:s',$info['updatetime']);
            $updatetime = $info['updatetime'];
            $status = $info['status'];
            $syncOrderId = $info['syncOrderId'];
            $contract = $this->_dao->getRow("select * from sm_contract_transaction where ID='" . $orderid . "' ");

            foreach ($signlog as $signer => $key) {

                $ucid = substr($signer, 2);
                $sign_time = $key;
                $Mid = $this->_dao->getOne("select Mid from sys_yq_qianshu_status where  dzcontract_id='" . $orderid . "' and userid='" . $ucid . "' ");

                if ($status == '0' || $status == '1' || $status == '2') {

                    if ($contract['Mid_Shipper'] == $Mid) {
                        //added by hzp for yunqian duanxin started 2016/10/21
                        //签署完成，发给合同创建人
                        $shipper_sign_status = $this->_dao->getOne("select shipper_sign_status from sm_yq_contract_transcation where  dzcontract_id='" . $orderid . "' ");
                        if ($shipper_sign_status == '1') {
                            $htuser = $this->_dao->getRow("select ConfirmSoUser,BID from sm_contract_transaction where ID='" . $orderid . "'");
                            $this->signcontract_yqsend($htuser['ConfirmSoUser'], $htuser['BID'], 1);
                        }
                        //added by hzp for yunqian duanxin ended 2016/10/21



                        $this->_dao->execute("Update sys_yq_qianshu_status SET result=1 where Mid='" . $contract['Mid_Shipper'] . "' and dzcontract_id='" . $orderid . "' and userid='" . $ucid . "' ");

                        //add by xiakang for yunqian started 2015/07/23
                        //$this->_dao->execute("update sm_contract_transaction set status=17 where ID='".$orderid."'");
                        $qyqsstatus = $this->_dao->getRow("select shipper_sign_status,consignee_sign_status from  sm_yq_contract_transcation where dzcontract_id='" . $orderid . "'");
                        if ($qyqsstatus['consignee_sign_status'] == "2" && $qyqsstatus['shipper_sign_status'] != '2') {
                            $this->_dao->execute("update sm_contract_transaction set status=17 where ID='" . $orderid . "'");
                        } else if ($qyqsstatus['consignee_sign_status'] == "1") {
                            $this->_dao->execute("update sm_contract_transaction set status=16 where ID='" . $orderid . "'");
                        }
                        //add by xiakang for yunqian ended 2015/07/23
                        $this->_dao->execute("Update sm_yq_contract_transcation SET shipper_sign_status = '2',shipper_sign_time='" . $updatetime . "' where dzcontract_id = '" . $orderid . "' and Mid_Shipper='" . $Mid . "' ");
                    } else if ($contract['Mid_Consignee'] == $Mid) {
                        //added by hzp for yunqian duanxin started 2016/10/21
                        //签署完成，发给合同创建人
                        $consignee_sign_status = $this->_dao->getOne("select consignee_sign_status from sm_yq_contract_transcation where  dzcontract_id='" . $orderid . "' ");
                        if ($consignee_sign_status == '1') {
                            $htuser = $this->_dao->getRow("select ConfirmBoUser,BID from sm_contract_transaction where ID='" . $orderid . "'");
                            $this->signcontract_yqsend($htuser['ConfirmBoUser'], $htuser['BID'], 2);
                        }
                        //added by hzp for yunqian duanxin ended 2016/10/21


                        $this->_dao->execute("Update sys_yq_qianshu_status SET result=1 where Mid='" . $contract['Mid_Consignee'] . "' and dzcontract_id='" . $orderid . "' and userid='" . $ucid . "' ");

                        //add by xiakang for yunqian started 2015/07/23
                        //$this->_dao->execute("update sm_contract_transaction set status=17 where ID='".$orderid."'");
                        $qyqsstatus = $this->_dao->getRow("select shipper_sign_status,consignee_sign_status from  sm_yq_contract_transcation where dzcontract_id='" . $orderid . "'");
                        if ($qyqsstatus['shipper_sign_status'] == "1") {
                            $this->_dao->execute("update sm_contract_transaction set status=16 where ID='" . $orderid . "'");
                        } else if ($qyqsstatus['shipper_sign_status'] == "2" && $qyqsstatus['consignee_sign_status'] != '2') {
                            $this->_dao->execute("update sm_contract_transaction set status=17 where ID='" . $orderid . "'");
                        }
                        //add by xiakang for yunqian ended 2015/07/23

                        $this->_dao->execute("Update sm_yq_contract_transcation SET consignee_sign_status = '2',consignee_sign_time='" . $updatetime . "' where dzcontract_id = '" . $orderid . "' and  Mid_Consignee='" . $Mid . "' ");
                    }
                } else if ($status == '3' || $status == '4' || $status == '5') {
                    //合同关闭的情况,双方设状态
                    $nstatus = -1;
                    switch ($status) {
                        case '3':
                            $nstatus = '4';
                            break;
                        case '4':
                            $nstatus = '3';
                            break;
                        case '5':
                            $nstatus = '5';
                            break;
                    }
                    $this->_dao->execute("Update sm_yq_contract_transcation SET consignee_sign_status = '" . $nstatus . "',consignee_sign_time='" . $sign_time . "' where dzcontract_id = '" . $orderid . "' and  Mid_Consignee='" . $Mid . "' ");
                    $this->_dao->execute("Update sm_yq_contract_transcation SET shipper_sign_status = '" . $nstatus . "',shipper_sign_time='" . $sign_time . " where dzcontract_id = '" . $orderid . "' and Mid_Shipper='" . $Mid . "' ");
                }
            }

            $data[$syncOrderId] = '1';
        }

        $result = array("status" => "1", "data" => $data);
        echo json_encode($result);
    }
    //ADD BY XIAKANG FOR YUNQIAN ENDED 2015/06/10

    //ADD BY hzp FOR YUNQIAN duanxin started 2015/07/20
    //云签审批短信
    public function yqspsend($params)
    {

        $mobile = $params['mobile'];
        $smscontent = $params['smscontent'];
        $checkcode = '';

        if ($mobile == "") {
            return "8";
        }

        $retData = $this->SendSms($mobile, $smscontent);
        if ($retData["resp_code"] == "000000000000") {
            return "9"; //成功
        } else {
            return $retData["resp_msg"];  //失败

        }
        exit;
    }
    //ADD BY hzp FOR YUNQIAN duanxin ended 2015/07/20

    //竞价
    public function myjinjia($params)
    {

        // Added for quanxian sheding(leftmenu)  by hzp started  2014/12/25
        $quanx = $this->_dao->getRow("SELECT childroot,IsMain,PowerLimit FROM sys_adminuser WHERE ID='" . $_SESSION['SYS_USERID'] . "'");
        $pos = strripos($quanx["childroot"], "1");

        if ($pos === false) {
            $pos = -1;
        }

        if ($pos >= 0 || $quanx["IsMain"] == 1) {
        } else {
            alert("需要合同管理权限");
            echo '<script>window.close();</script>';
        }
        // Added for quanxian sheding(leftmenu)  by hzp end  2014/12/25

        $type = $params['type'];
        $sta  = $params['sta'];
        $compid = $_SESSION['SYS_COMPANYID'];
        $gcm = $_SESSION['SYS_ZQID'];
        if (isZone($params)) {
            $iszone = "1";
        } else {
            $iszone = "2";
        }
        $where2 = "";
        $wherex = " where 1 ";
        $mids = "";
        if ($params['type'] == "2") {

            $sid = $this->_dao->getOnes("select ID from sm_exc_sales where Mid = '" . $_SESSION['SYS_COMPANYID'] . "' and Createdate>='2014-08-28 00:00:00' ");

            if ($gcm == "" || $compid == $gcm || $iszone == "2") {
                $buysid = $this->_dao->getOnes("Select buy.Sid from sm_exc_buy buy left join sm_exc_sales sales on   buy.Sid = sales.id where sales.Mid='" . $_SESSION['SYS_COMPANYID'] . "' and buy.Createdate>='2014-08-28 00:00:00'");
            } else {
                $buysid = $this->_dao->getOnes("Select buy.Sid from sm_exc_buy buy left join sm_exc_sales sales on   buy.Sid = sales.id where sales.Mid='" . $_SESSION['SYS_COMPANYID'] . "' and buy.Mid='" . $_SESSION['SYS_ZQID'] . "' and buy.Createdate>='2014-08-28 00:00:00'");
            }
            //update by xiakang for isec started 2015/11/27

            $num = array_intersect($sid, $buysid);
            $num = implode(",", $num);
            if (!empty($num)) {
                $where2 .= " and Sid in($num) ";
            } else {
                $where2 .= " and Sid=''";
            }
        } elseif ($params['type'] == "1") {
            //update by xiakang for isec started 2015/11/27
            //$where2 .= " and sm_exc_buy.Mid='".$_SESSION['SYS_COMPANYID']."'";
            if ($gcm == "" || $compid == $gcm || $iszone == "2") {
                //$where2 .= " and sm_exc_buy.Mid='".$_SESSION['SYS_COMPANYID']."'";
                $where2 .= $this->getwhere("sm_exc_buy.Mid", $_SESSION['SYS_COMPANYID'], EQUAL);
            } else {
                //$where2 .= " and sm_exc_buy.Mid='".$_SESSION['SYS_COMPANYID']."' and sm_exc_sales.Mid='".$_SESSION['SYS_ZQID']."' ";
                $where2 .= $this->getwhere("sm_exc_buy.Mid", $_SESSION['SYS_COMPANYID'], EQUAL);
                $where2 .= $this->getwhere("sm_exc_sales.Mid", $_SESSION['SYS_COMPANYID'], EQUAL);
            }
            //update by xiakang for isec ended 2015/11/27
        }

        if ($sta == "1" || $sta == "") { //竞价中
            $where2 .= " and sm_exc_buy.Status=1 and sm_exc_sales.Status!=8";
        }
        if ($sta == "2") { //未成交
            $where2 .= " and sm_exc_buy.Status=3";
        }
        if ($sta == "3") { //已成交
            $where2 .= " and sm_exc_buy.Status=2";
        }

        $where2 .= $this->getwhere("orderID", $params['orderID'], LIKE);
        if ($_SESSION['SYS_USERIsLimit'] == "1") {
            //$where .= " and CreateUser ='".$_SESSION['SYS_USERNAME']."' ";	 
            $where2 .= $this->getwhere("CreateUser", $_SESSION['SYS_COMPANYID'], EQUAL);
        }
        //update by xiakang started 2016/3/14 
        //$total = $this->_dao->getOne("select count(*) from sm_exc_buy,sm_exc_sales where 1 $where2 and  sm_exc_sales.ID=sm_exc_buy.Sid");

        $total = $this->_dao->query("SELECT DISTINCT sm_exc_buy.Mid,sm_exc_buy.Sid,sm_exc_buy.Btid as btid,sm_exc_buy.Status as Status2,sm_exc_buy.CreateDate as cdate, sm_exc_sales.SalesEndDate,sm_exc_sales.ID,sys_company.ComName,sys_company.ID as comid ,sm_exc_sales.StoreType FROM `sm_exc_sales` ,sm_exc_sales_details, sm_exc_buy,sys_company WHERE sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.ID = sm_exc_buy.Sid and sys_company.ID=sm_exc_buy.Mid $where2 order by
        cdate desc,sm_exc_sales.Status");
        $total2 = count($total);
        //update by xiakang ended 2016/3/14
        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 10;
        $start = ($page - 1) * $per;
        $url = "bizorder.php";
        unset($params['page']);

        $zys[] = array();

        $ddall = $this->_dao->query("SELECT DISTINCT sm_exc_buy.Mid,sm_exc_buy.Sid,sm_exc_buy.Btid as btid,sm_exc_buy.Status as Status2,sm_exc_buy.CreateDate as cdate, sm_exc_sales.SalesEndDate,sm_exc_sales.ID,sys_company.ComName,sys_company.ID as comid ,sm_exc_sales.StoreType FROM `sm_exc_sales` ,sm_exc_sales_details, sm_exc_buy,sys_company WHERE sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.ID = sm_exc_buy.Sid and sys_company.ID=sm_exc_buy.Mid $where2 order by
        cdate desc,sm_exc_sales.Status ASC LIMIT $start,$per");

        foreach ($ddall as $key => &$value) {

            $zys[$key] = $this->_dao->query("select sm_exc_sales_details.*,sm_exc_buy.*,sm_exc_buy.BuyQuantity*sm_exc_buy.PriceContention as Tmoney  from sm_exc_buy,sm_exc_sales_details,sm_exc_sales WHERE sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales_details.ID=sm_exc_buy.sdid AND  sm_exc_buy.Mid='" . $value['Mid'] . "' and sm_exc_buy.Sid='" . $value['Sid'] . "'");
        }

        $this->assign("zys", $zys);

        $ComIN = array();
        foreach ($ddall as $temp) {
            $ComIN[$temp['comid']] = $temp['ComName'];
        }

        $this->assign("ComIN", $ComIN);

        $pagebar = $this->pagebar($url, $params, $per, $page, $total2);
        $this->assign("pagebar", $pagebar);
        $this->assign("ddall", $ddall);
        $this->assign("page_title", "竞价列表_会员中心_");
        $this->assign("type", $type);
        $this->assign("params", $params);

        //取得该会员主联系人
        $masteruser = $this->_dao->get_sys_company($_SESSION['SYS_COMPANYID']);
        $comp2 = $this->_dao->get_sys_adminuser($_SESSION['SYS_USERID']);

        $this->assign("comp", $masteruser);
        $this->assign("comp2", $comp2);

        //权限字段
        $this->assign("userrole", $_SESSION['SYS_USERROLE']);
        $this->assign("ddtype", $GLOBALS['DDTYPE']);
        $this->assign("htstatus", $GLOBALS['HT_STATUS']);

        $this->assign("htpaystaus", $GLOBALS['HTPAYSTATUS']);

        $this->setvars();
    }

    public function myjinjialist($params)
    {

        $where = "";
        //$where .= " and sm_exc_buy.Mid= '".$_SESSION['SYS_COMPANYID']."'  ";
        $where .= $this->getwhere("sm_exc_buy.Mid", $_SESSION['SYS_COMPANYID'], EQUAL);

        if ($params['type'] != "") {
            if ($params['type'] == "1") {
                $where .= " and (sm_exc_sales.TradeType ='2' or sm_exc_sales.TradeType ='3') ";
            }
            if ($params['type'] == "2") {
                $where .= " and (sm_exc_sales.TradeType !='2' and sm_exc_sales.TradeType !='3') ";
            }
        }
        $where .= $this->getwhere("sm_exc_buy.Status", $params['Status'], EQUAL);

        $total = $this->_dao->getOne("SELECT COUNT(sm_exc_buy.ID) as c FROM sm_exc_buy left join sm_exc_sales on sm_exc_buy.Sid = sm_exc_sales.ID where 1  $where");

        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 6;
        $start = ($page - 1) * $per;
        $url = "member.php";
        unset($params['page']);

        $jinjialist = $this->_dao->query("SELECT * , sm_exc_buy.id as cid,sm_exc_buy.CreateDate as cdate,sm_exc_buy.Status as cStatus FROM sm_exc_buy left join sm_exc_sales on sm_exc_buy.Sid = sm_exc_sales.ID where 1  $where order by sm_exc_buy.CreateDate DESC LIMIT $start, $per");

        $this->assign("jinjialist", $jinjialist);

        $pagebar = $this->pagebar($url, $params, $per, $page, $total);

        $this->assign("params", $params);
        $this->assign("pagebar", $pagebar);
        $this->assign("jinjiastatus", $GLOBALS['JINJIA_STATUS']);
        $this->assign("dinggoustatus", $GLOBALS['DINGGOU_STATUS']);
        $this->assign("page_title", "我参与的采购列表_会员中心_");
    }

    public function shoppingcarts($params)
    {
        $this->checksession();
        $type = $params['type'];

        $this->assign("params", $params);
        if ($_SESSION['SYS_COMPANYID'] == '') {
        }
        //取得该会员主联系人
        $masteruser = $this->_dao->get_sys_company($_SESSION['SYS_COMPANYID']);
        $comp2 = $this->_dao->get_sys_adminuser($_SESSION['SYS_USERID']);

        $this->assign("comp", $masteruser);
        $this->assign("comp2", $comp2);

        if ($type == 1) {

            $data = $this->_dao->query("SELECT * FROM sm_buy_car where  Mid='" . $_SESSION['SYS_COMPANYID'] . "' and BuyType =1 order by CreateDate DESC");
            //$data = $this->gwc_gl( $data );

        } else if ($type == 2) {

            $data = $this->_dao->query("SELECT * FROM sm_buy_car where  Mid='" . $_SESSION['SYS_COMPANYID'] . "' and BuyType =2 order by CreateDate DESC");
            $sid = $data['Sid'];

            //$data = $this->gwc_gl2( $data );
        }

        $this->assign("gwc", $data);
        $connectinfo = '';
        $connectinfo .= '联系人: ' . $masteruser['ARealName'] . "\r\n";
        $connectinfo .= '联系地址: ' . $masteruser['Address'] . "\r\n";
        $connectinfo .= '联系电话: ' . $masteruser['Mobile'] . "\r\n";
        $this->assign('connectinfo', $connectinfo);
        $this->assign("page_title", "购物车_");
    }

    public function shoppingcart($params)
    {
        $this->checksession();
		$type=$params['type'] == ""?1:$params['type'];
        // Added for huadong gangshi  by hzp started  2015/03/05
        if ($_SESSION['HDGS']['gsid'] != "") {
            $sessiongsid = $_SESSION['mygwc_buy'][$v['ID']]['gsid'];
            $gsid = $GLOBALS['MODEMID'][$sessiongsid];
            $this->assign("gsid", $gsid);
        }
        // Added for huadong gangshi  by hzp ended  2015/03/05
        $this->assign("params", $params);
        if ($_SESSION['SYS_COMPANYID'] == '') {
            //alert( "您还没有登录, 不能使用购物车" );
            //$ref=urlencode("bizorder.php?view=gwc");
            //$ref=urlencode(str_replace("&amp;","&",$params["ref"]));
            //goURL( "user.php?view=login&ref=".$ref  );
        }

        //取得该会员主联系人
        $masteruser = $this->_dao->get_sys_company($_SESSION['SYS_COMPANYID']);
        $comp2 = $this->_dao->get_sys_adminuser($_SESSION['SYS_USERID']);

        $this->assign("comp", $masteruser);
        $this->assign("comp2", $comp2);
        //echo "type:".$type;
        if ($type == 1) {

            $resids = array();
            if (isset($_SESSION['mygwc_buy']) && is_array($_SESSION['mygwc_buy'])) {
                $resids = array_keys($_SESSION['mygwc_buy']);
            }
            //print_r($resids);
            if (count($resids) > 0) {
                //include_once ( APP_DIR . "/ajax/AjaxDao.inc.php" );
                //$dao = new AjaxDao( "SYS" );

                $current_ids = implode(',', $resids);

                $data = $this->_dao->getResByIds($current_ids, $type);
            }
            $data = $this->gwc_gl($data);
        } else if ($type == 2) {
            $resids2 = array();
            if (isset($_SESSION['mygwc_pro']) && is_array($_SESSION['mygwc_pro'])) {
                $resids2 = array_keys($_SESSION['mygwc_pro']);
            }
            if (count($resids2) > 0) {
                //include_once ( APP_DIR . "/ajax/AjaxDao.inc.php" );
                //$dao = new AjaxDao( "SYS" );
                //echo " pro  not null";
                $current_ids2 = implode(',', $resids2);

                // foreach($current_ids2 as $id){
                //      //echo " pro  not null:".$id;
                // }
                // Updated for huadong gangshi  by hzp started  2014/12/25
                if ($_SESSION['HDGS']['gsid'] != "") {
                    $data = $this->_dao->getResByIds_gs($current_ids2, $type);
                    foreach ($data as &$v) {
                        $VidSID = $v['Pid'] * (-1);

                        //查出在华东钢市购买的数量
                        $C = $this->_dao->getOne("SELECT SUM(BuyQuantity) as c FROM sm_exc_dd,sm_exc_dd_tag WHERE sm_exc_dd_tag.ID = sm_exc_dd.Dtid and  Sid='" . $VidSID . "' and sm_exc_dd_tag.Did='" . $GLOBALS['MODEMID'][$sessiongsid] . "' and sm_exc_dd_tag.Status=2 ");
                        //查出不在华东钢市购买的数量
                        $CC = $this->_dao->getOne("SELECT SUM(BuyQuantity) as c FROM sm_exc_dd,sm_exc_dd_tag WHERE sm_exc_dd_tag.ID = sm_exc_dd.Dtid and  Sid='" . $V['Pid'] . "' and sm_exc_dd_tag.Did != '" . $GLOBALS['MODEMID'][$sessiongsid] . "' and sm_exc_dd_tag.Mid != '" . $GLOBALS['MODEMID'][$sessiongsid] . "' and sm_exc_dd_tag.Status=2 ");
                        if ($CC == null) {
                            $CC = 0;
                        }
                        if ($C == null) {
                            $C = 0;
                        }
                        $cc = $C + $CC;
                        if ($v['StoreType'] == 2) {
                            $v['QuantitySalesed'] = $v['QuantitySales'];
                        } else {
                            $v['QuantitySalesed'] = $cc;
                        }
                    }
                } else {
                    $data = $this->_dao->getResByIds($current_ids2, $type);
                }
                //$data = $this->_dao->getResByIds( $current_ids2,$type );
                // Updated for huadong gangshi  by hzp end  2014/12/25
                $sid = $data['Sid'];
            }
            $data = $this->gwc_gl2($data);
        }

        $this->assign("gwc", $data);
        $this->assign( "gwc_num", count($data) );

        $connectinfo = '';
        $connectinfo .= '联系人: ' . $masteruser['ARealName'] . "\r\n";
        $connectinfo .= '联系地址: ' . $masteruser['Address'] . "\r\n";
        $connectinfo .= '联系电话: ' . $masteruser['Mobile'] . "\r\n";
        $this->assign('connectinfo', $connectinfo);
        $this->assign("page_title", "购物车_");

        //Added by quanjw for jinshuzhipin start 2015/3/9
        $this->assign("ZiYuType", $GLOBALS["ZiYuType"]);
        $this->assign("MEITAN_VID", $GLOBALS["MEITAN_VID"]);
        $this->assign("JIAOTAN_VID", $GLOBALS["JIAOTAN_VID"]);
        $this->assign("SHUINI_VID", $GLOBALS["SHUINI_VID"]);
        $this->assign("JINSHU_VID", $GLOBALS["JINSHU_VID"]);
        $this->assign("ZY_VID", $GLOBALS["ZY_VID"]);
        $this->setvars();
    }

    //添加供应商
    public function addprovider($resids)
    {

        if ($_SESSION['SYS_COMPANYID'] == '') {
            goURL("user.php?view=login");
        }

        if ($resids != '') {
            $resids = explode(",", $resids);
            //$current_resids = $_SESSION['mygwc'];
            //$current_ids = array_keys( $_SESSION['mygwc'] );
            foreach ($resids as $resid) {

                $Mid = $this->_dao->getOne("select Mid from sm_exc_sales where ID =" . $resid);
                $com = $this->_dao->get_sys_company($Mid);
                $comname = $com['ComName'];

                $ids = $this->_dao->query("select * from sys_user_mygys ");
                $flag = "";
                foreach ($ids as $key) {

                    $id = $key['ComID'];
                    if ($id == $Mid) {
                        $flag = "1";
                    }
                }
                //alert($flag);
                if ($flag != "1") {
                    // alert("add  new ");
                    $this->_dao->execute("insert into sys_user_mygys set level=2, ComType=1, IsLsCj=0, ComID=" . $Mid . " ,ComName='" . $comname . "' ,MyID = " . $_SESSION['SYS_COMPANYID']);
                } else {
                    //alert("add  repeat!!! ");
                }
            }
        }

        alert("已加入我的供应商");
        gourl("bizorder.php?view=list_resource");
    }

    //定制资源界面
    public function list_resource($params)
    {

        //$this->clearalldata();
        $this->setvars();
        $wherecg = "and SalesType in (1,5)";
        $varietys = $this->_dao->Aquery(" SELECT id,kname FROM `biz_key` WHERE `ktype` = 1 ", 30 * 3600);

        $str = '';
        $str .= $this->getRequestStr("pm", $params['pm']);
        $wherecg .= $this->getwhere("VarietyName", $params['pm'], LIKE);

        $str .= $this->getRequestStr("city", $params['city']);
        $wherecg .= $this->getwhere("PickUpCity", $params['city'], LIKE);

        $str .= $this->getRequestStr("gc", $params['gc']);
        $wherecg .= $this->getwhere("OriginCode", $params['gc'], LIKE);

        $str .= $this->getRequestStr("caizhi", $params['caizhi']);
        $wherecg .= $this->getwhere("MaterialCode", $params['caizhi'], LIKE);

        $str .= $this->getRequestStr("guige", $params['guige']);
        $wherecg .= $this->getwhere("SpecCode", $params['guige'], LIKE);

        $str .= $this->getRequestStr("jhck", $params['jhck']);
        $wherecg .= $this->getwhere("jhck", $params['jhck'], LIKE);


        $str .= $this->getRequestStr("jg1", $params['jg1']);
        $wherecg .= $this->getwhere("SalesMinPrice", $params['jg1'], GREATER_THAN);
        $str .= $this->getRequestStr("jg2", $params['jg2']);
        $wherecg .= $this->getwhere("SalesMinPrice", $params['jg2'], LESS_THAN);


        $str .= $this->getRequestStr("hd1", $params['hd1']);
        $wherecg .= $this->getwhere("hd", $params['hd1'], GREATER_THAN);
        $str .= $this->getRequestStr("hd2", $params['hd2']);
        $wherecg .= $this->getwhere("hd", $params['hd2'], LESS_THAN);

        $str .= $this->getRequestStr("kd1", $params['hd1']);
        $wherecg .= $this->getwhere("kd", $params['hd1'], GREATER_THAN);
        $str .= $this->getRequestStr("kd2", $params['hd2']);
        $wherecg .= $this->getwhere("kd", $params['hd2'], LESS_THAN);

        $str .= $this->getRequestStr("cd1", $params['cd1']);
        $wherecg .= $this->getwhere("cd", $params['cd1'], GREATER_THAN);
        $str .= $this->getRequestStr("cd2", $params['cd2']);
        $wherecg .= $this->getwhere("cd", $params['cd2'], LESS_THAN);

        if ($params['comname'] != "") {
            $mid = $this->_dao->getOnes("select ID from sys_company where ( ComName  LIKE '%" . mysqli_real_escape_string($this->mysqli, $params['comname']) . "%'   or ComNameShort  LIKE '%" . mysqli_real_escape_string($this->mysqli, $params['comname']) . "%'  )     ");
            $mid = implode(",", $mid);
            if ($mid) {
                $wherecg .= "and  Mid  in ($mid)  ";
            } else {
                $wherecg .= "and  Mid  =0  ";
            }
            $str .= "&comname=$params[comname]";
        }

        if ($params['sort'] == "1") {
            $where2 = " ORDER BY SalesMinPrice ASC,";
        }
        $str .= $this->getRequestStr("sort", $params['sort']);
        if ($params['sort'] == "2" || $params['sort'] == '') {
            $where2 = " ORDER BY sm_exc_sales.CreateDate DESC,";
        }

        $search = array();
        $str .= $this->getRequestStr("sptype", $params[sptype]);


        if ($params['sptype'] == "1") {

            $sptype = 1;
            $this->assign("page_title", "钢厂资源_");
        }
        if ($params['sptype'] == "2") {

            $sptype = 2;
            $this->assign("page_title", "贸易商资源_");
        }

        if ($params['sptype'] == "4") {

            $keywords = $this->_dao->query("select * from sys_user_zydz where ComID=" . $_SESSION['SYS_COMPANYID']);
            $sql = "";
            foreach ($keywords as $key) {

                $keywd = $key['VarietyName'];
                $arr = $this->getkeyid($keywd);
                $id = $arr["id"];
                //alert($keywd.":".$id);
                if ($id != "") {
                    $sql = $sql . "or  ( pm_id='" . $id . "' OR pm_parentid='" . $id . "' OR pm_ppid='" . $id . "' ) ";
                }
            }

            if (!empty($sql)) {
                $sql = " and (" . substr($sql, 3) . ")";
            } else {
                $sql = " and 1!=1";
            }

            $wherecg .= $sql;

            $sptype = 4;
            $this->assign("page_title", "定制资源_");
        }

        $params['search'] = mysqli_real_escape_string($this->mysqli, $params['search']);

        if ($params['search'] != "") {
            $searchsql = $this->getsqlbykeyword($params['search']);

            $wherecg .= $searchsql;
            $str .= $this->getRequestStr("search", $params['search']);
        }

        //厂家
        $factory = $this->getNotNull($params['factory'], $params['factory1']);
        $wherecg .= $this->getwhere("OriginCode", $factory, LIKE);
        $this->setValueWithNotNull($search["factory"], $factory);

        //城市
        $city = $this->getNotNull($params['city'], $params['city1']);
        $wherecg .= $this->getwhere("PickUpCity", $city, LIKE);
        $this->setValueWithNotNull($search["city"], $city);

        //品种
        $variety = $this->getNotNull($params['variety'], $params['variety1']);
        $wherecg .= $this->getwhere("pm_parentid", $variety, EQUAL);
        $this->setValueWithNotNull($search["variety"], $varietys[$variety]);
        $str .= $this->getRequestStr("variety2", $variety);

        //材质
        $materialcode = $this->getNotNull($params['materialcode'], $params['materialcode1']);
        $wherecg .= $this->getwhere("MaterialCode", $materialcode, LIKE);
        $this->setValueWithNotNull($search["materialcode"], $materialcode);

        //规格
        $size = $this->getNotNull($params['size'], $params['size1']);
        $wherecg .= $this->getwhere("SpecCode", $size, LIKE);
        $this->setValueWithNotNull($search["size"], $size);

        if ($params['sort'] == "1") {
            $where2 = " ORDER BY SalesMinPrice DESC,";
            //$str .= "&sort=".$params['sort'];
        }
        $str .= $this->getRequestStr("sort", $params['sort']);
        if ($params['sort'] == "2") {
            $where2 = " ORDER BY sm_exc_sales.CreateDate DESC,";
            //$str .= "&sort=".$params['sort'];
        }

        foreach ($search as $k => $v) {
            $str .= "&" . $k . "=" . $v;
        }
        $this->assign("str", $str);
        $this->assign("search", $search);

        $tjxz = $params['size'] != "" || $params['materialcode'] != "" || $params['variety'] || $params['city'] || $params['factory'] || $params['search'];

        //厂家
        if ($tjxz) {
            $factory = $this->_dao->Aquery("select  sm_exc_sales_details.ID, OriginCode from sm_exc_sales_details,sm_exc_sales,sys_company where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and OriginCode!=''   $wherecg   group by 	MaterialCode   limit 8");
            $this->assign("factory", $factory);
            $factory2 = $this->_dao->Aquery("select  sm_exc_sales_details.ID, OriginCode from sm_exc_sales_details,sm_exc_sales,sys_company where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and OriginCode!=''   $wherecg   group by 	MaterialCode   limit 8,50");
            $this->assign("factory2", $factory2);
        } else {
            $factory = $this->_dao->Aquery("SELECT id,kname FROM `biz_key` WHERE `ktype` = 3  limit 14", 20 * 3600);
            $this->assign("factory", $factory);
            $factory2 = $this->_dao->Aquery("SELECT id,kname FROM `biz_key` WHERE `ktype` = 3   limit 14,28", 20 * 3600);
            $this->assign("factory2", $factory2);
        }

        //城市
        if ($tjxz) {
            $city = $this->_dao->Aquery(" select  sm_exc_sales.ID,PickUpCity  from sm_exc_sales_details,sm_exc_sales,sys_company where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and  PickUpCity!=''  $wherecg   group by 	PickUpCity  limit 11", 20 * 3600);
            $this->assign("city", $city);
            $city2 = $this->_dao->Aquery(" select  sm_exc_sales.ID,PickUpCity  from sm_exc_sales_details,sm_exc_sales,sys_company where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2  and PickUpCity!='' $wherecg   group by 	PickUpCity  limit 11,50", 20 * 3600);
            $this->assign("city2", $city2);
        } else {

            $city = $this->_dao->Aquery(" SELECT id,kname FROM `biz_key` WHERE `ktype` = 4  and parentid !=0  order by id asc limit 16", 20 * 3600);
            $this->assign("city", $city);
            $city2 = $this->_dao->Aquery(" SELECT id,kname FROM `biz_key` WHERE `ktype` = 4  and parentid !=0  order by id asc limit 16,32", 20 * 3600);
            $this->assign("city2", $city2);
        }

        //品种
        $variety = $this->_dao->Aquery(" SELECT id,kname FROM `biz_key` WHERE `ktype` = 1 and parentid !=0 order by id asc limit 14", 20 * 3600);
        $this->assign("variety", $variety);
        $variety2 = $this->_dao->Aquery(" SELECT id,kname FROM `biz_key` WHERE `ktype` = 1  and parentid !=0 order by id asc limit 14,28", 20 * 3600);
        $this->assign("variety2", $variety2);

        //材质
        if ($tjxz) {
            $materialcode = $this->_dao->AQuery("select  sm_exc_sales_details.ID, MaterialCode from sm_exc_sales_details,sm_exc_sales,sys_company where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and MaterialCode!='' $wherecg   group by 	MaterialCode  limit 8", 20 * 3600);
            $this->assign("materialcode", $materialcode);
            $materialcode2 = $this->_dao->AQuery("select  sm_exc_sales_details.ID, MaterialCode from sm_exc_sales_details,sm_exc_sales,sys_company where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and MaterialCode!=''  $wherecg   group by 	MaterialCode   limit 8,50", 20 * 3600);
            $this->assign("materialcode2", $materialcode2);
        } else {
            $materialcode = $this->_dao->AQuery(" SELECT id,kname FROM `biz_key` WHERE `ktype` = 2 limit 12", 20 * 3600);
            $this->assign("materialcode", $materialcode);
            $materialcode2 = $this->_dao->AQuery(" SELECT id,kname FROM `biz_key` WHERE `ktype` = 2  limit 12,24", 20 * 3600);
            $this->assign("materialcode2", $materialcode2);
        }
        //规格
        $size = $this->_dao->AQuery("select  sm_exc_sales_details.ID, SpecCode from sm_exc_sales_details,sm_exc_sales,sys_company where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and SpecCode!='' $wherecg   group by 	SpecCode  limit 8", 20 * 3600);
        $this->assign("size", $size);
        $size2 = $this->_dao->AQuery("select  sm_exc_sales_details.ID, SpecCode from sm_exc_sales_details,sm_exc_sales,sys_company where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and SpecCode!=''  $wherecg   group by 	SpecCode   limit 8,50", 20 * 3600);
        $this->assign("size2", $size2);

        //获取大品种 数目
        $bigpz = $this->_dao->AQuery("select ID,PzName from sys_big_pz  where Status = 1  order by Ssort", 3600);
        $this->assign("bigpz", $bigpz);

        //全部
        if ($params['sptype'] == "4") {      //定制资源
            //alert("定制资源");
            /*  $keywords = $this->_dao->query("select * from sys_user_zydz where ComID=".$_SESSION['SYS_COMPANYID']);
                
                $sql = "";
                 
                 foreach($keywords as $key){
                   
                    $keywd=$key['VarietyName'];
                   
                    $arr = $this->getkeyid($keywd);
                    $id = $arr["id"];

                     $sql = $sql . "or  ( pm_id='".$id."' OR pm_parentid='".$id."' OR pm_ppid='".$id."' ) ";
                 }

                 if(!empty($sql)){
                    
                    $sql = " and (". substr($sql,3).")";
                 }else{
                    $sql = " and 1!=1";
                 }
                 echo  $sql; 
                exit;*/
            //echo  "SELECT COUNT(sm_exc_sales.ID) FROM sm_exc_sales,sm_exc_sales_details, sys_company  WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID  and   sm_exc_sales.Status =2  $wherecg " ;

            $totalalls = $this->_dao->getOne("SELECT COUNT(sm_exc_sales.ID) FROM sm_exc_sales,sm_exc_sales_details WHERE sm_exc_sales_details.Pid=sm_exc_sales.ID  and   sm_exc_sales.Status =2  $wherecg ",3600);
        } elseif ($params['sptype'] == "1") {    //钢厂资源
            // alert("钢厂资源");
            $totalalls = $this->_dao->getOne("SELECT COUNT(sm_exc_sales.ID) FROM sm_exc_sales,sm_exc_sales_details, sys_company ,sys_user_mygys  WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID   and sys_company.ID=sys_user_mygys.ComID  and sys_user_mygys.MyID=" . $_SESSION['SYS_COMPANYID'] . " and sys_user_mygys.ComType=1 and sm_exc_sales.Status =2  $wherecg ",3600);
        } elseif ($params['sptype'] == "2") {   //贸易商资源
            //alert("贸易商资源");
            $totalalls = $this->_dao->getOne("SELECT COUNT(sm_exc_sales.ID) FROM sm_exc_sales,sm_exc_sales_details, sys_company ,sys_user_mygys  WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sys_user_mygys.ComID  and sys_user_mygys.MyID=" . $_SESSION['SYS_COMPANYID'] . " and sys_user_mygys.ComType=2 and sm_exc_sales.Status =2  $wherecg ",3600);
        } else {
            //alert("全部资源");
            $totalalls = $this->_dao->getOne("SELECT COUNT(sm_exc_sales.ID) FROM sm_exc_sales,sm_exc_sales_details WHERE sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.Status =2 $wherecg  ",3600);
        }

        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 25;
        $start = ($page - 1) * $per;

        if ($params['sptype'] == "4") {
            //alert("搜索定制资源");
            $pzzy[0] = $this->_dao->query("SELECT sm_exc_sales_details.ID as sdid,sm_exc_sales_details.*,sm_exc_sales.*, sys_company.ComNameShort,sys_company.ComName,  sys_company.ContactTel,sys_company.Address   FROM sm_exc_sales_details, sys_company,sm_exc_sales  WHERE sys_company.ID = sm_exc_sales.Mid   and sm_exc_sales_details.Pid=sm_exc_sales.ID  AND sm_exc_sales.Status =2 $wherecg  order by  sm_exc_sales.id DESC  LIMIT $per", 0);
        } elseif ($params['sptype'] == "1") {
            // alert("搜索钢厂资源");
            $pzzy[0] = $this->_dao->query("SELECT sm_exc_sales_details.ID as sdid,sm_exc_sales_details.*,sm_exc_sales.*, sys_company.ComNameShort,sys_company.ComName,      sys_company.ContactTel,sys_company.Address   FROM sm_exc_sales_details, sys_company,sm_exc_sales,sys_user_mygys  WHERE sys_company.ID = sm_exc_sales.Mid   and sm_exc_sales_details.Pid=sm_exc_sales.ID   and sys_company.ID=sys_user_mygys.ComID 
                 and  sys_user_mygys.MyID=" . $_SESSION['SYS_COMPANYID'] . " and sys_user_mygys.ComType=1  and sm_exc_sales.Status =2  order by  sm_exc_sales.id DESC  LIMIT $per", 0);
        } elseif ($params['sptype'] == "2") {
            // alert("搜索贸易商资源");
            $pzzy[0] = $this->_dao->query("SELECT sm_exc_sales_details.ID as sdid,sm_exc_sales_details.*,sm_exc_sales.*, sys_company.ComNameShort,sys_company.ComName,      sys_company.ContactTel,sys_company.Address   FROM sm_exc_sales_details, sys_company,sm_exc_sales,sys_user_mygys  WHERE sys_company.ID = sm_exc_sales.Mid   and sm_exc_sales_details.Pid=sm_exc_sales.ID   and sys_company.ID=sys_user_mygys.ComID 
                 and  sys_user_mygys.MyID=" . $_SESSION['SYS_COMPANYID'] . " and sys_user_mygys.ComType=2  and sm_exc_sales.Status =2  order by  sm_exc_sales.id DESC  LIMIT $per", 0);
        } else {
            // alert("搜索全部资源");
            $pzzy[0] = $this->_dao->query("SELECT sm_exc_sales_details.ID as sdid,sm_exc_sales_details.*,sm_exc_sales.*, sys_company.ComNameShort,sys_company.ComName,(select ARealName from sys_adminuser where ComID = sys_company.ID  and IsMain=1 and Status=1 ) as LinkMan ,sys_company.ContactTel,sys_company.Address   FROM sm_exc_sales_details, sys_company,sm_exc_sales WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID  AND sm_exc_sales.Status =2 $wherecg  $where2  sm_exc_sales.id DESC LIMIT $per", 0);
        }
        //added by hezp for lock_sales zy started 2016/11/01
        foreach ($pzzy[0] as &$tmp) {
            $tmp['QuantitySalesed'] = $this->get_lock_sales_num($tmp['QuantitySalesed'], $tmp['Pid'], $tmp['sdid']);
        }
        //added by hezp for lock_sales zy ended 2016/11/01

        $this->assign("pzzy", $pzzy);

        //all
        $totalall = ceil($totalalls / $per);
        if ($totalall < 1) {
            $totalall = 1;
        }
        $this->assign("totalall", $totalall);
        $this->assign("totalalls", $totalalls);



        $this->assign("page", $page);
        $pre = $page - 1 < 1 ? 1 : $page - 1;
        //all
        $nextall = $page + 1 > $totalall ? $totalall : $page + 1;

        $this->assign("pre", $pre);
        $this->assign("nextall", $nextall);
        $this->assign("params", $params);

        $this->assign("page_title", "网上现货_");

        //=======================================
        $page2 = $params['page2'] == '' ? 1 : $params['page2'];
        $url2 = "listing.php";
        $per2 = 10;
        $start2 = ($page2 - 1) * $per2;
        unset($params['page2']);

        $totalcj = $this->_dao->getOne("SELECT COUNT(*) as c  FROM sm_contract_transaction_detail as sctd left join sm_contract_transaction as sct on sct.ID=sctd.TID  where (sct.SalesType=1 or sct.SalesType=5 )   and  VarietyName!='' ");

        $pzzycj = $this->_dao->query("SELECT SalesType,PickUpCity,Mid_Consignee,Mid_Shipper,Sid,VarietyName,OriginCode,SpecCode,MaterialCode,BuyQuantity,SalesPrice,sct.CreateDate as cjdate  from sm_contract_transaction_detail as sctd left join sm_contract_transaction as sct on sct.ID=sctd.TID  where  (SalesType=1 or SalesType=5 )    and  VarietyName!=''   order by cjdate desc  limit $start2,$per2");

        foreach ($pzzycj as &$tmp) {
            $tmp['QuantitySales'] = $tmp['BuyQuantity'];
            $tmp['QuantitySalesed'] = $tmp['BuyQuantity'];
            $tmp['SalesMinPrice'] = $tmp['SalesPrice'];
            $tmp['Mid'] = $tmp['Mid_Shipper'];
            $tmp['Mid2'] = $tmp['Mid_Consignee'];
            $tmp['id'] = $this->_dao->getOne("select Dtid from sm_exc_dd where sm_exc_dd.Sid = '" . $tmp['Sid'] . "' limit 1 ");

            $com1 = $this->_dao->get_sys_company($tmp['Mid_Shipper']);
            $com2 = $this->_dao->get_sys_company($tmp['Mid_Consignee']);

            $tmp['comname'] = $com1['ComNameShort'];
            $tmp['comname2'] = $com2['ComNameShort'];
            $tmp['cj1'] = $com2['IsShowCj'];
            $tmp['cj2'] = $com1['IsShowCj'];
        }

        $this->assign("pzzycj", $pzzycj);
        $pagebarcj = $this->pagebar2($url2, $params, $per2, $page2, $totalcj, 0, 2);
        $this->assign("pagebarcj", $pagebarcj);
        //==================================
    }

    //专卖
    public function list_zqzm($params)
    {       
        if($params['debug']==1&&EDITION == 'dev'){
            $this->_dao->_reader->adodb->debug = true;
        }
        $interval = 30; // 5秒或10秒
        $timestamp = time(); // 当前时间戳
        $aligned_timestamp = $timestamp - ($timestamp % $interval);
        $nowtime = date('Y-m-d H:i:s', $aligned_timestamp);
        // echo $nowtime;
        if (EDITION == 'dev') {
            $nowtime = '2018-04-17 00:00:00';
        }
        $earlytime=date('Y-m-d H:i:s', strtotime($nowtime . ' -1 year'));
        //added by tuxw for isec started 20160127
        // print_r($params);
        if (DZ_OR_YGW != 0)
            $nowdate = date('Y-m-d H:i:s', time());
        global $HUADONGZIGONGSI;
        $this->assign('HUADONGZIGONGSI', $HUADONGZIGONGSI);
        $this->setvars();

        $ResourceType_ID = array(
            "ZY_VID" => $GLOBALS["ZY_VID"],
            "MEITAN_VID" => $GLOBALS["MEITAN_VID"],
            "JIAOTAN_VID" => $GLOBALS["JIAOTAN_VID"],
            "SHUINI_VID" => $GLOBALS["SHUINI_VID"],
            "JINSHU_VID" => $GLOBALS["JINSHU_VID"]
        );

        // sm_sys_storecustom 的企业类型和sys_company的企业类型的映射关系
        $Store_Company_Comtype_MAP = array(
            '1' => array('store_type' => '1', 'comp_type' => '3', 'sales_type' => '5', 'page_title' => '钢厂专卖_',), // 钢厂
            '2' => array('store_type' => '2', 'comp_type' => '1', 'sales_type' => '5', 'page_title' => '商家专卖_',), // 商家
            '3' => array('store_type' => '3', 'comp_type' => '4', 'sales_type' => '2', 'page_title' => '最终用户_',), // 最终用户
            '5' => array('store_type' => '5', 'comp_type' => '20', 'sales_type' => '5', 'page_title' => '水泥企业_',), // 水泥
            '6' => array('store_type' => '6', 'comp_type' => '8', 'sales_type' => '5', 'page_title' => '原料企业_',), // 原料企业
            '7' => array('store_type' => '7', 'comp_type' => '11', 'sales_type' => '5', 'page_title' => '化工企业_',), // 化工
            '8' => array('store_type' => '8', 'comp_type' => '24', 'sales_type' => '2', 'page_title' => '工程用户_',), // 工程用户
            '9' => array('store_type' => '9', 'comp_type' => '1', 'sales_type' => '2', 'page_title' => '商家专卖_',), // 商家(商务通)
            'gczq_all' => array('store_type' => '1,2,5,6,7', 'comp_type' => '3,1,8,20,11,24,4', 'sales_type' => '5', 'page_title' => '全部_',), // 钢厂专区全部
            'sjzq_all' => array('store_type' => '8,3', 'comp_type' => '24,4,1', 'sales_type' => '2', 'page_title' => '全部_',), // 商家专区全部
        );


        $this->assign('ResourceType_ID', $ResourceType_ID);
        // Updated for magangcihu by Zhu Dahua ended  2015/05/11
        $wherecg = "";
        $wherecj = "";
        $wherejl = "";
        $str = "";
        $varietys = $this->_dao->Aquery(" SELECT id,kname FROM `biz_key` WHERE `ktype` = 1 ", 30 * 3600);
        if (isset($params['comtype']) && isset($Store_Company_Comtype_MAP[$params['comtype']])) {
            $comptype = $params['comtype'];
            $type_info = $Store_Company_Comtype_MAP[$comptype];
            // print_r($type_info);
            if (DZ_OR_YGW == 0) {
                $ctype = $type_info['store_type'];
                $wherecg .= "and sm_exc_sales.Mid = sm_sys_storecustom.Mid and sm_sys_storecustom.CompanyType in ($ctype)) ";
                $wherecj .= "and Mid_Shipper = sm_sys_storecustom.Mid and sm_sys_storecustom.CompanyType in ($ctype)) ";
            } else {
                $ctype = $type_info['comp_type'];
                $wherecg .= "and sm_exc_sales.Mid = sys_company.id and (sys_company.ComType in ($ctype) or sys_company.ComType2 in ($ctype) or sys_company.ComType3 in ($ctype)) and sm_exc_sales.Mid = sm_sys_storecustom.Mid and sm_exc_sales.SalesEndDate> '".$nowtime."'";
                $wherecj .= "and Mid_Shipper = sys_company.id and (sys_company.ComType in($ctype) or sys_company.ComType2 in ($ctype) or sys_company.ComType3 in ($ctype)) and Mid_Shipper = sm_sys_storecustom.Mid ";
            }
            $sctype = $type_info['store_type'];
            $wherejl .= "and sm_exc_sales.Mid = sm_sys_storecustom.Mid where CompanyType in ($sctype)) ";
            $str .= "&comtype=" . $params['comtype'];
            //$comtype = 1;
            $this->assign("page_title", $type_info['page_title']);
            $wherecg .= " and SalesType in ($type_info[sales_type])";
            $wherecj .= " and SalesType in ($type_info[sales_type])";
        } else if (!isset($params['comtype'])) {
            if (isset($params['sptype'])) {
                $sptype = mysqli_real_escape_string($this->mysqli, $params['sptype']);
                // print_r($sptype);
                $ctype = 1;
                if ($sptype == 2 || $sptype == 9) $ctype = 2;
                $storecustom_bycomtype_sql = " select Mid from sm_sys_storecustom  where CompanyType in ($ctype) "; //根据ctype 查询公司id,组合成ids
                $storecustom_bycomtype = $this->_dao->query($storecustom_bycomtype_sql, $interval);
                // print_r($storecustom_bycomtype);
                $ids = '';
                foreach ($storecustom_bycomtype as $key => $value) {
                    $ids .= $value['Mid'] . ',';
                }
                $ids = trim($ids, ',');
                // print_r($ids);
                $wherecg .= " and sm_exc_sales.Mid in ($ids) ";
                // $wherecg .= " and sm_exc_sales.Mid = sm_sys_storecustom.Mid and CompanyType in ($ctype) ";
                $wherecj .= " and Mid_Shipper in ($ids) ";
                // $wherecj .= " and Mid_Shipper = sm_sys_storecustom.Mid and CompanyType in ($ctype) ";
                $wherejl .= " and sm_exc_sales.Mid in ($ids) ";
                // $wherejl .= " and sm_exc_sales.Mid = sm_sys_storecustom.Mid and CompanyType in ($ctype) ";
                $str .= "&sptype=" . $sptype;

                if ($sptype == 9) {
                    $wherecg .= " and Isdxcg!=1 ";
                    //$wherecj .= "and SalesType in (1,2,5,6,8)";
                    $this->assign("page_title", "商家专区_");
                } else {
                    $wherecg .= " and SalesType in (1,5)";
                    $wherecj .= " and SalesType in (1,5)";

                    $title = "钢厂专卖_";
                    if ($sptype == 2)
                        $title = "商家专卖_";
                    else
                        $title = "钢厂专区_";

                    $this->assign("page_title", $title);
                }
            }
        }

        $search = array();
        //added by tuxw ended 20150930
        if ($params['TradeType'] != "") {
            $wherecg .= "  AND sm_exc_sales.TradeType =  '" . $params['TradeType'] . "' ";
            $str .= "&TradeType=" . $params['TradeType'];
        } else {
            $str .= "&TradeType=" . $params['TradeType'];
        }
        //资源分类
        // Updated by quanjw for pzfl start 2015/10/21

        if (DZ_OR_YGW == '0') {
            if ($params['ZiyuType'] == "" || $params['ZiyuType'] == "0") {
                $fl = implode(",", $GLOBALS["TYPE_GC"]);
                $wherecg .= " AND ( Vid  in( $fl) )";
                $wherecj .= " AND ( Vid  in( $fl) )";
                $str .= "&ZiyuType=" . $params['ZiyuType'];
            } else {
                $wherecg .= " AND sm_exc_sales_details.Vid = " . intval($params['ZiyuType']);
                $wherecj .= " AND Vid = " . intval($params['ZiyuType']);
                $str .= "&ZiyuType=" . $params['ZiyuType'];
            }
        }
        // Updated by quanjw for pzfl end 2015/10/21
        //Added by quanjw for jinshuzhipin start 2015/3/15
        //品种选择
        switch ($params['ZiyuType']) {
            case $GLOBALS["MEITAN_VID"]:
                $whereVariety = " and parentid = " . $GLOBALS["MEITAN_PID"];
                break;
            case $GLOBALS["JIAOTAN_VID"]:
                $whereVariety = " and parentid = " . $GLOBALS["JIAOTAN_PID"];
                break;
            case $GLOBALS["SHUINI_VID"]:
                $whereVariety = " and parentid = " . $GLOBALS["SHUINI_PID"];
                break;
            case $GLOBALS["JINSHU_VID"]:
                $whereVariety = " and parentid = " . $GLOBALS["JINSHU_PID"];
                break;
            case $GLOBALS["ZY_VID"]:
            default:
                $whereVariety = " and parentid != " . $GLOBALS["MEITAN_PID"] . " and parentid != " . $GLOBALS["JIAOTAN_PID"] . " and parentid != " . $GLOBALS["SHUINI_PID"] . " and parentid != " . $GLOBALS["JINSHU_PID"];
        }
        //Added by quanjw for jinshuzhipin end 2015/3/15
        if ($params['search'] != '') {
            $mid = $this->_dao->getOnes("select ID from sys_company where ( ComName  LIKE '%" . mysqli_real_escape_string($this->mysqli, $params['search']) . "%'   or ComNameShort  LIKE '%" . mysqli_real_escape_string($this->mysqli, $params['search']) . "%'  )     ");
            $mid = implode(",", $mid);
            if ($mid) {
                if (DZ_OR_YGW == 0){
                    $wherecg .= "  AND ( VarietyName like '%" . mysqli_real_escape_string($this->mysqli, $params['search']) . "%' or   MaterialCode like '%" . mysqli_real_escape_string($this->mysqli, $params['search']) . "%'   or    SpecCode like '%" . mysqli_real_escape_string($this->mysqli, $params['search']) . "%'  or sm_exc_sales.Mid  in ($mid))  ";
                    // $wherecj .= "  AND ( VarietyName like '%" . mysqli_real_escape_string($this->mysqli, $params['search']) . "%' or   MaterialCode like '%" . mysqli_real_escape_string($this->mysqli, $params['search']) . "%'   or    SpecCode like '%" . mysqli_real_escape_string($this->mysqli, $params['search']) . "%'  or Mid_Consignee  in ($mid) or Mid_Shipper  in ($mid))  ";
                }
            } else {
                if (DZ_OR_YGW == 0){
                    $wherecg .= "  AND ( VarietyName like '%" . mysqli_real_escape_string($this->mysqli, $params['search']) . "%' or   MaterialCode like '%" . mysqli_real_escape_string($this->mysqli, $params['search']) . "%'   or    SpecCode like '%" . mysqli_real_escape_string($this->mysqli, $params['search']) . "%'  or sm_exc_sales.Mid  =0 )  ";
                    // $wherecj .= "  AND ( VarietyName like '%" . mysqli_real_escape_string($this->mysqli, $params['search']) . "%' or   MaterialCode like '%" . mysqli_real_escape_string($this->mysqli, $params['search']) . "%'   or    SpecCode like '%" . mysqli_real_escape_string($this->mysqli, $params['search']) . "%'  or Mid_Consignee  =0 or Mid_Shipper =0 )  ";
                }
            }
            $str .= "&search=" . $params['search'];
        }


        if ($params['sort'] == "1") {
            $where2 = " ORDER BY SalesMinPrice DESC, ";
        }
        $str .= $this->getRequestStr("sort", $params['sort']);
        if ($params['sort'] == "2" || $params['sort'] == '') {
            $where2 = " ORDER BY sm_exc_sales.CreateDate DESC, ";
        }
        foreach ($search as $k => $v) {
            $str .= "&" . $k . "=" . $v;
        }
        $this->assign("str", $str);
        $this->assign("search", $search);
        $tjxz = $params['size'] != "" || $params['materialcode'] != "" || $params['variety'] || $params['city'] || $params['factory'] || $params['search'];

        $where_valid = " and SalesEndDate> '" . $nowtime . "'";

        if (DZ_OR_YGW == 0) { //大宗有这个功能
            //added by hzp started 2015/05/18
            //厂家
            $factory = $this->getNotNull($params['factory'], $params['factory1']);
            $wherecg .= $this->getwhere("OriginCode", $factory, LIKE);
            if ($factory != null)
                $this->setValueWithNotNull($search["factory"], $factory);

            //added by hzp ended 2015/05/18
            //城市
            //added by hzp started 2015/05/19
            $city = $this->getNotNull($params['city'], $params['city1']);
            $wherecg .= $this->getwhere("PickUpCity", $city, LIKE);
            if ($city != null)
                $this->setValueWithNotNull($search["city"], $city);
            //added by hzp ended 2015/05/19

            //品种
            $variety = $this->getNotNull($params['variety'], $params['variety1']);
            $wherecg .= $this->getwhere("pm_parentid", $variety, EQUAL);
            if ($variety != null)
                $this->setValueWithNotNull($search["variety"], $variety);
            $str .= $this->getRequestStr("variety2", $variety);


            //材质
            $materialcode = $this->getNotNull($params['materialcode'], $params['materialcode1']);
            $wherecg .= $this->getwhere("MaterialCode", $materialcode, LIKE);
            if ($materialcode != null)
                $this->setValueWithNotNull($search["materialcode"], $materialcode);

            //规格
            $size = $this->getNotNull($params['size'], $params['size1']);
            $wherecg .= $this->getwhere("SpecCode", $size, LIKE);
            if ($size != null)
                $this->setValueWithNotNull($search["size"], $size);


            //厂家
            // $factory = $this->_dao->Aquery("select MAX(sm_exc_sales_details.ID) AS dID,OriginCode from sm_exc_sales_details,sm_exc_sales,sys_company,sm_sys_storecustom where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and OriginCode!=''  $wherecg $where_valid GROUP BY OriginCode ORDER BY dID DESC limit 8");
            // $this->assign("factory", $factory);
            $factory = "select MAX(sm_exc_sales_details.ID) AS dID,OriginCode from sm_exc_sales_details,sm_exc_sales where sm_exc_sales_details.Pid=sm_exc_sales.ID  and  sm_exc_sales.Status =2 and OriginCode!=''  $wherecg $where_valid GROUP BY OriginCode ORDER BY dID DESC limit 50";
            $result = $this->_dao->Aquery($factory, $interval);
            // $this->assign("factory2", $factory2);
            // $result = $this->_dao->Aquery($query);

            // 将结果分成两部分
            $factory = array_slice($result, 0, 8);  // 前8条记录
            $factory2 = array_slice($result, 8);    // 接下来的42条记录

            // 将结果赋值给模板
            $this->assign("factory", $factory);
            $this->assign("factory2", $factory2);

            //added by hzp started 2015-05-14
            //城市
            // $city = $this->_dao->Aquery("select MAX(sm_exc_sales_details.ID) AS dID,PickUpCity from sm_exc_sales_details,sm_exc_sales,sys_company,sm_sys_storecustom where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and PickUpCity!=''  $wherecg $where_valid GROUP BY PickUpCity ORDER BY dID DESC limit 8");
            // $this->assign("city", $city);

            // $city2 = $this->_dao->Aquery("select MAX(sm_exc_sales_details.ID) AS dID,PickUpCity from sm_exc_sales_details,sm_exc_sales,sys_company,sm_sys_storecustom where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and PickUpCity!='' $wherecg $where_valid GROUP BY PickUpCity ORDER BY dID DESC limit 8,50");
            // $this->assign("city2", $city2);
            //added by hzp ended 2015-05-18
            $city = "select MAX(sm_exc_sales_details.ID) AS dID,sm_exc_sales.PickUpCity from sm_exc_sales_details,sm_exc_sales where sm_exc_sales_details.Pid=sm_exc_sales.ID and sm_exc_sales.Status =2 and sm_exc_sales.PickUpCity!=''  $wherecg $where_valid GROUP BY sm_exc_sales.PickUpCity ORDER BY dID DESC limit 50";
            $result = $this->_dao->Aquery($city, $interval);
            // 将结果分成两部分
            $city = array_slice($result, 0, 8);  // 前8条记录
            $city2 = array_slice($result, 8);    // 接下来的42条记录

            // 将结果赋值给模板
            $this->assign("city", $city);
            $this->assign("city2", $city2);


            //Updated by quanjw for jinshuzhipin start 2015/3/15
            //品种选择
            $variety = array();
            $variety2 = array();
            // $variety11 = $this->_dao->query(" SELECT MAX(sm_exc_sales.ID) AS dID,pm_parentid FROM sm_exc_sales_details, sys_company,sm_exc_sales,sm_sys_storecustom WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID   AND sm_exc_sales.Status =2 and  pm_parentid!='' $wherecg $where_valid group by pm_parentid ORDER BY dID desc limit 11");

            // foreach ($variety11 as   $k =>  $tmp) {
            //     $kname = $this->_dao->getOne("SELECT kname FROM `biz_key` WHERE  id ='" . $tmp['pm_parentid'] . "' and ktype = 1 ");
            //     if ($kname != "") {
            //         $variety[$tmp['pm_parentid']] = $kname;
            //     }
            // }
            // $this->assign("variety", $variety);

            // $variety22 = $this->_dao->query(" SELECT MAX(sm_exc_sales.ID) AS dID,pm_parentid FROM sm_exc_sales_details, sys_company,sm_exc_sales,sm_sys_storecustom WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID   AND sm_exc_sales.Status =2 and  pm_parentid!='' $wherecg $where_valid group by pm_parentid ORDER BY dID desc limit 11,50");
            // foreach ($variety22 as   $k =>  $tmp) {
            //     $kname2 = $this->_dao->getOne("SELECT kname FROM `biz_key` WHERE  id ='" . $tmp['pm_parentid'] . "' and ktype = 1 ");
            //     if ($kname2 != "") {
            //         $variety2[$tmp['pm_parentid']] = $kname2;
            //     }
            // }
            // $this->assign("variety2", $variety2);
            $variety = "SELECT MAX(sm_exc_sales.ID) AS dID,pm_parentid FROM sm_exc_sales_details, sm_exc_sales WHERE  sm_exc_sales_details.Pid=sm_exc_sales.ID   AND sm_exc_sales.Status =2 and  pm_parentid!='' $wherecg $where_valid group by pm_parentid ORDER BY dID desc limit 50";
            $result = $this->_dao->Aquery($variety, $interval);
            $key_ids = array_unique(array_values($result)); // 获取所有唯一ID
            // print_r($ids);
            $result_arr = [];
            if (!empty($key_ids)) {
                // 构建单个查询获取所有kname
                $idList = implode("','", array_map('intval', $key_ids)); // 安全处理整数ID
                $query = "SELECT id, kname FROM `biz_key` WHERE id IN ('$idList') AND ktype = 1";
                $result_arr = $this->_dao->Aquery($query, 3600);
            }

            // print_r($result_arr);
            // 将结果分成两部分
            $variety = array_slice($result_arr, 0, 11);  // 前8条记录
            $variety2 = array_slice($result_arr, 11);    // 接下来的42条记录
            $this->assign("variety", $variety);
            $this->assign("variety2", $variety2);

            //updated by hzp started 2015/05/14

            //Updated by quanjw for jinshuzhipin end 2015/3/15
            //材质
            //added by hzp started 2015/05/14
            // $materialcode = $this->_dao->AQuery(" select MAX(sm_exc_sales_details.ID) AS dID, MaterialCode from sm_exc_sales_details,sm_exc_sales,sys_company,sm_sys_storecustom where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and MaterialCode!='' $wherecg $where_valid group by MaterialCode ORDER BY dID desc limit 11");
            // $this->assign("materialcode", $materialcode);

            // $materialcode2 = $this->_dao->AQuery("select MAX(sm_exc_sales_details.ID) AS dID, MaterialCode from sm_exc_sales_details,sm_exc_sales,sys_company,sm_sys_storecustom where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and MaterialCode!='' $wherecg $where_valid group by MaterialCode ORDER BY dID desc limit 11,50");
            // $this->assign("materialcode2", $materialcode2);

            $materialcode = "select MAX(sm_exc_sales_details.ID) AS dID, MaterialCode from sm_exc_sales_details,sm_exc_sales where sm_exc_sales_details.Pid=sm_exc_sales.ID and sm_exc_sales.Status =2 and MaterialCode!='' $wherecg $where_valid group by MaterialCode ORDER BY dID desc limit 50";
            $result = $this->_dao->Aquery($materialcode, $interval);
            // 将结果分成两部分
            $materialcode = array_slice($result, 0, 11);  // 前8条记录
            $materialcode2 = array_slice($result, 11);    // 接下来的42条记录

            $this->assign("materialcode", $materialcode);
            $this->assign("materialcode2", $materialcode2);
            //added by hzp ended 2015/05/14

            //规格
            //updated by hzp started 2015/05/19
            // $size = $this->_dao->Aquery("select MAX(sm_exc_sales_details.ID) AS dID,SpecCode from sm_exc_sales_details,sm_exc_sales,sys_company,sm_sys_storecustom where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and SpecCode!=''  $wherecg $where_valid  GROUP BY SpecCode ORDER BY dID DESC limit 8");
            // $this->assign("size", $size);

            // $size2 = $this->_dao->Aquery("select MAX(sm_exc_sales_details.ID) AS dID,SpecCode from sm_exc_sales_details,sm_exc_sales,sys_company,sm_sys_storecustom where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and SpecCode!='' $wherecg $where_valid GROUP BY SpecCode ORDER BY dID DESC limit 8,50");
            // $this->assign("size2", $size2);
            $size = "select MAX(sm_exc_sales_details.ID) AS dID,SpecCode from sm_exc_sales_details,sm_exc_sales where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sm_exc_sales.Status =2 and SpecCode!=''  $wherecg $where_valid  GROUP BY SpecCode ORDER BY dID DESC limit 50";
            $result = $this->_dao->Aquery($size, $interval);
            // 将结果分成两部分
            $size = array_slice($result, 0, 8);  // 前8条记录
            $size2 = array_slice($result, 8);    // 接下来的42条记录

            // 将结果赋值给模板
            $this->assign("size", $size);
            $this->assign("size2", $size2);

            //updated by hzp ended 2015/05/19

            //获取大品种 数目
            $bigpz = $this->_dao->AQuery("select ID,PzName from sys_big_pz  where Status = 1  order by Ssort", 3600);
            $this->assign("bigpz", $bigpz);
        }

        //全部
        //updated by tuxw for isec started 20160122
        if (DZ_OR_YGW == 0) {
            $totalalls = $this->_dao->getOne("SELECT COUNT(sm_exc_sales.ID) FROM sm_exc_sales,sm_exc_sales_details WHERE sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.Status =2   AND SalesEndDate >='" . $earlytime . "' $wherecg $where_valid ", $interval);
        } else {
            $totalalls = $this->_dao->getOne("SELECT count(sm_exc_sales_details.ID) FROM sm_exc_sales_details,sm_exc_sales, WHERE sm_exc_sales_details.Pid=sm_exc_sales.ID   AND sm_exc_sales.Status =2  AND SalesEndDate >='" . $earlytime . "' $wherecg $where_valid ", $interval);
        }
        //updated by tuxw for isec ended 20160122

        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 25;
        $start = ($page - 1) * $per;

        //updated by tuxw for isec started 20160202
        if (DZ_OR_YGW != 0) {
            $zy_date_restrict = " and sm_exc_sales.SalesEndDate>'" . $nowtime . "'";
        }

        // $pzzy = $this->_dao->query("SELECT sm_exc_sales_details.ID as sdid,sm_exc_sales_details.*,sm_exc_sales.*, sys_company.ID as ComID,sys_company.ComNameShort,sys_company.ComName,sys_adminuser.ARealName as LinkMan ,sys_adminuser.QQNum as QQNum,sys_company.ContactTel,sys_company.Address   FROM sm_exc_sales_details, sys_company,sm_exc_sales,sys_adminuser,sm_sys_storecustom  WHERE sys_adminuser.ComID = sys_company.ID  and sys_adminuser.IsMain=1 and sys_adminuser.Status=1 and sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.Status =2  $wherecg $zy_date_restrict  $where2  sm_exc_sales.id DESC LIMIT $per");

        $pzzy = $this->_dao->query("SELECT sm_exc_sales_details.ID as sdid,sm_exc_sales_details.*,sm_exc_sales.* FROM sm_exc_sales_details,sm_exc_sales WHERE sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.Status =2  $wherecg $zy_date_restrict  $where2  sm_exc_sales.id DESC LIMIT $per", $interval);
        // 查询公司信息并建立映射
        $companyMap = [];
        $companies = $this->_dao->query("
            SELECT ID as ComID, ComNameShort, ComName, ContactTel, Address
            FROM sys_company 
            WHERE ID IN ($ids)
        ", $interval);

        foreach ($companies as $company) {
            $companyMap[$company['ComID']] = $company;
        }

        $contactMap = [];
        $contacts = $this->_dao->query("
            SELECT ComID, ARealName as LinkMan, QQNum 
            FROM sys_adminuser 
            WHERE ComID IN ($ids) 
            AND IsMain = 1 
            AND Status = 1
        ", $interval);

        foreach ($contacts as $contact) {
            $contactMap[$contact['ComID']] = $contact;
        }

        // 2. 提取所有会员ID
        $pids = [];
        foreach ($pzzy as $order) {
            if (!in_array($order['Pid'], $pids)) {
                $pids[] = $order['Pid'];
            }
        }

        $pidsStr = implode(',', $pids);


        $salesedMap = [];
        if ($pidsStr != '')
            $sales = $this->_dao->query("
            select SID as Pid,sum(Num) as QuantitySalesed from sm_lock_sales where SID in($pidsStr) and Status=1   group by SID
        ", $interval);

        foreach ($sales as $sale) {
            $salesedMap[$sale['Pid']] = $sale;
        }
        // print_r($salesedMap);

        $ComNameShort = array();
        foreach ($pzzy  as &$tmp) {
            $mid = $tmp['Mid'];
            // 合并公司信息
            if (isset($companyMap[$mid])) {
                $tmp = array_merge($tmp, $companyMap[$mid]);
            }

            // 合并联系人信息
            if (isset($contactMap[$mid])) {
                $tmp = array_merge($tmp, $contactMap[$mid]);
            }

            $Pid = $tmp['Pid'];
            // 合并已售数量
            if (isset($salesedMap[$Pid])) {
                $tmp = array_merge($tmp, $salesedMap[$Pid]);
            }
            //资源购供图标
            $zy_flag = $this->zy_flag($tmp['SalesStartDate'], $tmp['SalesEndDate'], $tmp['SalesType']);
            $tmp['flag'] = $zy_flag['flag'];
            $tmp['salestp'] = $zy_flag['salestp'];

            //added by hezp for lock_sales zy started 2016/11/01
            // $tmp['QuantitySalesed'] = $this->get_lock_sales_num($tmp['QuantitySalesed'], $tmp['Pid'], $tmp['sdid']);
            //added by hezp for lock_sales zy ended 2016/11/01

            if (!isset($ComNameShort[$tmp['mid']])) {
                $ComNameShort[$tmp['mid']] = array('mid' => $tmp['mid'], 'ComNameShort' => $tmp['ComNameShort']);
            }
        }
        unset($tmp);
        // echo '<pre>';
        // print_r($pzzy);
        //added by tuxw for isec started 20160205
        if (DZ_OR_YGW != 0) {
            $this->assign("ComNameShort", $ComNameShort);
            $pzzy = $this->zy_format($pzzy);
        }
        //added by tuxw for isec ended 20160205
        $this->assign("pzzy", $pzzy);

        //all
        $totalall = ceil($totalalls / $per);
        if ($totalall < 1) {
            $totalall = 1;
        }
        $this->assign("totalall", $totalall);
        $this->assign("totalalls", $totalalls);

        $this->assign("page", $page);
        $pre = $page - 1 < 1 ? 1 : $page - 1;
        //all
        $nextall = $page + 1 > $totalall ? $totalall : $page + 1;

        $this->assign("pre", $pre);
        $this->assign("nextall", $nextall);
        $this->assign("params", $params);

        //===================================================
        $page2 = $params['page2'] == '' ? 1 : $params['page2'];
        $url2 = "bizorder.php";
        $per2 = 10;
        $start2 = ($page2 - 1) * $per2;
        unset($params['page2']);

        if (DZ_OR_YGW == 0) {
            //Updated by quanjw for meijiao start 2015/1/27
            $totalcj = $this->_dao->getOne("SELECT COUNT(*) as c  FROM sm_contract_transaction_detail as sctd,sm_contract_transaction as sct where sct.ID=sctd.TID  $wherecj  and  VarietyName!=''", $interval);
            //Updated by quanjw for meijiao end 2015/1/27

            //Updated by quanjw for meijiao start 2015/1/27
            $cjsql="SELECT SalesType,PickUpCity,Mid_Consignee,Mid_Shipper,Sid,VarietyName,OriginCode,SpecCode,MaterialCode,BuyQuantity,SalesPrice,sct.CreateDate as cjdate,Vid,strength,xincengWeight  from sm_contract_transaction_detail as sctd, sm_contract_transaction as sct where sct.ID=sctd.TID  $wherecj   and  VarietyName!=''   order by cjdate desc  limit $start2,$per2";
            $pzzycj = $this->_dao->query($cjsql, $interval);
            // 收集所有Sid和公司ID
            $sids = [];
            $companyIds = [];
            foreach ($pzzycj as $tmp) {
                $sids[] = $tmp['Sid'];
                $companyIds[] = $tmp['Mid_Shipper'];
                $companyIds[] = $tmp['Mid_Consignee'];
            }
            $sids = array_unique(array_filter($sids));
            $companyIds = array_unique(array_filter($companyIds));
            // 批量查询id (Dtid)
            $idMap = [];
            if (!empty($sids)) {
                $sidList = implode(',', array_map('intval', $sids));
                $query = $this->_dao->query("SELECT Sid, Dtid FROM sm_exc_dd WHERE Sid IN ($sidList)");
                foreach ($query as $row) {
                    $idMap[$row['Sid']] = $row['Dtid'];
                }
            }
            // 批量查询TradeType
            $tradeTypeMap = [];
            if (!empty($sids)) {
                $sidList = implode(',', array_map('intval', $sids));
                $query = $this->_dao->query("SELECT id, TradeType FROM sm_exc_sales WHERE id IN ($sidList)");
                foreach ($query as $row) {
                    $tradeTypeMap[$row['id']] = $row['TradeType'];
                }
            }

            // 批量查询公司信息
            $companyMap = [];
            if (!empty($companyIds)) {
                $companyList = implode(',', array_map('intval', $companyIds));
                $query = $this->_dao->query("SELECT id, ComNameShort, IsShowCj FROM sys_company WHERE id IN ($companyList)");
                foreach ($query as $row) {
                    $companyMap[$row['id']] = [
                        'ComNameShort' => $row['ComNameShort'],
                        'IsShowCj' => $row['IsShowCj']
                    ];
                }
            }

            //Updated by quanjw for meijiao end 2015/1/27
            foreach ($pzzycj as &$tmp) {
                $tmp['QuantitySales'] = $tmp['BuyQuantity'];
                $tmp['QuantitySalesed'] = $tmp['BuyQuantity'];
                $tmp['SalesMinPrice'] = $tmp['SalesPrice'];
                $tmp['Mid'] = $tmp['Mid_Shipper'];
                $tmp['Mid2'] = $tmp['Mid_Consignee'];
                // $tmp['id'] = $this->_dao->getOne("select Dtid from sm_exc_dd where sm_exc_dd.Sid = '" . $tmp['Sid'] . "' limit 1 ");
                // $tmp['TradeType'] = $this->_dao->getOne("select TradeType from sm_exc_sales where sm_exc_sales.id = '" . $tmp['Sid'] . "' limit 1 ", $interval);

                // $com1 = $this->_dao->get_sys_company($tmp['Mid_Shipper']);
                // $com2 = $this->_dao->get_sys_company($tmp['Mid_Consignee']);

                // $tmp['comname'] = $com1['ComNameShort'];
                // $tmp['comname2'] = $com2['ComNameShort'];
                // $tmp['cj1'] = $com2['IsShowCj'];
                // $tmp['cj2'] = $com1['IsShowCj'];
                // 从映射数组中获取值
                $tmp['id'] = $idMap[$tmp['Sid']] ?? null;
                $tmp['TradeType'] = $tradeTypeMap[$tmp['Sid']] ?? null;
                
                $com1 = $companyMap[$tmp['Mid_Shipper']] ?? null;
                $com2 = $companyMap[$tmp['Mid_Consignee']] ?? null;
                
                $tmp['comname'] = $com1['ComNameShort'] ?? '';
                $tmp['comname2'] = $com2['ComNameShort'] ?? '';
                $tmp['cj1'] = $com2['IsShowCj'] ?? 0;  // 注意原逻辑：cj1使用com2的值
                $tmp['cj2'] = $com1['IsShowCj'] ?? 0;  // 注意原逻辑：cj2使用com1的值
            }
            unset($tmp);
            // print_r($pzzycj);
            $this->assign("pzzycj", $pzzycj);
            $pagebarcj = $this->pagebar2($url2, $params, $per2, $page2, $totalcj, 0, 2);

            $this->assign("pagebarcj", $pagebarcj);
            //=========================================================
            //已结束
            $page3 = $params['page3'] == '' ? 1 : $params['page3'];
            $start3 = ($page3 - 1) * $per2;
            unset($params['page3']);

            $totaljs = $this->_dao->getOne("SELECT COUNT(sm_exc_sales.ID) FROM sm_exc_sales,sm_exc_sales_details  WHERE   sm_exc_sales_details.Pid=sm_exc_sales.ID AND SalesEndDate < '" . $nowtime . "' AND SalesEndDate >='" . $earlytime . "' and (sm_exc_sales.Status=2 or sm_exc_sales.Status =8 or sm_exc_sales.Status=9)  $wherecg", $interval);

            //, sys_company.ComNameShort,sys_company.ComName,sys_adminuser.ARealName as LinkMan,sys_adminuser.QQNum as QQNum ,sys_company.ContactTel,sys_company.Address
            $jslist = $this->_dao->query("SELECT DATE_FORMAT(sm_exc_sales.CreateDate,'%Y-%m-%d') as CreateDateOD,sm_exc_sales_details.*,sm_exc_sales.* FROM sm_exc_sales_details, sm_exc_sales WHERE sm_exc_sales_details.Pid=sm_exc_sales.ID AND SalesEndDate < '" . $nowtime . "'  AND SalesEndDate >='" . $earlytime . "'  and (sm_exc_sales.Status=2 or sm_exc_sales.Status =8 or sm_exc_sales.Status=9)   $wherecg order by SalesEndDate desc  limit $start3,$per2", $interval);
            // 提取所有会员ID
            $idsArr = [];
            $pids = [];
            foreach ($jslist as $order) {
                if (!in_array($order['Mid'], $idsArr)) {
                    $idsArr[] = $order['Mid'];
                }
                if (!in_array($order['Pid'], $pids)) {
                    $pids[] = $order['Pid'];
                }
            }
            $ids = implode(',', $idsArr);
            $pidsStr = implode(',', $pids);
            // 查询公司信息并建立映射
            $companyMap = [];
            if ($ids != '') {
            $companies = $this->_dao->query("
                SELECT ID as ComID, ComNameShort, ComName, ContactTel, Address
                FROM sys_company 
                WHERE ID IN ($ids)
                "); 
            }

            foreach ($companies as $company) {
                $companyMap[$company['ComID']] = $company;
            }	
            $contactMap = [];
            if ($ids != '') {
                $contacts = $this->_dao->query("
                    SELECT ComID, ARealName as LinkMan, QQNum 
                    FROM sys_adminuser 
                    WHERE ComID IN ($ids) 
                    AND IsMain = 1 
                    AND Status = 1
                ");
            }

            foreach ($contacts as $contact) {
                $contactMap[$contact['ComID']] = $contact;
            }

            $salesedMap = [];
            if ($pidsStr != '')
                $sales = $this->_dao->query("
                select SID as Pid,sum(Num) as QuantitySalesed from sm_lock_sales where SID in($pidsStr) and Status=1   group by SID
            ", $interval);

            foreach ($sales as $sale) {
                $salesedMap[$sale['Pid']] = $sale;
            }
            foreach ($jslist  as &$tmp) {
                $mid = $tmp['Mid'];
                // 合并公司信息
                if (isset($companyMap[$mid])) {
                    $tmp['ComNameShort'] = $companyMap[$mid]['ComNameShort'];
                    $tmp['ComName'] = $companyMap[$mid]['ComName'];
                    $tmp['ContactTel'] = $companyMap[$mid]['ContactTel'];
                    $tmp['Address'] = $companyMap[$mid]['Address'];
                }

                // 合并联系人信息
                if (isset($contactMap[$mid])) {
                    $tmp['LinkMan'] = $contactMap[$mid]['LinkMan'];
                    $tmp['QQNum'] = $contactMap[$mid]['QQNum'];
                }

                $Pid = $tmp['Pid'];
                // 合并已售数量
                if (isset($salesedMap[$Pid])) {
                    $tmp['QuantitySalesed'] = $salesedMap[$Pid]['QuantitySalesed'];
                }
                //资源购供图标
                $zy_flag = $this->zy_flag($tmp['SalesStartDate'], $tmp['SalesEndDate'], $tmp['SalesType']);
                $tmp['flag'] = $zy_flag['flag'];
                $tmp['salestp'] = $zy_flag['salestp'];
            }
            unset($tmp);
            $this->assign("jslist", $jslist);
            $pagebarjs = $this->pagebar2($url2, $params, $per2, $page3, $totaljs, 0, 3);

            $this->assign("pagebarjs", $pagebarjs);
        }

        // 头部成交量统计
        $this->getCJTongji();

        $this->assign("params", $params);
        // $this->setvars();
    }

    //网上现货  单条资源详细
    public function con_resource($params) {}
    //网上现货  打包资源详细
    public function con_resource_details($params) {}

    // Added for huadong gangshi(xianhuoxiaoshou_fenye)  by hxy started  2015/2/5
    public function ajaxgetindexinfo_hdgs($params)
    {

        $params['gcm'] = $GLOBALS['HDGSMID'];
        $mid = $_SESSION['SYS_COMPANYID'];

        if ($GLOBALS['HDGSMID'] != $_SESSION['SYS_COMPANYID']) {
            $hdrvs = " and HDGS_RESID_VIEW.Status=0 ";
        }

        $totalalls = $this->_dao->getOne("SELECT COUNT(sm_exc_sales.ID) FROM sm_exc_sales,sm_exc_sales_details,HDGS_RESID_VIEW  WHERE  sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.ID = HDGS_RESID_VIEW.ID AND sm_exc_sales.Status =2 and sm_exc_sales.SalesType in (1,5) and ((HDGS_RESID_VIEW.IsVirtual=1 {$hdrvs}) or (HDGS_RESID_VIEW.IsVirtual=0 and HDGS_RESID_VIEW.Status=2))",3600);

        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 20;
        $start = ($page - 1) * $per;

        $pzzy[0] = $this->_dao->query("SELECT sm_exc_sales.ID,sm_exc_sales.PickUpCity, sm_exc_sales_details.OriginCode, sm_exc_sales_details.QuantitySales, sm_exc_sales_details.QuantitySalesed, sm_exc_sales_details.SalesMinPrice, sm_exc_sales_details.SpecCode, sm_exc_sales_details.MaterialCode, sm_exc_sales_details.VarietyName, sm_exc_sales.Mid, sm_exc_sales.CreateDate, sm_exc_sales.SalesStartDate, sm_exc_sales.SalesEndDate, sm_exc_sales.SalesType   FROM sm_exc_sales_details,sm_exc_sales,HDGS_RESID_VIEW WHERE  sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.ID = HDGS_RESID_VIEW.ID AND sm_exc_sales.Status =2  and  sm_exc_sales.SalesType in (1,5)  and ((HDGS_RESID_VIEW.IsVirtual=1 {$hdrvs}) or (HDGS_RESID_VIEW.IsVirtual=0 and HDGS_RESID_VIEW.Status=2)) order by   sm_exc_sales.CreateDate DESC,sm_exc_sales.id DESC LIMIT $start,$per");

        foreach ($pzzy[0]  as &$tmp) {
            if ($tmp['Mid'] != $GLOBALS['HDGSMID'] && $mid != $GLOBALS['HDGSMID']) {
                $C1 = $this->_dao->getOne("SELECT C1 FROM sm_diff_price WHERE  Mid='" . $tmp['Mid'] . "' ");
                $tmp['SalesMinPrice_gs'] = $tmp['SalesMinPrice'] + $C1;
            } else if ($tmp['Mid'] != $GLOBALS['HDGSMID'] && $mid == $GLOBALS['HDGSMID']) {
                $C2 = $this->_dao->getOne("SELECT C2 FROM sm_diff_price WHERE  Mid='" . $tmp['Mid'] . "' ");
                $tmp['SalesMinPrice_gs'] = $tmp['SalesMinPrice'] - $C2;
            } else {
                $tmp['SalesMinPrice_gs'] = $tmp['SalesMinPrice'];
            }
            //资源购供图标
            $zy_flag = $this->zy_flag($tmp['SalesStartDate'], $tmp['SalesEndDate'], $tmp['SalesType']);
            $tmp['flag'] = $zy_flag['flag'];
            $tmp['salestp'] = $zy_flag['salestp'];
        }
        $html_str = '';
        for ($i = 0; $i < count($pzzy[0]); $i++) {
            $index = $i + 1;
            $html_str = $html_str . "<tr><td align=\"center\"><span id=\"jjxs1_xuanz_$index\" ><input type=\"checkbox\"  name=\"res_id[]\" type=\"checkbox\" value=\"{$pzzy[0][$i]['ID']}\"/></span></td>";
            $VarietyName = substr($pzzy[0][$i]['VarietyName'], 0, 12);
            $html_str = $html_str . "<td><span id=\"jjxs1_VarietyName_$index\" ><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\">$VarietyName</a></span></td>";
            $MaterialCode = substr($pzzy[0][$i]['MaterialCode'], 0, 6);
            $html_str = $html_str . "<td><span id=\"jjxs1_MaterialCode_$index\" ><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\">$MaterialCode</a></span></td>";
            $SpecCode = substr($pzzy[0][$i]['SpecCode'], 0, 10);
            $html_str = $html_str . "<td><span id=\"jjxs1_SpecCode_$index\" ><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\">$SpecCode</a></span></td>";
            if ($mid == $GLOBALS['HDGSMID']) {
                $html_str = $html_str . "<td><span id=\"jjxs1_SalesMinPrice_$index\" ><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\">";
                if ($pzzy[0][$i]['SalesMinPrice'] != '0.00' && $pzzy[0][$i]['SalesMinPrice'] != "") {
                    $html_str = $html_str . "{$pzzy[0][$i]['SalesMinPrice']}/{$pzzy[0][$i]['SalesMinPrice_gs']}</a></span></td>";
                } else {
                    $html_str = $html_str . "协议价</a></span></td>";
                };
            } else {
                $html_str = $html_str . "<td><span id=\"jjxs1_SalesMinPrice_$index\" ><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\">";
                if ($pzzy[0][$i]['SalesMinPrice'] != '0.00' && $pzzy[0][$i]['SalesMinPrice'] != "") {
                    $html_str = $html_str . "{$pzzy[0][$i]['SalesMinPrice_gs']}</a></span></td>";
                } else {
                    $html_str = $html_str . "协议价</a></span></td>";
                };
            }
            $html_str = $html_str . "<td align=\"center\"><span id=\"jjxs1_QuantitySales_$index\" ><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\">{$pzzy[0][$i]['QuantitySalesed']}/{$pzzy[0][$i]['QuantitySales']}</a></span></td>";
            $OriginCode = substr($pzzy[0][$i]['OriginCode'], 0, 12);
            $html_str = $html_str . "<td><span id=\"jjxs1_OriginCode_$index\" ><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\">$OriginCode</a></span></td>";
            $html_str = $html_str . "<td><span id=\"jjxs1_PickUpCity_$index\" ><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\">{$pzzy[0][$i]['PickUpCity']}</a></span></td>";
            $CreateDate = date("Y-m-d", strtotime($pzzy[0][$i]['CreateDate']));
            $html_str = $html_str . "<td><span id=\"jjxs1_CreateDate_$index\" ><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\">$CreateDate</a></span></td>";
            if ($pzzy[0][$i]['flag'] == 1 && $pzzy[0][$i]['salestp'] == 1) {
                $html_str = $html_str . "<td><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\"><img  src=\"images/buy.jpg\" style=\"border:0;\" ></a></td></tr>";
            }
            if ($pzzy[0][$i]['flag'] == 2 && $pzzy[0][$i]['salestp'] == 1) {
                $html_str = $html_str . "<td><img  src=\"images/buy2.jpg\" style=\"border:0;\" ></td></tr>";
            }
            if ($pzzy[0][$i]['flag'] == 1 && $pzzy[0][$i]['salestp'] == 2) {
                $html_str = $html_str . "<td><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\"><img  src=\"images/sales.jpg\" style=\"border:0;\" ></a></td></tr>";
            }
            if ($pzzy[0][$i]['flag'] == 2 && $pzzy[0][$i]['salestp'] == 2) {
                $html_str = $html_str . "<td><img  src=\"images/sales2.jpg\" style=\"border:0;\" >
                                        </td></tr>";
            }
        }
        $html_str = $html_str . "~" . $page;

        echo $html_str;
    }

    public function ajaxgetindexinfo_hdgs2($params)
    {

        $params['gcm'] = $GLOBALS['HDGSMID'];
        $mid = $_SESSION['SYS_COMPANYID'];

        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 20;
        $start = ($page - 1) * $per;

        if ($GLOBALS['HDGSMID'] != $_SESSION['SYS_COMPANYID']) {
            $hdrvs = " and HDGS_RESID_VIEW.Status=0 ";
        }

        $totalcgzy = $this->_dao->getOne("SELECT COUNT(sm_exc_sales.ID) FROM sm_exc_sales,sm_exc_sales_details,HDGS_RESID_VIEW WHERE  sm_exc_sales_details.Pid=sm_exc_sales.ID   AND sm_exc_sales.ID = HDGS_RESID_VIEW.ID AND sm_exc_sales.Status =2  and ((HDGS_RESID_VIEW.IsVirtual=1 {$hdrvs}) or (HDGS_RESID_VIEW.IsVirtual=0 and HDGS_RESID_VIEW.Status=2)) and  (sm_exc_sales.SalesType in (2,6) or ( sm_exc_sales.SalesType =8 and Isdxcg!=1)) $wherecg $where2 ",3600);

        $pzzy[0] = $this->_dao->query("SELECT sm_exc_sales.ID,sm_exc_sales.PickUpCity, sm_exc_sales_details.OriginCode, sm_exc_sales_details.QuantitySales, sm_exc_sales_details.QuantitySalesed, sm_exc_sales_details.SalesMinPrice, sm_exc_sales_details.SpecCode, sm_exc_sales_details.MaterialCode, sm_exc_sales_details.VarietyName, sm_exc_sales.Mid, sm_exc_sales.CreateDate, sm_exc_sales.SalesStartDate, sm_exc_sales.SalesEndDate, sm_exc_sales.SalesType   FROM sm_exc_sales_details,sm_exc_sales,HDGS_RESID_VIEW WHERE  sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.ID = HDGS_RESID_VIEW.ID  AND sm_exc_sales.Status in (2,8)  and ((HDGS_RESID_VIEW.IsVirtual=1 {$hdrvs}) or (HDGS_RESID_VIEW.IsVirtual=0 and HDGS_RESID_VIEW.Status=2)) and ( sm_exc_sales.SalesType =2 or  ( sm_exc_sales.SalesType =8 and Isdxcg!=1) ) order by   sm_exc_sales.CreateDate DESC,sm_exc_sales.id DESC LIMIT $start,$per");

        foreach ($pzzy[0]  as &$tmp) {

            if ($tmp['Mid'] != $GLOBALS['HDGSMID'] && $mid != $GLOBALS['HDGSMID']) {
                $C3 = $this->_dao->getOne("SELECT C3 FROM sm_diff_price WHERE  Mid='" . $tmp['Mid'] . "' ");
                $tmp['SalesMinPrice_gs'] = $tmp['SalesMinPrice'] - $C3;
            } else if ($tmp['Mid'] != $GLOBALS['HDGSMID'] && $mid == $GLOBALS['HDGSMID']) {
                $C4 = $this->_dao->getOne("SELECT C4 FROM sm_diff_price WHERE  Mid='" . $tmp['Mid'] . "' ");
                $tmp['SalesMinPrice_gs'] = $tmp['SalesMinPrice'] + $C4;
            } else {
                $tmp['SalesMinPrice_gs'] = $tmp['SalesMinPrice'];
            }
            if ($tmp['Status'] == "8") {
                $C = $this->_dao->getOne("SELECT Mid FROM sm_exc_dd WHERE  Sid='" . $tmp['ID'] . "' ");
                if ($C == $GLOBALS['HDGSMID'] && $mid != $GLOBALS['HDGSMID']) {
                    $tmp['QuantitySalesed'] = 0;
                }
            }

            //资源购供图标
            $zy_flag = $this->zy_flag($tmp['SalesStartDate'], $tmp['SalesEndDate'], $tmp['SalesType']);
            $tmp['flag'] = $zy_flag['flag'];
            $tmp['salestp'] = $zy_flag['salestp'];
        }

        $html_str = '';
        for ($i = 0; $i < count($pzzy[0]); $i++) {
            $index = $i + 1;
            $html_str = $html_str . "<tr><td align=\"center\"><span id=\"jjcg2_xuanz_$index\" ><input type=\"checkbox\"  name=\"res_id2[]\" type=\"checkbox\" value=\"{$pzzy[0][$i]['ID']}\"/></span></td>";
            $VarietyName = substr($pzzy[0][$i]['VarietyName'], 0, 12);
            $html_str = $html_str . "<td><span id=\"jjcg2_VarietyName_$index\" ><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\">$VarietyName</a></span></td>";
            $MaterialCode = substr($pzzy[0][$i]['MaterialCode'], 0, 6);
            $html_str = $html_str . "<td><span id=\"jjcg2_MaterialCode_$index\" ><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\">$MaterialCode</a></span></td>";
            $SpecCode = substr($pzzy[0][$i]['SpecCode'], 0, 10);
            $html_str = $html_str . "<td><span id=\"jjcg2_SpecCode_$index\" ><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\">$SpecCode</a></span></td>";
            if ($mid == $GLOBALS['HDGSMID']) {
                $html_str = $html_str . "<td><span id=\"jjcg2_SalesMinPrice_$index\" ><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\">";
                if ($pzzy[0][$i]['SalesMinPrice'] != '0.00' && $pzzy[0][$i]['SalesMinPrice'] != "") {
                    $html_str = $html_str . "{$pzzy[0][$i]['SalesMinPrice']}/{$pzzy[0][$i]['SalesMinPrice_gs']}</a></span></td>";
                } else {
                    $html_str = $html_str . "协议价</a></span></td>";
                };
            } else {
                $html_str = $html_str . "<td><span id=\"jjcg2_SalesMinPrice_$index\" ><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\">";
                if ($pzzy[0][$i]['SalesMinPrice'] != '0.00' && $pzzy[0][$i]['SalesMinPrice'] != "") {
                    $html_str = $html_str . "{$pzzy[0][$i]['SalesMinPrice_gs']}</a></span></td>";
                } else {
                    $html_str = $html_str . "协议价</a></span></td>";
                };
            }
            $html_str = $html_str . "<td align=\"center\"><span id=\"jjcg2_QuantitySales_$index\" ><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\">{$pzzy[0][$i]['QuantitySalesed']}/{$pzzy[0][$i]['QuantitySales']}</a></span></td>";
            $OriginCode = substr($pzzy[0][$i]['OriginCode'], 0, 12);
            $html_str = $html_str . "<td><span id=\"jjcg2_OriginCode_$index\" ><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\">$OriginCode</a></span></td>";
            $html_str = $html_str . "<td><span id=\"jjcg2_PickUpCity_$index\" ><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\">{$pzzy[0][$i]['PickUpCity']}</a></span></td>";
            $CreateDate = date("Y-m-d", strtotime($pzzy[0][$i]['CreateDate']));
            $html_str = $html_str . "<td><span id=\"jjcg2_CreateDate_$index\" ><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\">$CreateDate</a></span></td>";
            if ($pzzy[0][$i]['flag'] == 1 && $pzzy[0][$i]['salestp'] == 1) {
                $html_str = $html_str . "<td><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\"><img  src=\"images/buy.jpg\" style=\"border:0;\" ></a></td></tr>";
            }
            if ($pzzy[0][$i]['flag'] == 2 && $pzzy[0][$i]['salestp'] == 1) {
                $html_str = $html_str . "<td><img  src=\"images/buy2.jpg\" style=\"border:0;\" ></td></tr>";
            }
            if ($pzzy[0][$i]['flag'] == 1 && $pzzy[0][$i]['salestp'] == 2) {
                $html_str = $html_str . "<td><a href=\"index.php?view=jinjia_info_gs&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\"><img  src=\"images/sales.jpg\" style=\"border:0;\" ></a></td></tr>";
            }
            if ($pzzy[0][$i]['flag'] == 2 && $pzzy[0][$i]['salestp'] == 2) {
                $html_str = $html_str . "<td><img  src=\"images/sales2.jpg\" style=\"border:0;\" >
                                        </td></tr>";
            }
        }
        $html_str = $html_str . "~" . $page;
        echo $html_str;
    }
    //Added for huadong gangshi(xianhuocaigou_fenye)  by hxy ended  2015/2/6
    public function ajaxgetindexinfo($params)
    {
        // $this->_dao->_reader->adodb->debug = true;
        $params['comname'] =  URLdecode($params['comname']);
        $params['search'] =  URLdecode($params['search']);
        $params['city'] =  URLdecode($params['city']);
        $params['factory'] =  URLdecode($params['factory']);
        $params['variety'] =  URLdecode($params['variety2']);
        //added by hzp for ygys started 2015/09/17 
        $params['gc'] =  URLdecode($params['gc']);
        //added by hzp for ygys ended 2015/09/17
        //added by hzp for shangwutong started 2015/06/12
        $params['pm'] =  URLdecode($params['pm']);
        //added by hzp for shangwutong ended 2015/06/12
        //added by tuxw started 20151011
        if ($params['comtype'] != "") {
            $params['comtype'] =  URLdecode($params['comtype']);
        }
        //added by tuxw ended 20151011
        $where = $this->getwhere("sm_exc_sales.Mid", $params['gcm'], EQUAL);

        //$str .= "&sptype=".$params['sptype'];
        if ($params['sptype'] == "1" || $params['sptype'] == "10") {
            $where .= "and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom where CompanyType = '1')";
        }
        if ($params['sptype'] == "2" ||  $params['sptype'] == "9") {
            $where .= " and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom where CompanyType = '2') ";
        }
        if ($params['sptype'] == "3") {
            //$where .= $this->getwhere("sm_exc_sales.Mid", "153", EQUAL);
            $where .= " and sm_exc_sales.Mid in (153,2918,1) ";
        }
        if ($params['TradeType'] != "") {
            $where .= $this->getwhere("sm_exc_sales.TradeType", $params['TradeType'], EQUAL);
        }
        //added by tuxw started 20151011

        if ($params['comtype'] != "") {
            if ($params['comtype'] != "" && $params['comtype'] != "gczq_all" && $params['comtype'] != "sjzq_all") {
                //$where .= "	AND sys_company.ComType = '".$params['comtype']."'";	
                //updated by tuxw for isec started 20160122
                if (DZ_OR_YGW == 0) {
                    $where .= " and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom where CompanyType = '" . $params['comtype'] . "') ";
                    //$where .= " and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom where CompanyType = '".$params['comtype']."') ";
                } else {
                    $comtype2 = "";
                    if ($params['comtype'] == 1) {
                        $comtype2 = 3;
                    } elseif ($params['comtype'] == 2) {
                        $comtype2 = 1;
                    } elseif ($params['comtype'] == 5) {
                        $comtype2 = 8;
                    } elseif ($params['comtype'] == 6) {
                        $comtype2 = 20;
                    } elseif ($params['comtype'] == 7) {
                        $comtype2 = 11;
                    } elseif ($params['comtype'] == 8) {
                        $comtype2 = 24;
                    } elseif ($params['comtype'] == 3) {
                        $comtype2 = 4;
                    } elseif ($params['comtype'] == 9) {
                        $comtype2 = 1;
                    }
                    $where .= " and sm_exc_sales.Mid in (select id from sys_company where ComType = '" . $comtype2 . "' or ComType2 = '" . $comtype2 . "' or ComType3 = '" . $comtype2 . "') and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom) ";
                }
                //updated by tuxw for isec ended 20160122
            }
            if ($params['comtype'] == "gczq_all") {
                //updated by tuxw for isec started 20160122
                if (DZ_OR_YGW == 0) {
                    $where .= " and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom where CompanyType IN (1,2,5,6,7)) ";
                } else {
                    $where .= " and sm_exc_sales.Mid in (select id from sys_company where ComType IN (3,1,8,20,11,24,4) or ComType2 IN (3,1,8,20,11,24,4) or ComType3 IN (3,1,8,20,11,24,4)) and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom) ";
                }
                //updated by tuxw for isec ended 20160122
            }
            if ($params['comtype'] == "sjzq_all") {
                //updated by tuxw for isec started 20160122
                if (DZ_OR_YGW == 0) {
                    $where .= " and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom where CompanyType IN (3,8)) ";
                } else {
                    $where .= " and sm_exc_sales.Mid in (select id from sys_company where ComType IN (24,4,1) or ComType2 IN (24,4,1) or ComType3 IN (24,4,1)) and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom) ";
                }
                //updated by tuxw for isec ended 20160122
            }
            if ($params['comtype'] == 1 || $params['comtype'] == 2 || $params['comtype'] == 5 || $params['comtype'] == 6 || $params['comtype'] == 7 || $params['comtype'] == 'gczq_all') {
                $where .= " and SalesType=5 ";
            } else {
                $where .= " and SalesType=2 ";
            }
        }
        //added by tuxw ended 20151011
        if ($params['salestype'] == "1") {
            $where .= "	AND sm_exc_sales.SalesType in (1,5) ";
        } elseif ($params['salestype'] == "2") {
            $where .= "	AND sm_exc_sales.SalesType in (2,6) ";
        }
        //Updated by quanjw for jinshuzhipin start 2015/3/25
        //首页的快速搜索和ajax搜索不对应,现统一
        $str = '';
        if ($params['search'] != '') {
            $mid = $this->_dao->getOnes("select ID from sys_company where ( ComName  LIKE '%" . $params['search'] . "%'   or ComNameShort  LIKE '%" . $params['search'] . "%'  )     ");
            $mid = implode(",", $mid);
            if ($mid) {
                //Updated by hzp for fanye start 2015/05/06
                $where .= "and ( VarietyName like '%" . $params['search'] . "%' or   MaterialCode like '%" . $params['search'] . "%'   or    SpecCode like '%" . $params['search'] . "%'  or Mid  in ($mid))  ";
            } else {
                //Updated by hzp for fanye start 2015/05/06
                $where .= "and ( VarietyName like '%" . $params['search'] . "%' or   MaterialCode like '%" . $params['search'] . "%'   or    SpecCode like '%" . $params['search'] . "%'  or Mid  =0 )  ";
            }
            $str = "&search=" . $params['search'];
        }

        //Updated by quanjw for jinshuzhipin end 2015/3/25

        $where .= $this->getwhere("VarietyName", $params['pm'], LIKE);
        $where .= $this->getwhere("PickUpCity", $params['city'], LIKE);
        $where .= $this->getwhere("OriginCode", $params['gc'], LIKE);
        $where .= $this->getwhere("MaterialCode", $params['caizhi'], LIKE);
        $where .= $this->getwhere("SpecCode", $params['guige'], LIKE);
        $where .= $this->getwhere("jhck", $params['jhck'], LIKE);
        $where .= $this->getwhere("SalesMinPrice", $params['jg1'], GREATER_THAN);
        $where .= $this->getwhere("SalesMinPrice", $params['jg2'], LESS_THAN);

        $where .= $this->getwhere("hd", $params['hd1'], GREATER_THAN);
        $where .= $this->getwhere("hd", $params['hd2'], LESS_THAN);

        $where .= $this->getwhere("kd", $params['kd1'], GREATER_THAN);
        $where .= $this->getwhere("kd", $params['kd2'], LESS_THAN);

        $where .= $this->getwhere("cd", $params['cd1'], GREATER_THAN);
        $where .= $this->getwhere("cd", $params['cd2'], LESS_THAN);

        // $aa = URLdecode($params['comname']);
        if ($params['comname'] != "") {

            // $mid = $this->_dao->getOnes("select ID from sys_company where ( ComName   LIKE '%" . $params['comname'] . "%'   or ComNameShort   LIKE '%" . $params['comname'] . "%'  )     ");
            $sql = "SELECT ID FROM sys_company   WHERE ComName LIKE '?' OR ComNameShort LIKE '?'";

            $inputarr = [
                'comname' => '%' . $params['comname'] . '%',
                'comnameshort' => '%' . $params['comname'] . '%'
            ];
            $mid = $this->_dao->getOnes($sql, $inputarr);
            $mid = implode(",", $mid);
            if ($mid) {
                $where .= " and Mid  in ($mid) ";
            } else {
                $where .= " and Mid = 0 ";
            }
        }

        //厂家
        $factory = $this->getNotNull($params['factory'], $params['factory1']);
        $where .= $this->getwhere("OriginCode", $factory, LIKE);
        //城市
        $city = $this->getNotNull($params['city'], $params['city1']);
        $where .= $this->getwhere("PickUpCity", $city, LIKE);

        //品种
        $variety = $this->getNotNull($params['variety'], $params['variety1']);
        $where .= $this->getwhere("pm_parentid", $variety, EQUAL);

        //材质
        $materialcode = $this->getNotNull($params['materialcode'], $params['materialcode1']);
        $where .= $this->getwhere("MaterialCode", $materialcode, LIKE);

        //规格
        $size = $this->getNotNull($params['size'], $params['size1']);
        $where .= $this->getwhere("SpecCode", $size, LIKE);

        if ($params['sort'] == "1") {
            $where2 = " ORDER BY SalesMinPrice DESC,";
        }
        if ($params['sort'] == "2" || $params['sort'] == '') {
            $where2 = " ORDER BY sm_exc_sales.CreateDate DESC,";
        }

        if ($params['type'] != "") {

            if ($params['type'] == "2" || $params['type'] == "4" || $params['type'] == "6" || $params['type'] == "8") {
                if ($params['sptype'] == "5" || $params['sptype'] == "7") {
                    //Updated by hzp for 现货采购中工程物流采购也要加上去 start 2015/04/27
                    //$where .= " and  sm_exc_sales.SalesType =2 ";
                    $where .= " and  sm_exc_sales.SalesType in (2,8) and Isdxcg!=1 ";
                    //Updated by hzp for 现货采购中工程物流采购也要加上去 end 2015/04/27
                } elseif ($params['sptype'] == "8") {
                    $where .= " and  sm_exc_sales.SalesType =5 ";
                } elseif ($params['sptype'] == "6") {
                    $where .= " and  sm_exc_sales.SalesType =8 and Isdxcg!=1";
                }

                if ($params['d'] == "gccg") {
                    $where .= " and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom where CompanyType = '1')";
                } elseif ($params['d'] == "sjcg") {
                    $where .= " and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom where CompanyType = '2')";
                }
            } elseif ($params['type'] == "1" || $params['type'] == "3" || $params['type'] == "5" || $params['type'] == "7") {
                //updated by tuxw started 20160412

                if ($params['sptype'] == "6") {
                    $where .= " and Isdxcg!=1";
                } elseif ($params['sptype'] == "7") {
                    $where .= " and Isdxcg!=1 and sm_exc_sales.SalesType IN (2,8)";
                } elseif ($params['sptype'] == "8") {
                    $where .= " and sm_exc_sales.SalesType=5";
                } elseif ($params['sptype'] == "10") {
                    $where .= " and sm_exc_sales.SalesType=5";
                } elseif ($params['sptype'] == "9") {
                    $where .= " and Isdxcg!=1";
                }
                //updated by tuxw ended 20160412
                if ($params['d'] == "gcxs") {
                    $where .= " and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom where CompanyType = '1')";
                } elseif ($params['d'] == "sjxs") {
                    $where .= " and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom where CompanyType = '2')";
                }
            }
        }

        // Updated by quanjw for pzfl start 2015/10/21
        if (DZ_OR_YGW == '0') {
            if( $params['k'] == "undefined"){   
                $params['k']='';
            }
            if ($params['k'] == "" || $params['k'] == "0") {
                $fl = implode(",", $GLOBALS["TYPE_GC"]);
                $where .= " AND ( Vid  in( $fl) )";
                $str .= "&ZiyuType=" . $params['k'];
            } else {
                
                $where .= " AND sm_exc_sales_details.Vid = " . $params['k'];
                $str .= "&ZiyuType=" . $params['k'];
            }
            //$zy_date_restrict = " and Vid not in(select ID  from sys_big_pz  where Status = 1) ";
        } else {
            $zy_date_restrict = " and sm_exc_sales.SalesEndDate>'" . date("Y-m-d H:i:s", time()) . "' ";
        }
        // Updated by quanjw for pzfl end 2015/10/21

        //获取大品种 数目
        $bigpz = $this->_dao->AQuery("select ID,PzName from sys_big_pz  where Status = 1  order by Ssort", 3600);
        $this->assign("bigpz", $bigpz);
        $this->assign("params", $params);

        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 25;
        $start = ($page - 1) * $per;


        $total = $this->_dao->getOne("SELECT COUNT(sm_exc_sales.ID) FROM sm_exc_sales,sm_exc_sales_details, sys_company WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.Status =2  {$zy_date_restrict} $where $keyserach  ",3600);

        if ($params['vid'] == '1') {

            $data = $this->_dao->query("SELECT  sm_exc_sales_details.*,sm_exc_sales.*, sys_company.ComNameShort,sys_company.ComName,(select ARealName from sys_adminuser where ComID = sys_company.ID  and IsMain=1 and Status=1 ) as LinkMan ,(select QQNum from sys_adminuser where ComID = sys_company.ID  and IsMain=1 and Status=1 ) as QQNum,sys_company.ContactTel,sys_company.Address  FROM sm_exc_sales, sys_company,sm_exc_sales_details WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.Status =2 {$zy_date_restrict} $where   $where2 sm_exc_sales.id DESC LIMIT $start, $per", 0);
        } elseif ($params['vid'] == '2') {
            //	foreach($bigpz as $key=>$val) {
            $data = $this->_dao->query("SELECT  sm_exc_sales_details.ID as sdid,sm_exc_sales_details.*,sm_exc_sales.*, sys_company.ComNameShort,sys_company.ComName,(select ARealName from sys_adminuser where ComID = sys_company.ID  and IsMain=1 and Status=1 ) as LinkMan ,sys_company.ContactTel,sys_company.Address  FROM sm_exc_sales, sys_company,sm_exc_sales_details WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.Status =2 $where  and Vid = '" . $params['k'] . "' ORDER BY sm_exc_sales.CreateDate DESC, sm_exc_sales.id DESC LIMIT $start, $per", 0);
            //	}	
        } elseif ($params['vid'] == '3') { //其他

            $data = $this->_dao->query("SELECT sm_exc_sales_details.ID as sdid,sm_exc_sales_details.*,sm_exc_sales.*, sys_company.ComNameShort,sys_company.ComName,(select ARealName from sys_adminuser where ComID = sys_company.ID  and IsMain=1 and Status=1 ) as LinkMan ,sys_company.ContactTel,sys_company.Address  FROM sm_exc_sales, sys_company,sm_exc_sales_details WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.Status =2 $where {$zy_date_restrict} ORDER BY sm_exc_sales.CreateDate DESC, sm_exc_sales.id DESC LIMIT $start, $per", 0);
        }

        $totalpage = ceil($total / $per);
        //----------------组织数据开始
        $record = array();
        //added by tuxw for isec started 20160205
        if (DZ_OR_YGW != 0) $data = $this->zy_format($data);
        //added by tuxw for isec ended 20160205
        foreach ($data as $d) {
            //资源购供图标
            $zy_flag = $this->zy_flag($d['SalesStartDate'], $d['SalesEndDate'], $d['SalesType']);
            $d['flag'] = $zy_flag['flag'];
            $d['salestp'] = $zy_flag['salestp'];
            //Add by xiakang started 2015/03/20
            $d['ContactTel'] = hidtel($d['ContactTel'], $_SESSION['SYS_COMPANYID']);
            //Add by xiakang ended 2015/03/20
            //added by hezp for lock_sales zy started 2016/11/01
            $d['QuantitySalesed'] = $this->get_lock_sales_num($d['QuantitySalesed'], $d['Pid'], $d['sdid']);
            //added by hezp for lock_sales zy ended 2016/11/01

            $tmp =  array();
            $tmp[] = "<a href='listing.php?view=zylist&mid=" . $d['Mid'] . " ' target='_blank'>" . mb_substr($d['ComNameShort'], 0, 16, 'gb2312') . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['VarietyName'], 0, 8, 'gb2312') . "</a>";

            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['MaterialCode'], 0, 6, 'gb2312') . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['SpecCode'], 0, 8, 'gb2312') . "</a>";

            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['OriginCode'], 0, 6, 'gb2312') . "</a>";

            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['PickUpCity'], 0, 4, 'gb2312') . "</a>";
            if ($d['SalesMinPrice'] == '0.00' || $d['SalesMinPrice'] == ''  || $d['TradeType'] == '3') {
                $tmp[] = "协议价";
            } else {
                $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank' >" . floor($d['SalesMinPrice']) . "</a>";
            }
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank' >" . $d['QuantitySalesed'] . "/" . $d['QuantitySales'] . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank' >" . $d['QuantitySalesed'] . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank' >" . date("m/d", strtotime($d['CreateDate'])) . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank' >" . $GLOBALS['ZY_ALL_TYPES'][$d['TradeType']] . "</a>";

            $phone = PHONENUM;
            //Update by xiakang haomajiami ended 2015/08/08
            $tmp[] = "<span></span>
                        <dt>" . $d['ComName'] . "</dt>
                        <dd>
                            <p>公司地址：" . $d['Address'] . "</p>
                            <p>联系电话：" . $d['ContactTel'] . "</p>
                            <p>" . kefu . "" . $phone . "</p>
                        </dd> ";
            //Update by xiakang for haomajiami ended 2015/03/20

            $tmp[] = "<TABLE width=\"100%\" border=0 cellPadding=1 cellSpacing=1 bgcolor=\"#BFE1FF\">
                                      <TBODY>
                                      <TR bgColor=#FFFFFF >
                                        <TD colspan=\"2\" style=\"FONT-SIZE: 12px;line-height:24px;background-image:url(/images/company_cmanbg_new.gif);\" >
                                        <strong>" . $d['ComName'] . "</strong></TD>
                                      </TR>
                                      <TR bgColor=#FFFFFF>
                                        <TD colspan=\"2\"  style=\"FONT-SIZE: 12px; line-height:24px;\">
                                          　交货仓库：" . $d['jhck'] . "<br>
                                          　交货地址：" . $d['PickUpAddress'] . "<br></TD>
                                        </TR>
                                      </TBODY>
                                    </TABLE> ";

            $tmp[] = "<input name=\"res_id[]\" type=\"checkbox\" value=" . $d['ID'] . ">";

            if ($d['QQNum'] != "") {
                $xxxt = "<a href=\"//wpa.qq.com/msgrd?v=3&uin=" . $d['QQNum'] . "&site=qq&menu=yes\"><img src=\"/img/talk_qq.png\" /></a>";
            } else {

                $xxxt = "";
            }

            $tmp[] = "<a href=\"javascript:checkHasAndChatTo(" . $d['CreateUser'] . ")\"><img class=\"img_" . $d['CreateUser'] . "\" id=\"img_" . $d['CreateUser'] . "\" src=\"images/gmt4.gif\" style=\"border:0;\" ></a>" . $xxxt;
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "&talk=1' target='_blank'>留言</a>";

            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . date('m/d', strtotime($d['CreateDate'])) . "</a>";

            if ($d['flag'] == 1 && $d['salestp'] == 1) {
                $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'><img  src=\"/images/buy.jpg\" /></a>";
            } else if ($d['flag'] == 2 && $d['salestp'] == 1) {
                $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'><img  src=\"/images/buy2.jpg\" /></a>";
            } else if ($d['flag'] == 1 && $d['salestp'] == 2) {
                $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'><img  src=\"/images/sales.jpg\" /></a>";
            } else if ($d['flag'] == 2 && $d['salestp'] == 2) {
                $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'><img  src=\"/images/sales2.jpg\" /></a>";
            }
            //Added by quanjw for jinshuzhipin start 2015/3/22
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['cd'], 0, 6, 'gb2312') . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['strength'], 0, 6, 'gb2312') . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['xincengWeight'], 0, 6, 'gb2312') . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['yongtu'], 0, 6, 'gb2312') . "</a>";
            //Added by quanjw for jinshuzhipin end 2015/3/22
            $record[] = implode("|X|", $tmp);
        }
        $record = implode("|O|", $record);
        //总页, 当前页
        $others = $totalpage . "|A|" . $page;
        echo $params['type'] . "|T|" . $params['d'] . "|T|" . $record . "|H|" . $others;
        exit;
    }

    //added by hzp started 2015/05.22
    public function ajaxgetindexinfo_sjzq($params)
    {

        $params['comname'] =  URLdecode($params['comname']);
        $params['search'] =  URLdecode($params['search']);
        $params['city'] =  URLdecode($params['city']);
        $params['factory'] =  URLdecode($params['factory']);
        $params['variety'] =  URLdecode($params['variety2']);

        $where = "";
        $where .= $this->getwhere("sm_exc_sales.Mid", $params['gcm'], EQUAL);

        if ($params['sptype'] == "1" || $params['sptype'] == "10") {
            $where .= "and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom where CompanyType = '1')";
        }
        if ($params['sptype'] == "2" ||  $params['sptype'] == "9") {
            $where .= " and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom where CompanyType = '2')";
        }
        if ($params['sptype'] == "3") {
            //$where .= " and sm_exc_sales.Mid = 153";
            $where .= " and sm_exc_sales.Mid in (153,2918,1) ";
        }
        $where .= $this->getwhere("sm_exc_sales.TradeType", $params['TradeType'], EQUAL);

        if ($params['salestype'] == "1") {
            $where .= "	AND sm_exc_sales.SalesType in (1,5) ";
        } elseif ($params['salestype'] == "2") {
            $where .= "	AND sm_exc_sales.SalesType in (2,6) ";
        }

        if ($params['search'] != '') {
            $mid = $this->_dao->getOnes("select ID from sys_company where ( ComName  LIKE '%" . $params['search'] . "%'   or ComNameShort  LIKE '%" . $params['search'] . "%'  )     ");
            $mid = implode(",", $mid);
            if ($mid) {

                $where .= "and ( VarietyName like '%" . $params['search'] . "%' or   MaterialCode like '%" . $params['search'] . "%'   or    SpecCode like '%" . $params['search'] . "%'  or Mid  in ($mid))  ";
            } else {

                $where .= "and ( VarietyName like '%" . $params['search'] . "%' or   MaterialCode like '%" . $params['search'] . "%'   or    SpecCode like '%" . $params['search'] . "%'  or Mid  =0 )  ";
            }
            $str .= "&search=" . $params['search'];
        }

        $where .= $this->getwhere("VarietyName", $params['pm'], LIKE);
        $where .= $this->getwhere("PickUpCity", $params['city'], LIKE);
        $where .= $this->getwhere("OriginCode", $params['gc'], LIKE);
        $where .= $this->getwhere("MaterialCode", $params['caizhi'], LIKE);
        $where .= $this->getwhere("SpecCode", $params['guige'], LIKE);
        $where .= $this->getwhere("jhck", $params['jhck'], LIKE);

        $where .= $this->getwhere("SalesMinPrice", $params['jg1'], GREATER_THAN);
        $where .= $this->getwhere("SalesMinPrice", $params['jg2'], LESS_THAN);

        $where .= $this->getwhere("hd", $params['hd1'], GREATER_THAN);
        $where .= $this->getwhere("hd", $params['hd2'], LESS_THAN);

        $where .= $this->getwhere("kd", $params['kd1'], GREATER_THAN);
        $where .= $this->getwhere("kd", $params['kd2'], LESS_THAN);

        $where .= $this->getwhere("cd", $params['cd1'], GREATER_THAN);
        $where .= $this->getwhere("cd", $params['cd2'], LESS_THAN);

        $aa = URLdecode($params['comname']);
        if ($params['comname'] != "") {

            $mid = $this->_dao->getOnes("select ID from sys_company where ( ComName   LIKE '%" . $params['comname'] . "%'   or ComNameShort   LIKE '%" . $params['comname'] . "%'  )     ");
            $mid = implode(",", $mid);
            if ($mid) {
                $where .= " and Mid  in ($mid) ";
            } else {
                $where .= " and Mid = 0 ";
            }
        }

        //厂家
        $factory = $this->getNotNull($params['factory'], $params['factory1']);
        $where .= $this->getwhere("OriginCode", $factory, LIKE);
        //城市
        $city = $this->getNotNull($params['city'], $params['city1']);
        $where .= $this->getwhere("PickUpCity", $city, LIKE);

        //品种
        $variety = $this->getNotNull($params['variety'], $params['variety1']);
        $where .= $this->getwhere("pm_parentid", $variety, EQUAL);

        //材质
        $materialcode = $this->getNotNull($params['materialcode'], $params['materialcode1']);
        $where .= $this->getwhere("MaterialCode", $materialcode, LIKE);

        //规格
        $size = $this->getNotNull($params['size'], $params['size1']);
        $where .= $this->getwhere("SpecCode", $size, LIKE);


        if ($params['sort'] == "1") {
            $where2 = " ORDER BY SalesMinPrice DESC,";
        }
        if ($params['sort'] == "2" || $params['sort'] == '') {
            $where2 = " ORDER BY sm_exc_sales.CreateDate DESC,";
        }

        if ($params['type'] != "") {

            if ($params['type'] == "2" || $params['type'] == "4" || $params['type'] == "6" || $params['type'] == "8") {
                //added by hzp started 2015/05/22 
                if ($params['type'] == "2") {
                    $where .= " and  sm_exc_sales.SalesType =2 ";
                } else if ($params['type'] == "6") {
                    $where .= " and  sm_exc_sales.SalesType =8 and Isdxcg!=1";
                }
                //added by hzp ended 2015/05/22 
                if ($params['d'] == "gccg") {
                    $where .= " and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom where CompanyType = '1')";
                } elseif ($params['d'] == "sjcg") {
                    $where .= " and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom where CompanyType = '2')";
                }
            } elseif ($params['type'] == "1" || $params['type'] == "3" || $params['type'] == "5" || $params['type'] == "7") {
                if ($params['d'] == "gcxs") {
                    $where .= " and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom where CompanyType = '1')";
                } elseif ($params['d'] == "sjxs") {
                    $where .= " and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom where CompanyType = '2')";
                }
                //added by hzp started 2015/05/22 
                if ($params['type'] == "1") {
                    $where .= " and  sm_exc_sales.SalesType =5 ";
                }
                //added by hzp ended 2015/05/22 
            }
        }

        //获取大品种 数目
        $bigpz = $this->_dao->AQuery("select ID,PzName from sys_big_pz  where Status = 1  order by Ssort", 3600);
        $this->assign("bigpz", $bigpz);
        $this->assign("params", $params);

        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 25;
        $start = ($page - 1) * $per;

        $where .= " AND Vid != " . $GLOBALS["MEITAN_VID"] . " and Vid != " . $GLOBALS["JIAOTAN_VID"] . " and Vid != " . $GLOBALS["SHUINI_VID"] . " and Vid != " . $GLOBALS["JINSHU_VID"];
        $total = $this->_dao->getOne("SELECT COUNT(sm_exc_sales.ID) FROM sm_exc_sales,sm_exc_sales_details, sys_company WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.Status =2 $where $keyserach  ");
        $data = $this->_dao->query("SELECT  sm_exc_sales_details.ID as sdid,sm_exc_sales_details.*,sm_exc_sales.*, sys_company.ComNameShort,sys_company.ComName,(select ARealName from sys_adminuser where ComID = sys_company.ID  and IsMain=1 and Status=1 ) as LinkMan ,(select QQNum from sys_adminuser where ComID = sys_company.ID  and IsMain=1 and Status=1 ) as QQNum,sys_company.ContactTel,sys_company.Address  FROM sm_exc_sales, sys_company,sm_exc_sales_details WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.Status =2 $where   $where2 sm_exc_sales.id DESC LIMIT $start, $per",3600);

        $totalpage = ceil($total / $per);
        //----------------组织数据开始
        $record = array();
        foreach ($data as &$d) {
            //added by hezp for lock_sales zy started 2016/11/01
            $d['QuantitySalesed'] = $this->get_lock_sales_num($d['QuantitySalesed'], $d['Pid'], $d['sdid']);
            //added by hezp for lock_sales zy ended 2016/11/01
            //资源购供图标
            $zy_flag = $this->zy_flag($d['SalesStartDate'], $d['SalesEndDate'], $d['SalesType']);
            $d['flag'] = $zy_flag['flag'];
            $d['salestp'] = $zy_flag['salestp'];
            $d['ContactTel'] = hidtel($d['ContactTel'], $_SESSION['SYS_COMPANYID']);
        }
        if ($params['type'] == 2) {
            $s = 2;
        } else if ($params['type'] == 6) {
            $s = 3;
        }
        $html_str = '';
        for ($i = 0; $i < count($data); $i++) {
            $index = $i + 1;
            $html_str = $html_str . "<tr><td align=\"center\"><span id=\"jjcg2_xuanz_$index\" ><input type=\"checkbox\"  name=\"res_id" . $s . "[]\" type=\"checkbox\" value=\"{$data[$i]['ID']}\"/></span></td>";

            //$VarietyName=substr($data[$i]['VarietyName'],0,12);
            $html_str = $html_str . "<td><span id=\"jjcg2_VarietyName_$index\" ><a href=\"index.php?view=jinjia_info&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">{$data[$i]['VarietyName']}</a></span></td>";
            $MaterialCode = substr($data[$i]['MaterialCode'], 0, 6);
            $html_str = $html_str . "<td><span id=\"jjcg2_MaterialCode_$index\" ><a href=\"index.php?view=jinjia_info&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">{$MaterialCode}</a></span></td>";

            $SpecCode = substr($data[$i]['SpecCode'], 0, 6);
            $html_str = $html_str . "<td><span id=\"jjcg2_SpecCode_$index\" ><a href=\"index.php?view=jinjia_info&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">$SpecCode</a></span></td>";

            $html_str = $html_str . "<td><span id=\"jjcg2_SalesMinPrice_$index\" ><a href=\"index.php?view=jinjia_info&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">{$data[$i]['SalesMinPrice']}</a></span></td>";

            $html_str = $html_str . "<td align=\"center\"><span id=\"jjcg2_QuantitySales_$index\" ><a href=\"index.php?view=jinjia_info&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">{$data[$i]['QuantitySalesed']}/{$data[$i]['QuantitySales']}</a></span></td>";

            $OriginCode = substr($data[$i]['OriginCode'], 0, 12);
            $html_str = $html_str . "<td><span id=\"jjcg2_OriginCode_$index\" ><a href=\"index.php?view=jinjia_info&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">$OriginCode</a></span></td>";

            $html_str = $html_str . "<td><span id=\"jjcg2_PickUpCity_$index\" ><a href=\"index.php?view=jinjia_info&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">{$data[$i]['PickUpCity']}</a></span></td>";

            $CreateDate = date("Y-m-d", strtotime($data[$i]['CreateDate']));
            $html_str = $html_str . "<td><span id=\"jjcg2_CreateDate_$index\" ><a href=\"index.php?view=jinjia_info&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">$CreateDate</a></span></td>";

            if ($data[$i]['flag'] == 1 && $data[$i]['salestp'] == 1) {
                $html_str = $html_str . "<td><a href=\"index.php?view=jinjia_info&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\"><img  src=\"images/buy.jpg\" style=\"border:0;\" ></a></td></tr>";
            }
            if ($data[$i]['flag'] == 2 && $data[$i]['salestp'] == 1) {
                $html_str = $html_str . "<td><img  src=\"images/buy2.jpg\" style=\"border:0;\" ></td></tr>";
            }
            if ($data[$i]['flag'] == 1 && $data[$i]['salestp'] == 2) {
                $html_str = $html_str . "<td><a href=\"index.php?view=jinjia_info&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\"><img  src=\"images/sales.jpg\" style=\"border:0;\" ></a></td></tr>";
            }
            if ($data[$i]['flag'] == 2 && $data[$i]['salestp'] == 2) {
                $html_str = $html_str . "<td><img  src=\"images/sales2.jpg\" style=\"border:0;\" ></td></tr>";
            }
        }
        $html_str = $params['type'] . "~" . $html_str . "~" . $page;
        echo $html_str;
        exit;
    }
    //added by hzp ended 2015/05.22

    //added by hzp started 2016/2.1
    public function ajaxgetindexinfo_youfa($params)
    {

        $params['city'] =  URLdecode($params['city']);
        $params['factory'] =  URLdecode($params['factory']);
        $params['variety'] =  URLdecode($params['variety2']);

        $where .= $this->getwhere("sm_exc_sales.Mid", $params['gcm'], EQUAL);

        if ($params['TradeType'] != "") {
            $where .= "	AND sm_exc_sales.TradeType = '" . $params['TradeType'] . "'";
        }
        if ($params['salestype'] == "1") {
            $where .= "	AND sm_exc_sales.SalesType in (1,5) ";
        } elseif ($params['salestype'] == "2") {
            $where .= "	AND sm_exc_sales.SalesType in (2,6) ";
        }
        $where .= $this->getwhere("VarietyName", $params['pm'], LIKE);

        $where .= $this->getwhere("OriginCode", $params['gc'], LIKE);
        $where .= $this->getwhere("MaterialCode", $params['caizhi'], LIKE);
        $where .= $this->getwhere("SpecCode", $params['guige'], LIKE);
        $where .= $this->getwhere("jhck", $params['jhck'], LIKE);

        $where .= $this->getwhere("SalesMinPrice", $params['jg1'], GREATER_THAN);
        $where .= $this->getwhere("SalesMinPrice", $params['jg2'], LESS_THAN);

        $where .= $this->getwhere("hd", $params['hd1'], GREATER_THAN);
        $where .= $this->getwhere("hd", $params['hd2'], LESS_THAN);

        $where .= $this->getwhere("kd", $params['kd1'], GREATER_THAN);
        $where .= $this->getwhere("kd", $params['kd2'], LESS_THAN);

        $where .= $this->getwhere("cd", $params['cd1'], GREATER_THAN);
        $where .= $this->getwhere("cd", $params['cd2'], LESS_THAN);

        //厂家
        $where .= $this->getwhere("OriginCode", $params['factory'], LIKE);
        //城市
        $where .= $this->getwhere("PickUpCity", $params['city'], LIKE);
        //品种
        $where .= $this->getwhere("pm_parentid", $params['variety'], EQUAL);
        //材质
        $where .= $this->getwhere("MaterialCode", $params['materialcode'], LIKE);
        //规格
        $where .= $this->getwhere("SpecCode", $params['size'], LIKE);

        if ($params['sort'] == "1") {
            $where2 = " ORDER BY SalesMinPrice DESC,";
        }
        if ($params['sort'] == "2" || $params['sort'] == '') {
            $where2 = " ORDER BY sm_exc_sales.CreateDate DESC,";
        }

        $this->assign("params", $params);

        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 7;
        $start = ($page - 1) * $per;

        $total = $this->_dao->getOne("SELECT COUNT(sm_exc_sales.ID) FROM sm_exc_sales,sm_exc_sales_details, sys_company WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.Status =2 and sm_exc_sales.SalesType=5 $where ");
        $data = $this->_dao->query("SELECT  sm_exc_sales_details.ID as sdid,sm_exc_sales_details.*,sm_exc_sales.*, sys_company.ComNameShort,sys_company.ComName FROM sm_exc_sales, sys_company,sm_exc_sales_details WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.Status =2 and sm_exc_sales.SalesType=5 $where ORDER BY sm_exc_sales.CreateDate DESC, sm_exc_sales.id DESC LIMIT $start, $per",3600);

        $totalpage = ceil($total / $per);
        //----------------组织数据开始

        $nowtime = date('Y-m-d H:i:s');

        foreach ($data as &$d) {
            //added by hezp for lock_sales zy started 2016/11/01
            $d['QuantitySalesed'] = $this->get_lock_sales_num($d['QuantitySalesed'], $d['Pid'], $d['sdid']);
            //added by hezp for lock_sales zy ended 2016/11/01
            //资源购供图标
            $zy_flag = $this->zy_flag($d['SalesStartDate'], $d['SalesEndDate'], $d['SalesType']);
            $d['flag'] = $zy_flag['flag'];
            $d['salestp'] = $zy_flag['salestp'];
        }
        $html_str = '';
        for ($i = 0; $i < count($data); $i++) {
            $index = $i + 1;
            $html_str = $html_str . "<tr><td align=\"center\"><input type=\"checkbox\"  name=\"res_id" . $params['type'] . "[]\" type=\"checkbox\" value=\"{$data[$i]['ID']}\"/></td>";

            $bz = substr($data[$i]['bz'], 0, 10);
            $html_str = $html_str . "<td align=\"left\" style=\"padding-left:10px;\"><a href=\"index.php?view=jinjia_info&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">{$bz}</a></td>";

            $MaterialCode = substr($data[$i]['MaterialCode'], 0, 10);
            $html_str = $html_str . "<td align=\"left\" style=\"padding-left:10px;\"><a href=\"index.php?view=jinjia_info&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">{$MaterialCode}</a></td>";

            $SpecCode = substr($data[$i]['SpecCode'], 0, 20);
            $html_str = $html_str . "<td align=\"left\" style=\"padding-left:20px;\"><a href=\"index.php?view=jinjia_info&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">$SpecCode</a></td>";


            $html_str = $html_str . "<td><a href=\"index.php?view=jinjia_info&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">{$data[$i]['PickUpCity']}</a></td>";

            $html_str = $html_str . "<td><a href=\"index.php?view=jinjia_info&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">{$data[$i]['SalesMinPrice']}</a></td>";

            $html_str = $html_str . "<td align=\"center\"><a href=\"index.php?view=jinjia_info&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">{$data[$i]['QuantitySales']}</a></td>";



            $CreateDate = date("Y-m-d", strtotime($data[$i]['CreateDate']));
            $html_str = $html_str . "<td><span id=\"jjcg2_CreateDate_$index\" ><a href=\"index.php?view=jinjia_info&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">$CreateDate</a></span></td>";

            if ($data[$i]['flag'] == 1 && $data[$i]['salestp'] == 1) {
                $html_str = $html_str . "<td><a href=\"index.php?view=jinjia_info&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\"><img  src=\"images/buy.jpg\" style=\"border:0;\" ></a></td></tr>";
            }
            if ($data[$i]['flag'] == 2 && $data[$i]['salestp'] == 1) {
                $html_str = $html_str . "<td><img  src=\"images/buy2.jpg\" style=\"border:0;\" ></td></tr>";
            }
            if ($data[$i]['flag'] == 1 && $data[$i]['salestp'] == 2) {
                $html_str = $html_str . "<td><a href=\"index.php?view=jinjia_info&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\"><img  src=\"images/sales.jpg\" style=\"border:0;\" ></a></td></tr>";
            }
            if ($data[$i]['flag'] == 2 && $data[$i]['salestp'] == 2) {
                $html_str = $html_str . "<td><img  src=\"images/sales2.jpg\" style=\"border:0;\" >
										</td></tr>";
            }
        }
        $html_str = $params['type'] . "~" . $html_str . "~" . $page;

        echo $html_str;
        exit;
    }
    //added by hzp ended 2016/2.1

    //新增的  资源定制  贸易商资源  钢厂资源     下一页
    public function ajaxgetindexinfom($params)
    {

        $params['comname'] =  URLdecode($params['comname']);
        $params['search'] =  URLdecode($params['search']);
        $params['city'] =  URLdecode($params['city']);
        $params['factory'] =  URLdecode($params['factory']);
        $params['variety'] =  URLdecode($params['variety2']);
        //Added by quanjw for jinshuzhipin start 2015/3/20
        $params['vid'] =  URLdecode($params['vid']);
        //Added by quanjw for jinshuzhipin end 2015/3/20
        $where = " and SalesType in (1,5) ";
        //$wherecg = "and SalesType in (1,5)";
        $where .= $this->getwhere("sm_exc_sales.Mid", $params['gcm'], EQUAL);

        if ($params['search'] != '') {
            $searchsql = $this->getsqlbykeyword($params['search']);
            $where .= $searchsql;
            $str .= "&search=" . $params['search'];
        }

        $where .= $this->getwhere("VarietyName", $params['pm'], LIKE);
        $where .= $this->getwhere("PickUpCity", $params['city'], LIKE);
        $where .= $this->getwhere("OriginCode", $params['gc'], LIKE);
        $where .= $this->getwhere("MaterialCode", $params['caizhi'], LIKE);
        $where .= $this->getwhere("SpecCode", $params['guige'], LIKE);
        $where .= $this->getwhere("jhck", $params['jhck'], LIKE);

        $where .= $this->getwhere("SalesMinPrice", $params['jg1'], GREATER_THAN);
        $where .= $this->getwhere("SalesMinPrice", $params['jg2'], LESS_THAN);

        $where .= $this->getwhere("hd", $params['hd1'], GREATER_THAN);
        $where .= $this->getwhere("hd", $params['hd2'], LESS_THAN);

        $where .= $this->getwhere("kd", $params['kd1'], GREATER_THAN);
        $where .= $this->getwhere("kd", $params['kd2'], LESS_THAN);

        $where .= $this->getwhere("cd", $params['cd1'], GREATER_THAN);
        $where .= $this->getwhere("cd", $params['cd2'], LESS_THAN);

        $aa = URLdecode($params['comname']);
        if ($params['comname'] != "") {

            $mid = $this->_dao->getOnes("select ID from sys_company where ( ComName   LIKE '%" . $params['comname'] . "%'   or ComNameShort   LIKE '%" . $params['comname'] . "%'  )     ");
            $mid = implode(",", $mid);
            if ($mid) {
                $where .= " and Mid  in ($mid) ";
            } else {
                $where .= " and Mid = 0 ";
            }
        }

        //厂家
        $factory = $this->getNotNull($params['factory'], $params['factory1']);
        $where .= $this->getwhere("OriginCode", $factory, LIKE);
        //城市
        $city = $this->getNotNull($params['city'], $params['city1']);
        $where .= $this->getwhere("PickUpCity", $city, LIKE);

        //品种
        $variety = $this->getNotNull($params['variety'], $params['variety1']);
        $where .= $this->getwhere("pm_parentid", $variety, EQUAL);

        //材质
        $materialcode = $this->getNotNull($params['materialcode'], $params['materialcode1']);
        $where .= $this->getwhere("MaterialCode", $materialcode, LIKE);

        //规格
        $size = $this->getNotNull($params['size'], $params['size1']);
        $where .= $this->getwhere("SpecCode", $size, LIKE);

        if ($params['sort'] == "1") {
            $where2 = " ORDER BY SalesMinPrice DESC,";
        }
        if ($params['sort'] == "2" || $params['sort'] == '') {
            $where2 = " ORDER BY sm_exc_sales.CreateDate DESC,";
        }

        if ($params['type'] != "") {

            if ($params['type'] == "2" || $params['type'] == "4" || $params['type'] == "6" || $params['type'] == "8") {
                $where .= " and  sm_exc_sales.SalesType =2 ";
                if ($params['d'] == "gccg") {
                    $where .= " and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom where CompanyType = '1')";
                } elseif ($params['d'] == "sjcg") {
                    $where .= " and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom where CompanyType = '2')";
                }
            } elseif ($params['type'] == "1" || $params['type'] == "3" || $params['type'] == "5" || $params['type'] == "7") {
                $where .= " and  sm_exc_sales.SalesType in(1,5)";
            }
        }
        //资源分类
        //Added by quanjw for meijiao start 2015/1/23 资源分类: 钢材 煤炭 焦炭 水泥 金属制品
        //Updated by quanjw for jinshuzhipin start 2015/2/25 增加金属
        if ($params['vid'] != "") {
            $where .= " AND sm_exc_sales_details.Vid = " . $params['vid'] . " ";
        } else {
            $where .= " AND Vid != " . $GLOBALS["MEITAN_VID"] . " and Vid != " . $GLOBALS["JIAOTAN_VID"] . " and Vid != " . $GLOBALS["SHUINI_VID"] . " and Vid != " . $GLOBALS["JINSHU_VID"];
        }
        //Updated by quanjw for jinshuzhipin end 2015/2/25
        //Added by quanjw for meijiao end 2015/1/23

        //获取大品种 数目
        $bigpz = $this->_dao->AQuery("select ID,PzName from sys_big_pz  where Status = 1  order by Ssort", 3600);
        $this->assign("bigpz", $bigpz);

        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 25;
        $start = ($page - 1) * $per;

        if ($params['sptype'] == "1") {   //我的钢厂资源  下一页
            $total = $this->_dao->getOne("SELECT COUNT(sm_exc_sales.ID) FROM sm_exc_sales,sm_exc_sales_details, sys_company ,sys_user_mygys  WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID   and sys_company.ID=sys_user_mygys.ComID  and sys_user_mygys.MyID=" . $_SESSION['SYS_COMPANYID'] . " and sys_user_mygys.ComType=1 and sm_exc_sales.Status =2   $where $keyserach  ",3600);

            $data = $this->_dao->query("SELECT sm_exc_sales_details.*,sm_exc_sales.*, sys_company.ComNameShort,sys_company.ComName,      sys_company.ContactTel,sys_company.Address   FROM sm_exc_sales_details, (select QQNum from sys_adminuser where ComID = sys_company.ID  and IsMain=1 and Status=1 ) as QQNum, sys_company,sm_exc_sales,sys_user_mygys  WHERE sys_company.ID = sm_exc_sales.Mid   and sm_exc_sales_details.Pid=sm_exc_sales.ID   and sys_company.ID=sys_user_mygys.ComID 
                 and  sys_user_mygys.MyID=" . $_SESSION['SYS_COMPANYID'] . " and sys_user_mygys.ComType=1  and sm_exc_sales.Status =2   $where   $where2 sm_exc_sales.id DESC LIMIT $start, $per", 0);
        }
        if ($params['sptype'] == "2") {   //我的贸易商资源  下一页
            $total = $this->_dao->getOne("SELECT COUNT(sm_exc_sales.ID) FROM sm_exc_sales,sm_exc_sales_details, sys_company ,sys_user_mygys  WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID   and sys_company.ID=sys_user_mygys.ComID  and sys_user_mygys.MyID=" . $_SESSION['SYS_COMPANYID'] . " and sys_user_mygys.ComType=2 and sm_exc_sales.Status =2   $where $keyserach  ",3600);

            $data = $this->_dao->query("SELECT sm_exc_sales_details.*,sm_exc_sales.*, sys_company.ComNameShort,sys_company.ComName,      sys_company.ContactTel,sys_company.Address   FROM sm_exc_sales_details, sys_company,sm_exc_sales,sys_user_mygys  WHERE sys_company.ID = sm_exc_sales.Mid   and sm_exc_sales_details.Pid=sm_exc_sales.ID   and sys_company.ID=sys_user_mygys.ComID 
                 and  sys_user_mygys.MyID=" . $_SESSION['SYS_COMPANYID'] . " and sys_user_mygys.ComType=2  and sm_exc_sales.Status =2   $where   $where2  sm_exc_sales.id DESC LIMIT $start, $per", 0);
        }
        if ($params['sptype'] == "4") {    //我的定制资源  下一页
            $keywords = $this->_dao->query("select * from sys_user_zydz where ComID=" . $_SESSION['SYS_COMPANYID']);
            //$wherecg= " and SalesType in (1,5) ";
            //$where .= $wherecg;
            $sql = "";
            foreach ($keywords as $key) {

                $keywd = $key['VarietyName'];
                $arr = $this->getkeyid($keywd);
                $id = $arr["id"];
                //alert($keywd.":".$id);
                if ($id != "") {
                    $sql = $sql . "or  ( pm_id='" . $id . "' OR pm_parentid='" . $id . "' OR pm_ppid='" . $id . "' ) ";
                }
            }

            if (!empty($sql)) {
                $sql = " and (" . substr($sql, 3) . ")";
            } else {
                $sql = " and 1!=1";
            }
            // echo  $sql;			 
            // exit;

            $where .= $sql;

            $total = $this->_dao->getOne("SELECT COUNT(sm_exc_sales.ID) FROM sm_exc_sales,sm_exc_sales_details,sys_company  WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID  and  sm_exc_sales.Status =2  $where $keyserach ",3600);

            $data = $this->_dao->query("SELECT sm_exc_sales_details.*,sm_exc_sales.*, sys_company.ComNameShort,sys_company.ComName,      sys_company.ContactTel,sys_company.Address   FROM sm_exc_sales_details,sm_exc_sales ,sys_company
            WHERE sys_company.ID = sm_exc_sales.Mid   and sm_exc_sales_details.Pid=sm_exc_sales.ID and  
            sm_exc_sales.Status =2  $where   $where2  sm_exc_sales.id DESC LIMIT $start, $per", 0);
        }

        if ($params['sptype'] == "") {    //全部资源  下一页
            $total = $this->_dao->getOne("SELECT COUNT(sm_exc_sales_details.ID) FROM sm_exc_sales_details,sm_exc_sales, sys_company WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID  AND sm_exc_sales.Status =2 $where $keyserach  ");

            $data = $this->_dao->query("SELECT  sm_exc_sales_details.*,sm_exc_sales.*, sys_company.ComNameShort,sys_company.ComName,(select ARealName from sys_adminuser where ComID = sys_company.ID  and IsMain=1 and Status=1 ) as LinkMan ,sys_company.ContactTel,sys_company.Address  FROM sm_exc_sales, sys_company,sm_exc_sales_details WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.Status =2 $where   $where2 sm_exc_sales.id DESC LIMIT $start, $per", 0);
        }

        $totalpage = ceil($total / $per);
        //----------------组织数据开始
        $record = array();

        foreach ($data as $d) {

            //Update by xiakang for haomajiami started 2015/03/20
            $d['ContactTel'] = hidtel($d['ContactTel'], $_SESSION['SYS_COMPANYID']);
            //Update by xiakang for haomajiami ended 2015/03/20

            $tmp =  array();
            $tmp[] = "<a href='listing.php?view=zylist&mid=" . $d['Mid'] . " ' target='_blank'>" . $d['ComNameShort'] . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['VarietyName'], 0, 24) . "</a>";

            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['MaterialCode'], 0, 10) . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['SpecCode'], 0, 10) . "</a>";

            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . $d['OriginCode'] . "</a>";

            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . $d['PickUpCity'] . "</a>";
            if ($d['SalesMinPrice'] == '0.00' || $d['SalesMinPrice'] == '') {
                $tmp[] = "协议价";
            } else {
                $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank' >" . floor($d['SalesMinPrice']) . "</a>";
            }
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank' >" . $d['QuantitySales'] . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank' >" . $d['QuantitySalesed'] . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank' >" . date("m/d", strtotime($d['CreateDate'])) . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank' >" . $GLOBALS['ZY_ALL_TYPES'][$d['TradeType']] . "</a>";

            // //Update by xiakang for haomajiami started 2015/03/20
            $tmp[] = "<span></span>
					<dt>" . $d['ComName'] . "</dt>
					<dd>
						<p>公司地址：" . $d['Address'] . "</p>
						<p>联系电话：" . $d['ContactTel'] . "</p>
						<p>" . kefu . "" . PHONENUM . "</p>
					</dd> ";

            $tmp[] = "<TABLE width=\"100%\" border=0 cellPadding=1 cellSpacing=1 bgcolor=\"#BFE1FF\">
								  <TBODY>
								  <TR bgColor=#FFFFFF >
									<TD colspan=\"2\" style=\"FONT-SIZE: 12px;line-height:24px;background-image:url(/images/company_cmanbg_new.gif);\" >
									<strong>" . $d['ComName'] . "</strong></TD>
								  </TR>
								  <TR bgColor=#FFFFFF>
									<TD colspan=\"2\"  style=\"FONT-SIZE: 12px; line-height:24px;\">
									  　交货仓库：" . $d['jhck'] . "<br>
									  　交货地址：" . $d['PickUpAddress'] . "<br></TD>
									</TR>
								  </TBODY>
								</TABLE> ";

            $tmp[] = "<input name=\"res_id[]\" type=\"checkbox\" value=" . $d['ID'] . ">";

            if ($d['QQNum'] != "") {
                $xxxt = "<a href=\"//wpa.qq.com/msgrd?v=3&uin=" . $d['QQNum'] . "&site=qq&menu=yes\"><img src=\"/img/talk_qq.png\" target='_top'/></a>";
            } else {

                $xxxt = "";
            }

            $tmp[] = "<a href=\"javascript:checkHasAndChatTo(" . $d['CreateUser'] . ")\"><img class=\"img_" . $d['CreateUser'] . "\" id=\"img_" . $d['CreateUser'] . "\" src=\"images/gmt4.gif\" style=\"border:0;\" ></a>" . $xxxt;
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "&talk=1' target='_blank'>留言</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . date('Y-m-d', strtotime($d['CreateDate'])) . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . date('m-d', strtotime($d['CreateDate'])) . "</a>";

            //  $tmp[]=$d['SalesType'];
            $record[] = implode("|X|", $tmp);
        }
        $record = implode("|O|", $record);
        //总页, 当前页
        $others = $totalpage . "|A|" . $page;
        echo $params['type'] . "|T|" . $params['d'] . "|T|" . $record . "|H|" . $others;
        exit;
    }

    public function qxdd($params)
    {

        //print_r($params);exit;
        if ($params['ddtype'] == "2") {
            $zyd = $this->_dao->getRow("update sm_exc_dd_tag set Status =4 where ID = '" . $params['id'] . "'");
        } else {
            $zyd = $this->_dao->getRow("update sm_exc_dd_tag set Status =3 where ID = '" . $params['id'] . "'");
        }

        $this->xieZt(2, $params['id'], "取消订单");

        goURL("bizorder.php?view=myorder&type=" . $params['type']);
    }


    public function getsjd($Mid)
    {

        $sjd = $this->_dao->getRow("select * from sm_sys_storecustom where Mid = '" . $Mid . "' and IsTime = 1  limit 1", 0);

        if (empty($sjd)) {
            return "3";
        }

        if (_isholiday() && $sjd['IsWorkDay'] == "1") {
            return "1";
        } else {
            $dnow = date("H:i");
            if (((strtotime($dnow) > strtotime($sjd['Mshour'] . ":" . $sjd['Msfen'])) &&  (strtotime($dnow) < strtotime($sjd['Mehour'] . ":" . $sjd['Mefen']))) ||
                ((strtotime($dnow) > strtotime($sjd['Ashour'] . ":" . $sjd['Asfen'])) &&  (strtotime($dnow) < strtotime($sjd['Aehour'] . ":" . $sjd['Aefen'])))
            ) {
                return "0";
            } else {
                return "1";
            }
        }
    }

    public function jygg($params)
    {
        $btag = $params['btag'];
        $Sid = $params['sid'];

        $this->assign("category", $GLOBALS['category']);

        $zyl = $this->_dao->query("select * from sm_exc_dd,sm_exc_sales,sm_exc_sales_details where sm_exc_sales.ID=sm_exc_sales_details.Pid and sm_exc_dd.sdid= sm_exc_sales_details.ID and  Dtid='" . $Sid . "' ");
        $this->assign("zyl", $zyl);

        //Added by quanjw for jinshuzhipin start 2015/2/27
        //交易公告表格区分 煤炭 焦炭 水泥 金属 钢材
        foreach ($zyl as $key => $value) {
            switch ($value["Vid"]) {
                case $GLOBALS["MEITAN_VID"]:
                    $zyl_meitan[$key] = $value;
                    break;
                case $GLOBALS["JIAOTAN_VID"];
                    $zyl_jiaotan[$key] = $value;
                    break;
                case $GLOBALS["SHUINI_VID"];
                    $zyl_shuini[$key] = $value;
                    break;
                case $GLOBALS["JINSHU_VID"];
                    $zyl_jinshu[$key] = $value;
                    break;
                case $GLOBALS["TKS_VID"];
                    $zyl_tks[$key] = $value;
                    break;
                default:
                    $zyl_zy[$key] = $value;
            }
        }
        //非空
        if (!empty($zyl_meitan)) {
            $zyll[$GLOBALS["MEITAN_VID"]] = $zyl_meitan;
        }
        if (!empty($zyl_jiaotan)) {
            $zyll[$GLOBALS["JIAOTAN_VID"]] = $zyl_jiaotan;
        }
        if (!empty($zyl_shuini)) {
            $zyll[$GLOBALS["SHUINI_VID"]] = $zyl_shuini;
        }
        if (!empty($zyl_jinshu)) {
            $zyll[$GLOBALS["JINSHU_VID"]] = $zyl_jinshu;
        }
        if (!empty($zyl_tks)) {
            $zyll[$GLOBALS["TKS_VID"]] = $zyl_tks;
        }
        if (!empty($zyl_zy)) {
            $zyll[$GLOBALS["ZY_VID"]] = $zyl_zy;
        }

        $this->assign("zyll", $zyll);
        //Added by quanjw for jinshuzhipin end 2015/2/27

        $dealdate = $this->_dao->getOne("select CreateDate from sm_contract_transaction WHERE BID=" . $Sid . " ");
        $this->assign("dealdate", $dealdate);

        $comname = $this->_dao->getRow("SELECT ComName,ComTransType from sys_company where ID = (select Mid from sm_exc_dd_tag where ID=" . $Sid . ")");
        $this->assign("comname", $comname);

        if ($zyl[0]['Mid'] == "153") {
            $adminuser = $this->_dao->getRow("select * from sys_adminuser where AUserName ='" . $zyl[0]['CreateUser'] . "'");
        } else {
            $adminuser = $this->_dao->getRow("select * from sys_adminuser where ID ='" . $zyl[0]['AdminUser1'] . "'");
        }

        $this->assign("adminuser", $adminuser);
        $this->assign("zy", $zydetail);

        $this->assign("params", $params);

        $this->assign("zysaletypes", $GLOBALS['ZY_ALL_TYPES']);
        $this->assign("danbaos", $GLOBALS['DANBAO_TYPES']);
        $this->assign("zytype", $GLOBALS['ZYTYPE']);

        $citys = $this->_dao->AQuery("SELECT cityid as CityCode, cityname as CityName FROM city Where 1=1 ORDER BY id");
        $this->assign("citys", $citys);

        $comp2 = $this->_dao->getRow("select IsShowCj  from sys_company where ID = '" . $zydetail['Mid'] . "'");
        $this->assign("comp2", $comp2);

        //显示成交价
        if ($comp2['IsShowCj'] == "2" || $comname['IsShowCj'] == "2") {
            $iscj = "1";
        }
        $this->assign("iscj", $iscj);
    }

    public function city($params)
    {
        $city = $this->_dao->AQuery("select cityid,cityname from city  where  	city_keys != ''  limit 30");
        $this->assign("city", $city);
    }

    public function reczylist($params)
    {

        //Added by quanjw for meijiao start 2015/7/29 煤焦
        $this->setvars();
        $this->assign("Vid", $params['Vid']);
        if ($params['Vid'] == $GLOBALS["MEITAN_VID"] || $params['Vid'] == $GLOBALS["JIAOTAN_VID"] || $params['Vid'] == $GLOBALS["SHUINI_VID"] || $params['Vid'] == $GLOBALS["JINSHU_VID"] || $params['Vid'] == $GLOBALS["HW_VID"]) {
            $where = " and Vid= " . $params['Vid'];
        } else {
            $where = " AND Vid != " . $GLOBALS["MEITAN_VID"] . " and Vid != " . $GLOBALS["JIAOTAN_VID"] . " and Vid != " . $GLOBALS["SHUINI_VID"] . " and Vid != " . $GLOBALS["JINSHU_VID"] . " and Vid != " . $GLOBALS["HW_VID"];
        }
        //Added by quanjw for meijiao start 2015/7/29	
        //资源推荐
        $citys = $this->_dao->AQuery("SELECT cityid as CityCode, cityname as CityName FROM city Where 1=1 ORDER BY id");
        $this->assign("citys", $citys);
        // Updated by quanjw for meijiao start 2015/7/30 增加Vid cd strength xincengWeight
        $zyrec = $this->_dao->query("select sm_exc_sales.*,sm_exc_sales_details.cd,sm_exc_sales_details.strength,sm_exc_sales_details.xincengWeight,sm_exc_sales_details.Vid,sm_exc_sales_details.VarietyName,sm_exc_sales_details.MaterialCode,sm_exc_sales_details.SpecCode,sm_exc_sales_details.SalesMinPrice,sm_exc_sales_details.yongtu from sm_exc_sales,sm_exc_sales_details where sm_exc_sales.ID= sm_exc_sales_details.Pid and IsRec=1" . $where . "  order by CreateDate DESC limit 10", 3600);
        //add by xiakang for ieschome started 2015/09/16  
        $iseczyrec = $this->_dao->query("select sm_exc_sales.*,sm_exc_sales_details.cd,sm_exc_sales_details.strength,sm_exc_sales_details.xincengWeight,sm_exc_sales_details.QuantitySales,sm_exc_sales_details.Vid,sm_exc_sales_details.VarietyName,sm_exc_sales_details.MaterialCode,sm_exc_sales_details.SpecCode,sm_exc_sales_details.SalesMinPrice,sm_exc_sales_details.OriginCode,sm_exc_sales_details.yongtu from sm_exc_sales,sm_exc_sales_details where sm_exc_sales.ID= sm_exc_sales_details.Pid and IsRec=1 order by CreateDate DESC limit 9", 3600);

        foreach ($iseczyrec as &$arr) {
            if ($arr['Vid'] == $GLOBALS["MEITAN_VID"]) {
                $arr['zyxinxi'] = $arr['MaterialCode'] . "|" . $arr['SpecCode'] . "|" . $arr['cd'];
            } else if ($arr['Vid'] == $GLOBALS["JIAOTAN_VID"]) {
                $arr['zyxinxi'] = $arr['SpecCode'] . "|" . $arr['MaterialCode'] . "|" . $arr['cd'];
            } else if ($arr['Vid'] == $GLOBALS["SHUINI_VID"]) {
                $arr['zyxinxi'] = $arr['SpecCode'];
            } else if ($arr['Vid'] == $GLOBALS["JINSHU_VID"]) {
                $arr['zyxinxi'] = $arr['SpecCode'] . "|" . $arr['strength'] . "|" . $arr['xincengWeight'];
            } else if ($arr['Vid'] == $GLOBALS["HW_VID"]) {
                $arr['zyxinxi'] = $arr['MaterialCode'] . "|" . $arr['SpecCode'] . "|" . $arr['yongtu'] . "|" . $arr['strength'];
            } else {
                $arr['zyxinxi'] = $arr['MaterialCode'] . "|" . $arr['SpecCode'];
            }
        }
        //add by xiakang for ieschome ended 2015/09/16
        $this->assign("zyrec", $zyrec);
        $this->assign("iseczyrec", $iseczyrec);
        //商家推荐

        $company = $this->_dao->query("select * from sys_company where IsTj = 1 order by px,FirstSignDate DESC limit 10", 0);
        //added by tuxw for isec started 20160125
        if (DZ_OR_YGW != 0) {
            $nowdate = date("Y-m-d H:i:s", time());
            //$company2 = $this->_dao->query("select distinct ComName from sm_exc_sales, sys_company where IsRec = 1 and sys_company.id=sm_exc_sales.mid order by RecDate DESC limit 10",0);
            $company2 = $this->_dao->query("select ComName, sum(QuantitySales-QuantitySalesed) Quantity, sys_company.ID Mid from sm_exc_sales, sys_company, sm_exc_sales_details 
			where IsRec = 1 and sys_company.id=sm_exc_sales.mid and sm_exc_sales.ID=sm_exc_sales_details.Pid and sm_exc_sales.status=2 and sm_exc_sales.SalesEndDate>'" . $nowdate . "'
			group by ComName order by RecDate DESC limit 10", 3600);
            //added by tuxw for isec ended 20160125
        }
        $this->assign("company", $company);
        //added by tuxw for isec started 20160125
        if (DZ_OR_YGW != 0)
            $this->assign("company2", $company2);
        //added by tuxw for isec ended 20160125		
    }
    // Added for huadong gangshi  by xk started  2014/12/25
    public function hdgszylist($params)
    {

        //华东钢市最新上架资源
        $citys = $this->_dao->AQuery("SELECT cityid as CityCode, cityname as CityName FROM city Where 1=1 ORDER BY id");
        $this->assign("citys", $citys);

        // Updated for huadong gangshi  by hzp  started  2015/03/16
        //(HDGS_RESID_VIEW.IsVirtual=1 and HDGS_RESID_VIEW.Status=0) 改为((HDGS_RESID_VIEW.IsVirtual=1 and HDGS_RESID_VIEW.Status=0) or (HDGS_RESID_VIEW.IsVirtual=0 and HDGS_RESID_VIEW.Status=2))
        $zyrec = $this->_dao->query("select sm_exc_sales.ID,sm_exc_sales.StoreCityCode,sm_exc_sales_details.VarietyName,sm_exc_sales_details.MaterialCode,sm_exc_sales_details.SpecCode,sm_exc_sales_details.SalesMinPrice,sm_exc_sales.SalesType,sm_exc_sales.Mid from sm_exc_sales,sm_exc_sales_details,HDGS_RESID_VIEW where sm_exc_sales.ID= sm_exc_sales_details.Pid AND sm_exc_sales.ID = HDGS_RESID_VIEW.ID  and ((HDGS_RESID_VIEW.IsVirtual=1 and HDGS_RESID_VIEW.Status=0) or (HDGS_RESID_VIEW.IsVirtual=0 and HDGS_RESID_VIEW.Status=2)) and sm_exc_sales.Status =2  order by CreateDate DESC limit 7");

        // Updated for huadong gangshi  by hzp  started  2015/03/16
        foreach ($zyrec as &$tmp) {
            if ($tmp['SalesType'] == "2") {
                $C3 = $this->_dao->getOne("SELECT C3 FROM sm_diff_price WHERE  Mid='" . $tmp['Mid'] . "' and  Pid='" . $GLOBALS['HDGSMID'] . "'");
                $tmp['SalesMinPrice'] = $tmp['SalesMinPrice'] - $C3;
            } else if ($tmp['SalesType'] == "5") {
                $C1 = $this->_dao->getOne("SELECT C1 FROM sm_diff_price WHERE  Mid='" . $tmp['Mid'] . "' and  Pid='" . $GLOBALS['HDGSMID'] . "' ");
                $tmp['SalesMinPrice'] = $tmp['SalesMinPrice'] + $C1;
            }
        }
        $this->assign("zyrec", $zyrec);
        //商家推荐
        $company = $this->_dao->query("select * from sys_company where IsTj = 1 order by px,FirstSignDate DESC limit 10", 0);
        $this->assign("company", $company);
    }

    // Added for huadong gangshi  by xk ended 2014/12/25
    public function reczy($params)
    {
        //add by xiakang started 2015/10/18
        $params['materialcode'] =  URLdecode($params['materialcode']);
        $params['size'] =  URLdecode($params['size']);
        //add by xiakang ended 2015/10/18
        $this->setvars();
        $wherecg = "";
        $str = "";
        //$wherecg = "and SalesType in (1,5)";
        //$wherecj = "and SalesType in (1,5)";
        $varietys = $this->_dao->Aquery(" SELECT id,kname FROM `biz_key` WHERE `ktype` = 1 ", 30 * 3600);

        $str .= $this->getRequestStr("pm", $params['pm']);
        $wherecg .= $this->getwhere("VarietyName", $params['pm'], LIKE);

        $str .= $this->getRequestStr("city", $params['city']);
        $wherecg .= $this->getwhere("PickUpCity", $params['city'], LIKE);

        $str .= $this->getRequestStr("gc", $params['gc']);
        $wherecg .= $this->getwhere("OriginCode", $params['gc'], LIKE);

        $str .= $this->getRequestStr("caizhi", $params['caizhi']);
        $wherecg .= $this->getwhere("MaterialCode", $params['caizhi'], LIKE);

        $str .= $this->getRequestStr("guige", $params['guige']);
        $wherecg .= $this->getwhere("SpecCode", $params['guige'], LIKE);

        $str .= $this->getRequestStr("pm", $params['jhck']);
        $wherecg .= $this->getwhere("jhck", $params['jhck'], LIKE);

        $str .= $this->getRequestStr("jg1", $params['jg1']);
        $str .= $this->getRequestStr("jg2", $params['jg2']);
        $wherecg .= $this->getwhere("SalesMinPrice", $params['jg1'], GREATER_THAN);
        $wherecg .= $this->getwhere("SalesMinPrice", $params['jg2'], LESS_THAN);

        $str .= $this->getRequestStr("hd1", $params['hd1']);
        $str .= $this->getRequestStr("hd2", $params['hd2']);
        $wherecg .= $this->getwhere("hd", $params['hd1'], GREATER_THAN);
        $wherecg .= $this->getwhere("hd", $params['hd2'], LESS_THAN);

        $str .= $this->getRequestStr("kd1", $params['kd1']);
        $str .= $this->getRequestStr("kd2", $params['kd2']);
        $wherecg .= $this->getwhere("kd", $params['kd1'], GREATER_THAN);
        $wherecg .= $this->getwhere("kd", $params['kd2'], LESS_THAN);

        $str .= $this->getRequestStr("cd1", $params['cd1']);
        $str .= $this->getRequestStr("cd2", $params['cd2']);
        $wherecg .= $this->getwhere("cd", $params['cd1'], GREATER_THAN);
        $wherecg .= $this->getwhere("cd", $params['cd2'], LESS_THAN);

        if ($params['comname'] != "") {
            $mid = $this->_dao->getOnes("select ID from sys_company where ( ComName  LIKE '%" . mysqli_real_escape_string($this->mysqli, $params['comname']) . "%'   or ComNameShort  LIKE '%" . mysqli_real_escape_string($this->mysqli, $params['comname']) . "%'  )     ");
            $mid = implode(",", $mid);
            if ($mid) {
                $wherecg .= "and  Mid  in ($mid)  ";
            } else {
                $wherecg .= "and  Mid  =0  ";
            }
            $str .= "&comname=$params[comname]";
        }

        if ($params['sort'] == "1") {
            $where2 = " ORDER BY SalesMinPrice ASC,";
            $str .= "&sort=" . $params['sort'];
        }
        if ($params['sort'] == "2" || $params['sort'] == '') {
            $where2 = " ORDER BY sm_exc_sales.CreateDate sm_exc_sales_details.zylevel DESC,";
            $str .= "&sort=" . $params['sort'];
        }

        $search = array();
        if ($params['sptype'] == "") {
            $str .= "&sptype=" . $params['sptype'];
        }

        if ($params['sptype'] == "3") {
            $wherecg .= "and sm_exc_sales.Mid = 1 ";
            $wherejl .= "and sm_exc_sales.Mid = 1";
            $wherecj = "and Mid_Shipper=1 or Mid_Consignee=1";
            $str .= "&sptype=" . $params['sptype'];
            $sptype = 3;
            $this->assign("page_title", "本站资源_");
        } else {
            $this->assign("page_title", "网上现货_");
        }

        $params['search'] = mysqli_real_escape_string($this->mysqli, $params['search']);

        if ($params['search'] != '') {
            $searchsql = $this->getsqlbykeyword($params['search']);
            $wherecg .= $searchsql;
            $str .= "&search=" . $params['search'];
        }

        //厂家
        $factory = $this->getNotNull($params['factory'], $params['factory1']);
        $wherecg .= $this->getwhere("OriginCode", $factory, LIKE);
        $this->setValueWithNotNull($search["factory"], $factory);
        //城市
        $city = $this->getNotNull($params['city'], $params['city1']);
        $wherecg .= $this->getwhere("PickUpCity", $city, LIKE);
        $this->setValueWithNotNull($search["city"], $city);

        //品种
        $variety = $this->getNotNull($params['variety'], $params['variety1']);
        $wherecg .= $this->getwhere("PickUpCity", $variety, LIKE);
        $this->setValueWithNotNull($search["variety"], $variety);

        //材质
        $materialcode = $this->getNotNull($params['materialcode'], $params['materialcode1']);
        $wherecg .= $this->getwhere("MaterialCode", $materialcode, LIKE);
        $this->setValueWithNotNull($search["materialcode"], $materialcode);

        //规格
        $size = $this->getNotNull($params['size'], $params['size1']);
        $wherecg .= $this->getwhere("SpecCode", $size, LIKE);
        $this->setValueWithNotNull($search["size"], $size);

        if ($params['sort'] == "1") {
            $where2 = " ORDER BY SalesMinPrice DESC,";
        }
        //$str .= "&sort=".$params['sort'];
        $str .= $this->getRequestStr("sort", $params['sort']);
        if ($params['sort'] == "2" || $params['sort'] == '') {
            $where2 = " ORDER BY sm_exc_sales.CreateDate DESC,";
        }

        //资源分类
        // Updated by quanjw for pzfl start 2015/10/21
        if ($params['ZiyuType'] != "" && $params['ZiyuType'] != "0") { //有分类 易钢云商都有分类
            if (DZ_OR_YGW != '0' &&  $params['ZiyuType'] == $GLOBALS["GC_VID"]) { // 除大宗钢材 == GC_VID + ZY_VID
                $fl = implode(",", $GLOBALS["TYPE_GC"]);
                $wherecg .= " AND ( Vid  in( $fl) )";
                $wherecj .= " AND ( Vid  in( $fl) )";
                $str .= "&ZiyuType=" . $params['ZiyuType'];
            } else { // 有固定VID分类
                $wherecg .= " AND sm_exc_sales_details.Vid = " . $params['ZiyuType'];
                $wherecj .= " AND Vid = " . $params['ZiyuType'];
                $str .= "&ZiyuType=" . $params['ZiyuType'];
            }
        } else { //大宗 钢材
            $fl = implode(",", $GLOBALS["TYPE_GC"]);
            $wherecg .= " AND ( Vid  in( $fl) )";
            $wherecj .= " AND ( Vid  in( $fl) )";
            $str .= "&ZiyuType=" . $params['ZiyuType'];
        }
        // Updated by quanjw for pzfl end 2015/10/21

        foreach ($search as $k => $v) {
            $str .= "&" . $k . "=" . $v;
        }
        $this->assign("str", $str);
        $this->assign("search", $search);

        $tjxz = $params['size'] != "" || $params['materialcode'] != "" || $params['variety'] || $params['city'] || $params['factory'] || $params['search'];

        //厂家
        $factory = $this->_dao->Aquery("select MAX(sm_exc_sales_details.ID) AS dID,OriginCode from sm_exc_sales_details,sm_exc_sales,sys_company where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and OriginCode!='' and  sm_exc_sales.IsRec=1 $wherecg GROUP BY OriginCode ORDER BY dID DESC limit 8");

        $this->assign("factory", $factory);
        $factory2 = $this->_dao->Aquery("select MAX(sm_exc_sales_details.ID) AS dID,OriginCode from sm_exc_sales_details,sm_exc_sales,sys_company where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and OriginCode!='' and  sm_exc_sales.IsRec=1 $wherecg GROUP BY OriginCode ORDER BY dID DESC limit 8,50");
        $this->assign("factory2", $factory2);

        //城市
        $city = $this->_dao->Aquery(" select  MAX(sm_exc_sales.ID) AS dID,PickUpCity  from sm_exc_sales_details,sm_exc_sales,sys_company where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and  PickUpCity!='' and  sm_exc_sales.IsRec=1 $wherecg group by PickUpCity ORDER BY dID DESC limit 11", 20 * 3600);
        $this->assign("city", $city);
        $city2 = $this->_dao->Aquery(" select  MAX(sm_exc_sales.ID) AS dID,PickUpCity  from sm_exc_sales_details,sm_exc_sales,sys_company where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and  PickUpCity!='' and  sm_exc_sales.IsRec=1 $wherecg group by PickUpCity ORDER BY dID DESC limit 11,50", 20 * 3600);
        $this->assign("city2", $city2);

        //品种
        $variety = array();
        $variety2 = array();
        $variety11 = $this->_dao->query("  SELECT MAX(sm_exc_sales.ID) AS dID,pm_parentid FROM sm_exc_sales_details, sys_company,sm_exc_sales WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID   AND sm_exc_sales.Status =2 and  pm_parentid!='' and  sm_exc_sales.IsRec=1 $wherecg group by pm_parentid ORDER BY dID desc limit 11", 20 * 3600);
        foreach ($variety11 as   $k =>  $tmp) {
            $kname = $this->_dao->getOne("SELECT kname FROM `biz_key` WHERE  id ='" . $tmp['pm_parentid'] . "' and ktype = 1 ");
            if ($kname != "") {
                $variety[$tmp['pm_parentid']] = $kname;
            }
        }
        $this->assign("variety", $variety);

        $variety22 = $this->_dao->query("  SELECT MAX(sm_exc_sales.ID) AS dID,pm_parentid FROM sm_exc_sales_details, sys_company,sm_exc_sales WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID   AND sm_exc_sales.Status =2 and  pm_parentid!='' and  sm_exc_sales.IsRec=1 $wherecg group by pm_parentid ORDER BY dID desc limit 11,50", 20 * 3600);
        foreach ($variety22 as   $k =>  $tmp) {
            $kname2 = $this->_dao->getOne("SELECT kname FROM `biz_key` WHERE  id ='" . $tmp['pm_parentid'] . "' and ktype = 1 ");
            if ($kname2 != "") {
                $variety2[$tmp['pm_parentid']] = $kname2;
            }
        }
        $this->assign("variety2", $variety2);

        //材质
        $materialcode = $this->_dao->AQuery(" select  MAX(sm_exc_sales_details.ID) AS dID, MaterialCode from sm_exc_sales_details,sm_exc_sales,sys_company where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and MaterialCode!='' and  sm_exc_sales.IsRec=1 $wherecg group by MaterialCode  ORDER BY dID desc limit 11", 20 * 3600);
        $this->assign("materialcode", $materialcode);
        $materialcode2 = $this->_dao->AQuery(" select  MAX(sm_exc_sales_details.ID) AS dID, MaterialCode from sm_exc_sales_details,sm_exc_sales,sys_company where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and MaterialCode!='' and  sm_exc_sales.IsRec=1 $wherecg group by MaterialCode  ORDER BY dID desc limit 11,50", 20 * 3600);
        $this->assign("materialcode2", $materialcode2);

        //规格
        $size = $this->_dao->AQuery("select  MAX(sm_exc_sales_details.ID) AS dID, SpecCode from sm_exc_sales_details,sm_exc_sales,sys_company where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and SpecCode!='' and  sm_exc_sales.IsRec=1 $wherecg  group by 	SpecCode ORDER BY dID DESC limit 11", 20 * 3600);
        $this->assign("size", $size);
        $size2 = $this->_dao->AQuery("select  MAX(sm_exc_sales_details.ID) AS dID, SpecCode from sm_exc_sales_details,sm_exc_sales,sys_company where sm_exc_sales_details.Pid=sm_exc_sales.ID  and sys_company.ID=sm_exc_sales.Mid and sm_exc_sales.Status =2 and SpecCode!='' and  sm_exc_sales.IsRec=1 $wherecg group by 	SpecCode ORDER BY dID DESC limit 11,50", 20 * 3600);
        $this->assign("size2", $size2);

        //获取大品种 数目
        $bigpz = $this->_dao->AQuery("select ID,PzName from sys_big_pz  where Status = 1  order by Ssort", 3600);
        $this->assign("bigpz", $bigpz);

        //全部
        $totalalls = $this->_dao->getOne("SELECT COUNT(sm_exc_sales.ID) FROM sm_exc_sales,sm_exc_sales_details, sys_company WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID  and  sm_exc_sales.IsRec=1  AND sm_exc_sales.Status =2 $wherecg  ",3600);

        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 25;
        $start = ($page - 1) * $per;

        $pzzy[0] = $this->_dao->query("SELECT sm_exc_sales_details.*,sm_exc_sales.*, sys_company.ComNameShort,sys_company.ComName,(select ARealName from sys_adminuser where ComID = sys_company.ID  and IsMain=1 and Status=1 ) as LinkMan ,sys_company.ContactTel,sys_company.Address   FROM sm_exc_sales_details, sys_company,sm_exc_sales WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID and  sm_exc_sales.IsRec=1 AND sm_exc_sales.Status =2 $wherecg  $where2  sm_exc_sales.id DESC LIMIT $per", 0);
        // Added by quanjw for woyao start 2015/10/23
        foreach ($pzzy[0]  as &$tmp) {
            //资源购供图标
            $zy_flag = $this->zy_flag($tmp['SalesStartDate'], $tmp['SalesEndDate'], $tmp['SalesType']);
            $tmp['flag'] = $zy_flag['flag'];
            $tmp['salestp'] = $zy_flag['salestp'];
            unset($tmp);
        }
        // Added by quanjw for woyao end 2015/10/23

        $this->assign("pzzy", $pzzy);

        //all
        $totalall = ceil($totalalls / $per);
        if ($totalall < 1) {
            $totalall = 1;
        }
        $this->assign("totalall", $totalall);
        $this->assign("totalalls", $totalalls);

        $this->assign("page", $page);
        $pre = $page - 1 < 1 ? 1 : $page - 1;
        //all
        $nextall = $page + 1 > $totalall ? $totalall : $page + 1;

        $this->assign("pre", $pre);
        $this->assign("nextall", $nextall);
        $this->assign("params", $params);
        //===================================================

        $page2 = $params['page2'] == '' ? 1 : $params['page2'];
        $url2 = "bizorder.php";
        $per2 = 10;
        $start2 = ($page2 - 1) * $per2;
        unset($params['page2']);

        $totalcj = $this->_dao->getOne("SELECT COUNT(*) as c  FROM sm_contract_transaction_detail as sctd left join sm_contract_transaction as sct on sct.ID=sctd.TID  where 1  $wherecj  and  VarietyName!=''");

        $pzzycj = $this->_dao->query("SELECT SalesType,PickUpCity,Mid_Consignee,Mid_Shipper,Sid,VarietyName,OriginCode,SpecCode,MaterialCode,BuyQuantity,SalesPrice,sct.CreateDate as cjdate  from sm_contract_transaction_detail as sctd left join sm_contract_transaction as sct on sct.ID=sctd.TID  where  1  $wherecj   and  VarietyName!=''   order by cjdate desc  limit $start2,$per2");

        foreach ($pzzycj as &$tmp) {
            $tmp['QuantitySales'] = $tmp['BuyQuantity'];
            $tmp['QuantitySalesed'] = $tmp['BuyQuantity'];
            $tmp['SalesMinPrice'] = $tmp['SalesPrice'];
            $tmp['Mid'] = $tmp['Mid_Shipper'];
            $tmp['Mid2'] = $tmp['Mid_Consignee'];
            $tmp['id'] = $this->_dao->getOne("select Dtid from sm_exc_dd where sm_exc_dd.Sid = '" . $tmp['Sid'] . "' limit 1 ");

            $com1 = $this->_dao->get_sys_company($tmp['Mid_Shipper']);
            $com2 = $this->_dao->get_sys_company($tmp['Mid_Consignee']);

            $tmp['comname'] = $com1['ComNameShort'];
            $tmp['comname2'] = $com2['ComNameShort'];
            $tmp['cj1'] = $com2['IsShowCj'];
            $tmp['cj2'] = $com1['IsShowCj'];
        }
        $this->assign("pzzycj", $pzzycj);
        $pagebarcj = $this->pagebar2($url2, $params, $per2, $page2, $totalcj, 0, 2);
        $this->assign("pagebarcj", $pagebarcj);
        //=========================================================
    }

    //选择支付方式
    public function paytype($params)
    {

        $id = $params['payid'];
        $paytype = $params['PayType'];
        $fkxs = $params['fkxs'];
        $fkxss = $params['fkxss'];
        $dwname2 = $params['dwname2'];
        $dwname3 = $params['dwname3'];
        $fkxs2 = $params['fkxs2'];

        $array = array("PayType", "fkqx", "cdhp", "txcd", "fkxs", "kctime", "txl", "dwname", "danwei", "kpbank", "lybzj");
        foreach ($array as $a) {

            if ($params[$a] == "0" && $a == "fkxs") {
                $params[$a] = addslashes($fkxs2);
                $fkxs = addslashes($fkxs2);
            } elseif ($fkxss == "3" && $a == "dwname") {
                if ($fkxs == "9") {
                    $params[$a] = addslashes($dwname3);
                } else {
                    $params[$a] = addslashes($dwname2);
                }
            } else {
                $params[$a] = addslashes($params[$a]);
            }
            $data[] = $a . "='" . $params[$a] . "'";
        }
        $data = implode(",", $data);
        $contact = $this->_dao->getRow("select * from sm_order_transaction where ID = '" . $id . "'");
        $yfkje = $params['lybzj'] * $contact['TotalMoney'] / 100;

        if ($fkxs == "1") {
            $where .= ",PayStatus=1";
        } else {
            $where .= ",PayStatus=0";
        }

        //if($contact['PayType'] != $paytype || $contact['fkqx'] != $params['fkqx'] || $contact['txcd'] != $params['txcd'] || $contact['fkxs'] != $params['fkxs']){
        $this->_dao->execute("update sm_exc_dd_tag set IsChange = 1 where ID = '" . $contact['BID'] . "'");
        if ($contact['Mid_Shipper'] == $_SESSION['SYS_COMPANYID']) {
            $this->_dao->execute("update sm_order_transaction SET $data $where where ID = '" . $id . "'");
        }
        if ($contact['Mid_Consignee'] == $_SESSION['SYS_COMPANYID']) {
            $this->_dao->execute("update sm_order_transaction SET $data $where where ID = '" . $id . "'");
        }
        //}

        foreach ($array as $a) {
            if ($a == "PayType") {
                $datazy[] = $GLOBALS['PAYTYPE'][$params[$a]];
            } else if ($a == "fkxs") {
                $datazy[] = $GLOBALS['ZFKXS'][$params[$a]];
            } else if ($a == "txcd") {
                $datazy[] = $GLOBALS['HTSF'][$params[$a]];
            } else if ($a == "danwei") {
                $datazy[] = $GLOBALS['TXDANWEI'][$params[$a]];
            } else {
                $datazy[] =   $params[$a];
            }
        }
        $data2 = implode("|-|", $datazy);

        echo $data2 . "|-|" . $paytype . "|-|" . $fkxs . "|-|" . $fkxss . "|-|" . $GLOBALS['FKXS'][$fkxss] . "(" . $GLOBALS['ZFKXS'][$fkxs] . ")" . "|-|" . $GLOBALS['FKXS'][$fkxss] . "(" . $dwname3 . ")" . "|-|" . $yfkje;
    }

    //pdf合同相关条款字段
    public function httk($params)
    {
        $id = $params['tkid'];
        $params['yzf_Shipper'] = trim($params['yzf_Shipper'], ",");
        $params['yzf_Consignee'] = trim($params['yzf_Consignee'], ",");
        $contact = $this->_dao->getRow("select * from sm_order_transaction where ID = '" . $id . "'");

        if ($contact['Mid_Shipper'] == $_SESSION['SYS_COMPANYID']) {
            $array = array("qdtime", "qdaddress", "zlbz", "bzbz", "xgzs", "hwsl", "kjfp", "jhqx", "yqwyj", "yqqx", "fjtk", "ycfkwyj", "jhbd", "gcwlht", "DisputeSettlement", "DisputeSettlement_city", "IsShowBo", "summary", "ChengbanName", "yzf_Shipper", "yzf_Consignee");
        }
        if ($contact['Mid_Consignee'] == $_SESSION['SYS_COMPANYID']) {
            $array = array("qdtime", "qdaddress", "zlbz", "bzbz", "xgzs", "hwsl", "kjfp", "jhqx", "yqwyj", "yqqx", "fjtk", "ycfkwyj", "jhbd", "gcwlht", "DisputeSettlement", "DisputeSettlement_city", "IsShowSo", "summary", "ChengbanName", "yzf_Shipper", "yzf_Consignee");
        }

        $array2 = array("qdaddress", "zlbz", "bzbz", "xgzs", "hwsl", "kjfp", "jhqx", "yqwyj", "yqqx", "fjtk", "ycfkwyj", "jhbd");

        foreach ($array2 as $a2) {

            $params[$a2] = addslashes($params[$a2]);
            $data2[] = $a2 . "='" . $params[$a2] . "'";
        }

        $data2 = implode(",", $data2);
        foreach ($array as $a) {

            $params[$a] = addslashes($params[$a]);
            $data[] = $a . "='" . $params[$a] . "'";
        }

        $data = implode(",", $data);

        $this->_dao->execute("update sm_exc_dd_tag set IsChange = 1 where ID = '" . $contact['BID'] . "'");
        if ($contact['Mid_Shipper'] == $_SESSION['SYS_COMPANYID']) { //,IsConfirmBo=0,Status=0,IsshowBo;
            $this->_dao->execute("update sm_order_transaction SET $data,IsshowBo='" . $params['IsShow'] . "' where ID = '" . $id . "'");
            $this->sethtlog($contact['SalesType'], "卖方合同条款修改", $contact['ContractNo'], "卖方合同条款修改", $id);
        }
        if ($contact['Mid_Consignee'] == $_SESSION['SYS_COMPANYID']) { //,IsConfirmSo=0,Status=0,IsshowSo;
            $this->_dao->execute("update sm_order_transaction SET $data,IsshowSo='" . $params['IsShow'] . "' where ID = '" . $id . "'");
            $this->sethtlog($contact['SalesType'], "买方合同条款修改", $contact['ContractNo'], "买方合同条款修改", $id);
        }
        //------修改部分↓↓↓↓↓↓↓↓↓↓↓↓-----------------------------------------------------------------------------------------------
        $contact2 = $this->_dao->query("select * from sm_contract_terms where  MID = '" . $_SESSION['SYS_COMPANYID'] . "'");
        if ($contact2) {
            $this->_dao->execute("update sm_contract_terms SET $data2 where  MID = '" . $_SESSION['SYS_COMPANYID'] . "'");
        } else {
            $this->_dao->execute("insert into sm_contract_terms SET $data2,MID = '" . $_SESSION['SYS_COMPANYID'] . "'");
        }
        //------修改部分↑↑↑↑↑↑↑↑↑↑↑↑-----------------------------------------------------------------------------------------------
        foreach ($array as $a) {
            if ($a == "ysfy") {
                $datazy[] = $GLOBALS['HTSF'][$params[$a]];
            } else if ($a == "hwsl") {
                $datazy[] = $GLOBALS['HWSL'][$params[$a]];
            } else if ($a == "fkxs") {
                $datazy[] = $GLOBALS['FKXS'][$params[$a]];
            } else if ($a == "txcd") {
                $datazy[] = $GLOBALS['HTSF'][$params[$a]];
            } else if ($a == "TradeType") {
                $datazy[] = $GLOBALS['ZY_SALE_TYPES'][$params[$a]];
            } else if ($a == "IsShowBo") {
                $datazy[] = $params['IsShow'];
            } else if ($a == "IsShowSo") {
                $datazy[] = $params['IsShow'];
            } else if ($a == "yzf_Shipper") {
                $yzf_Shipper = explode(",", $params['yzf_Shipper']);
                $yzf_Shipper_arr = "";

                foreach ($GLOBALS['YZF'] as $key => $tmp) {
                    if (in_array($key, $yzf_Shipper)) {
                        $yzf_Shipper_arr .= '<label><input type="checkbox" disabled value="' . $key . '" checked >' . $tmp . '</label>';
                    } else {
                        $yzf_Shipper_arr .= '<label><input type="checkbox" disabled value="' . $key . '" >' . $tmp . '</label>';
                    }
                }
                $datazy[] = $yzf_Shipper_arr;
            } else if ($a == "yzf_Consignee") {
                $yzf_Consignee = explode(",", $params['yzf_Consignee']);
                $yzf_Consignee_arr = "";
                foreach ($GLOBALS['YZF'] as $key => $tmp) {
                    if (in_array($key, $yzf_Consignee)) {
                        $yzf_Consignee_arr .= '<label><input type="checkbox"  disabled value="' . $key . '" checked >' . $tmp . '</label>';
                    } else {
                        $yzf_Consignee_arr .= '<label><input type="checkbox"  disabled value="' . $key . '" >' . $tmp . '</label>';
                    }
                }
                $datazy[] = $yzf_Consignee_arr;
            } else {
                $datazy[] =   $params[$a];
            }
        }
        $data2 = implode("|-|", $datazy);
        echo $data2;
    }

    //add by xiakang started 2016/05/03
    public function scht($params)
    {
        $uploaddir = "uploadfile/scht";
        if (!file_exists($uploaddir)) {
            mkdir($uploaddir, 0777);
        }
        $contract_bd = "";
        //附件1
        $attTransPre = $_FILES['contract_bd'];

        if ($attTransPre['size'] != 0) {
            //文件大小不在指定范围
            if ($attTransPre['size'] > "4194304") {
                echo "<script>alert('上传合同文件不可大于4M!');window.history.back();</script>";
                exit;
            }
            if (!$this->is_safe($attTransPre['tmp_name'])) {
                echo "<script>alert('不要恶意伤害我!');window.history.back();</script>";
                exit;
            }
            $name =   explode('.',   $attTransPre['name']);
            $ext =   $name[count($name) - 1];
            $nam = $this->create_guid();
            $newname = $nam . "." . $ext;
            move_uploaded_file($attTransPre['tmp_name'], $uploaddir . "/" . $newname)  or die('文件上传失败');
            chmod($uploaddir . "/" . $newname, 0777);
            $attTransPrePath = $uploaddir . "/" . $newname;
            $contract_bd .= "contract_bd ='" . $attTransPrePath . "'";
        } else {
            $contract_bd = "";
            echo "<script>alert('请选择你需要上传的合同文件!');window.history.back();</script>";
            exit;
        }
        $contact = $this->_dao->getRow("select * from sm_order_transaction where ID = '" . $params['id'] . "'");
        if (file_exists($contact['contract_bd'])) {
            unlink($contact['contract_bd']);
        }
        $this->_dao->execute("update sm_exc_dd_tag set IsChange = 1 where ID = '" . $contact['BID'] . "'");
        $this->_dao->execute("UPDATE sm_order_transaction SET $contract_bd where ID = '" . $contact['ID'] . "'");
        echo "<script>alert('上传成功');window.history.back();</script>";
        exit;
    }
    //add by xiakang ended 2016/05/03

    //收货联系人地址

    public function shlxraddress($params)
    {

        $params['arrayed'] = array("ConsigneeAddressState", "ConsigneeAddressCity",  "ConsigneePostCode", "ConsigneeAddress");
        $params['sethtlog'] = "收货联系人地址修改";
        echo $this->sfhlxraddress($params);
        exit;
    }

    //发货联系人地址
    public function fhlxraddress($params)
    {
        $params['arrayed'] = array("ShipperAddressState", "ShipperAddressCity",  "ShipperPostCode", "ShipperAddress");
        $params['sethtlog'] = "发货联系人地址修改";
        echo $this->sfhlxraddress($params);
        exit;
    }

    //收发货联系人地址
    function sfhlxraddress($params)
    {

        $sm_id = $params['pid'];

        $arrayed = $params['arrayed'];
        foreach ($arrayed as $a) {

            $params[$a] = addslashes($params[$a]);
            $dataed[] = $a . "='" . $params[$a] . "'";
        }
        $dataed2 = implode(",", $dataed);
        $this->_dao->execute("update sm_order_transaction set $dataed2 where id = '" . $sm_id . "'");

        $llog = $this->_dao->getRow("select * from sm_contract_transaction where id = '" . $sm_id . "' limit 1");
        $this->sethtlog($llog['SalesType'], $params['sethtlog'], $llog['ContractNo'], $params['sethtlog'], $sm_id);
        //$this->xieZt(1,$sm_id,"修改了发货联系人信息");

        foreach ($arrayed as $a) {

            $params[$a] = addslashes($params[$a]);
            $datafh[] =  $params[$a];
        }

        $data2 = implode("|-|", $datafh);
        return $data2 . "|-|" . $sm_id;
    }

    function sfhlxr_demo_common($params)
    {

        $id = $params['sfhflag'];
        $sm_id = $params['usid'];

        $arrayed = $params['arrayed'];
        foreach ($arrayed as $a) {

            $params[$a] = addslashes($params[$a]);
            $dataed[] = $a . "='" . $params[$a] . "'";
        }
        $dataed2 = implode(",", $dataed);
        $ordinfo = $this->_dao->getRow("select BID," . $params['select_Ysfs'] . " from  sm_order_transaction  where ID = '" . $sm_id . "'");

        if ($ordinfo[$params['select_Ysfs']] == "0") {
            $this->_dao->execute("update sm_order_transaction set " . $params['select_Ysfs'] . "='" . $params[$params['update_Ysfs']] . "' where ID = '" . $sm_id . "'");
        }

        $this->_dao->execute("update sm_order_transaction set $dataed2 where ID = '" . $sm_id . "'");

        foreach ($arrayed as $a) {

            if ($a == $params['update_Ysfs']) {
                $params[$a] = $GLOBALS['YSFS'][$params[$a]];
            }
            $params[$a] = addslashes($params[$a]);
            $datafh[] =  $params[$a];
        }

        $data2 = implode("|-|", $datafh);
        return $data2 . "|-|" . $sm_id;
    }

    //收货联系人demo
    public function shlxr_demo($params)
    {
        $params['sfhflag'] = $params['shflag'];
        $params['select_Ysfs'] = "ShipperYsfs";
        $params['update_Ysfs'] = "ConsigneeYsfs";
        $params['arrayed'] = array("ConsigneeMan", "ConsigneePhone", "ConsigneeMobile", "ConsigneeFax", "ConsigneeEmail",  "ConsigneePostCode", "ConsigneeQQNum", "ConsigneeAddressState", "ConsigneeAddressCity", "ConsigneeAddress", "ConsigneeDhck", "ConsigneeYsfs");

        echo $this->sfhlxr_demo_common($params);
        exit;
    }

    //发货联系人demo
    public function fhlxr_demo($params)
    {

        $params['sfhflag'] = $params['fhflag'];
        $params['select_Ysfs'] = "ConsigneeYsfs";
        $params['update_Ysfs'] = "ShipperYsfs";
        $params['arrayed'] = array("ShipperMan", "ShipperPhone", "ShipperMobile", "ShipperFax", "ShipperEmail",  "ShipperPostCode", "ShipperQQNum", "ShipperAddressState", "ShipperAddressCity", "ShipperAddress", "ShipperFhck", "ShipperYsfs");

        echo $this->sfhlxr_demo_common($params);
        exit;
    }


    //交割信息
    public function jgxx($params)
    {

        $sm_id = $params['jghtid'];

        //$this->changed($sm_id);
        $arrayed = array("PickUpType", "PickUpDate", "PickUpCity", "PickUpAddress", "Delivery", "ysfy", "jhck");
        foreach ($arrayed as $a) {

            $params[$a] = addslashes($params[$a]);
            $dataed[] = $a . "='" . $params[$a] . "'";
        }
        $dataed2 = implode(",", $dataed);
        $this->_dao->execute("update sm_order_transaction set $dataed2 where ID = '" . $sm_id . "'");
        //added by hezp started 2016/12/27
        $BID = $this->_dao->getOne("select BID from sm_order_transaction where ID = '" . $sm_id . "' ");
        //added by hezp ended 2016/12/27
        $this->_dao->execute("update sm_exc_dd_tag set IsChange = 1 where ID = '" . $BID . "'");

        $arrayfh = array("PickUpType", "PickUpDate", "PickUpCity", "PickUpAddress", "Delivery", "ysfy", "jhck");
        foreach ($arrayfh as $a) {


            if ($a == "PickUpType") {
                $params[$a] = $GLOBALS['JIAOGE_TYPES'][$params[$a]];
            }
            if ($a == "Delivery") {
                $params[$a] = $GLOBALS['DELIVERY'][$params[$a]];
            }
            if ($a == "ysfy") {
                $params[$a] = $GLOBALS['HTSF'][$params[$a]];
            }


            $params[$a] = addslashes($params[$a]);
            $datafh[] =  $params[$a];
        }

        $data2 = implode("|-|", $datafh);
        echo $data2 . "|-|" . $sm_id;
    }

    public function qrschtsales($params)
    {

        //$comp = $this->_dao->getRow( "SELECT * FROM sys_company WHERE ID='".$_SESSION['SYS_COMPANYID']."' AND TradePassWD=MD5( '$params[mima]' ) " );
        //if( empty( $comp ) ){
        //alert( "交易密码输入错误" );
        //goBack();
        //}

        $arr = $this->_dao->getRow("select * from sm_order_transaction as jt left join sm_contract_transaction_detail as jd on jd.TID = jt.ID where jt.ID = '" . $params['id'] . "' ");
        //$zy = $this->_dao->getRow( "SELECT * FROM sm_exc_sales WHERE ID=$arr[Sid]" );

        $OrderNo = $this->_dao->getOne("select OrderNo from sm_exc_dd_tag as d where d.ID='" . $params['DDid'] . "'");

        $num = 0;
        $zydetail = $this->_dao->query("select * from sm_exc_dd  where  Dtid='" . $arr['BID'] . "'");
        foreach ($zydetail as $v) {
            if ($v['XyPrice'] == $v['PriceContention']) {
            } else {
                $num++;
            }
            //echo $v['XyPrice'] == $v['PriceContention'] ;exit;
        }

        if ($num > 0) {
            if ($arr['Mid_Shipper'] == $_SESSION['SYS_COMPANYID']) {
                $this->_dao->execute("update sm_order_transaction SET  IsConfirmBo=0,Status=0 where ID = '" . $params['id'] . "'");
            }
            if ($arr['Mid_Consignee'] == $_SESSION['SYS_COMPANYID']) {
                $this->_dao->execute("update sm_order_transaction SET  IsConfirmSo=0,Status=0 where ID = '" . $params['id'] . "'");
            }
        }
        if ($arr['SalesType'] == "1" || $arr['SalesType'] == "5") {  //----|| $arr['SalesType'] == "6"
            $buymobile = $this->_dao->getRow("select * from sm_exc_dd where Dtid = '" . $arr['Bid'] . "'");
            $sellmobile = $this->_dao->getRow("select * from sm_exc_sales where ID = '" . $buymobile['Sid'] . "'");
        }
        if ($arr['SalesType'] == "2" || $arr['SalesType'] == "6") {
            $sellmobile = $this->_dao->getRow("select * from sm_exc_dd where Dtid = '" . $arr['Bid'] . "'");
            $buymobile = $this->_dao->getRow("select * from sm_exc_sales where ID = '" . $sellmobile['Sid'] . "'");
        }

        if ($_SESSION['SYS_COMPANYID'] == $arr['Mid_Shipper']) {

            $status = 4;
            $this->_dao->execute("update sm_order_transaction SET IsConfirmSo = 2 ,ConfirmSoDate=now(),ConfirmSoUser = '" . $_SESSION[SYS_USERNAME] . "' where ID = '" . $params['id'] . "'");
            //$this->_dao->execute( "UPDATE sm_exc_buy SET Status=4 WHERE ID=$arr[BID]" );
            //交易状态表
            $this->_dao->execute("insert into sm_contract_transaction_status set TID='" . $params['id'] . "',StatusDesc='卖方确认生成订单',CreateDate=now(),CreateUser='$_SESSION[SYS_USERNAME]'");

            //发送短消息
            $zymobile = $this->_dao->get_sys_company($tmp['Mid_Consignee']);

            $temp = "订单:" . $OrderNo;
            $Message = Msg::setMsgHtscTemp($_SESSION['SYS_COMPABB'], $temp);
            $this->sendMsgFunc($_SESSION['SYS_COMPANYID'], $arr['Mid_Consignee'], $Message, 1);

            $this->sendSmsFunc($arr['Mid_Consignee'], $Message, $buymobile['CreateUser'], 6);
            //发送信息
            $this->sethtlog($arr['SalesType'], "卖方生成订单", $temp, "卖方生成订单", $params['id']);

            if ($status == "4") {
                echo "1";
            }
            if ($status == "5") {
                echo "3";
            }
        } else if ($_SESSION['SYS_COMPANYID'] == $arr['Mid_Consignee']) {


            if ($arr['IsConfirmSo'] == "2") {
                $status = 5;
            } else {
                $status = 4;
            }
            $this->_dao->execute("update sm_order_transaction SET IsConfirmBo = 2 ,ConfirmBoDate=now(),ConfirmBoUser = '" . $_SESSION[SYS_USERNAME] . "' where ID = '" . $params['id'] . "'");
            //$this->_dao->execute( "UPDATE sm_exc_buy SET Status=4 WHERE ID=$arr[BID]" );
            //交易状态表
            $this->_dao->execute("insert into sm_contract_transaction_status set TID='" . $params['id'] . "',StatusDesc='买方确认生成订单',CreateDate=now(),CreateUser='$_SESSION[SYS_USERNAME]'");

            //发送短消息
            $zymobile = $this->_dao->get_sys_company($tmp['Mid_Shipper']);
            $temp = "订单:" . $OrderNo;
            $Message = Msg::setMsgHtscTemp($_SESSION['SYS_COMPABB'], $temp);
            $this->sendMsgFunc($_SESSION['SYS_COMPANYID'], $arr['Mid_Shipper'], $Message, 1);
            $this->sendSmsFunc($arr['Mid_Shipper'], $Message, $sellmobile['CreateUser'], 6);
            $this->sethtlog($arr['SalesType'], $temp, "买方生成订单", $params['id']);

            echo "2";
        }
    }

    public function PanduanKuCun($params)
    {

        $tQuantity = array();
        $contact = $this->_dao->getRow("select * from sm_order_transaction where BID = '" . $tagid . "'");

        $buy = $this->_dao->query("select Sid from sm_exc_dd where Dtid='" . $tagid . "'");
        $salesA = array();
        foreach ($buy as $temp) {
            $zydetail2[$temp['Sid']] = $this->_dao->getRow("SELECT QuantitySales,QuantitySalesed FROM sm_exc_sales_details where Pid='" . $temp['Sid'] . "'");

            $zydetail3[$temp['Sid']] = $this->_dao->getRow("select BuyQuantity from sm_exc_dd,sm_exc_sales_details,sm_exc_sales  where sm_exc_sales_details.ID=sm_exc_dd.sdid and sm_exc_dd.Sid=sm_exc_sales.ID and sm_exc_dd.Sid=" . $temp['Sid'] . " and Dtid='" . $tagid . "'"); //查询该用户购买数量
            /*echo "资源总量".$zydetail2[$temp['Sid']]['QuantitySales']."<br/>";
            echo "当前已销售总量".$zydetail2[$temp['Sid']]['QuantitySalesed']."<br/>";
            echo "该用户购买量".$zydetail3[$temp['Sid']]['BuyQuantity']."<br/>";*/
            $zydetail2[$temp['Sid']]['Quantity'] =  $zydetail2[$temp['Sid']]['QuantitySales'] - $zydetail2[$temp['Sid']]['QuantitySalesed'] + $zydetail3[$temp['Sid']]['BuyQuantity']; //获得当前剩余数量
            /*echo "销售总量减去该用户购买量".($zydetail2[$temp['Sid']]['QuantitySalesed']-$zydetail3[$temp['Sid']]['BuyQuantity'])."<br/>";*/
            array_push($tQuantity, $zydetail2[$temp['Sid']]['QuantitySalesed'] - $zydetail3[$temp['Sid']]['BuyQuantity']);
            array_push($salesA, $zydetail2[$temp['Sid']]['Quantity']); //将剩余值写入数组~

        }

        $zydetail = $this->_dao->query("select *,sm_exc_dd.ID as did,sm_exc_dd.CreateDate as dddate,sm_exc_sales.CreateDate as fbdate from sm_exc_dd,sm_exc_sales_details,sm_exc_sales  where sm_exc_sales_details.ID=sm_exc_dd.sdid and sm_exc_dd.Sid=sm_exc_sales.ID and   Dtid='" . $tagid . "'");
        $salesB = array();
        foreach ($zydetail as $v) {
            array_push($salesB, $params['sl' . $v['did']]); //将剩余值写入数组
        }

        //salesA 为库存剩余量
        //salesB 为前台购买量

        for ($i = 0; $i < sizeof($salesA); $i++) {
            if ($salesA[$i] < $salesB[$i]) {
                goURL("bizorder.php?view=orderdetail&flag=1&id=" . $tagid);
                break;
            } else {
                /*echo "销售总量减去该用户购买量".$tQuantity[$i]."<br />";
                echo "前台购买量".$salesB[$i]."<br />";*/
                $tQuantity[$i] += $salesB[$i];
                /*echo "当前总的购买量".$tQuantity[$i]."<br />";*/
            }
        }
        $tempSid = $this->_dao->getOnesArray("select Sid from sm_exc_dd where Dtid='" . $tagid . "'");

        for ($i = 0; $i < sizeof($salesA); $i++) {
            $this->_dao->execute("UPDATE sm_exc_sales_details SET QuantitySalesed =" . $tQuantity[$i] . " where Pid=" . $tempSid[$i]);
        }

        $num = 0;
        $tmoney = 0;
        $weight = 0;
        foreach ($zydetail as $v) {
            if (($v['PriceContention'] == $params['jg' . $v['did']]) && ($v['BuyQuantity'] == $params['sl' . $v['did']]) && ($v['PickUpDate'] == $params['jhrq' . $v['did']])  && ($v['Factory'] == $params['gc' . $v['did']])) {
            } else {
                $num++;
            }
            $this->_dao->execute("update sm_exc_dd set PriceContention = '" . $params['jg' . $v['did']] . "',BuyQuantity =  '" . $params['sl' . $v['did']] . "',PickUpDate='" . $params['jhrq' . $v['did']] . "',Factory='" . $params['gc' . $v['did']] . "'   where ID = '" . $v['did'] . "' ");

            $tmoney = $tmoney +    $params['jg' . $v['did']] * $params['sl' . $v['did']];
            $weight = $weight + $params['sl' . $v['did']];
        }

        if ($num > 0) {
            $this->_dao->execute("update sm_exc_dd_tag set Tmoney = '" . $tmoney . "',Tweight =  '" . $weight . "'  where ID = '" . $tagid . "'  ");
            if ($contact['Mid_Shipper'] == $_SESSION['SYS_COMPANYID']) {
                $this->_dao->execute("update sm_order_transaction SET TotalWeight= '" . $weight . "',TotalMoney='" . $tmoney . "' where BID = '" . $tagid . "'");
            } //,IsConfirmBo=0
            if ($contact['Mid_Consignee'] == $_SESSION['SYS_COMPANYID']) {
                $this->_dao->execute("update sm_order_transaction SET  TotalWeight= '" . $weight . "',TotalMoney='" . $tmoney . "' where BID = '" . $tagid . "'");
            } //,IsConfirmSo=0
        }
    }

    public function xieyijia($params)
    {

        $id = $params['xyid'];
        $tagid = $params['xytagid'];

        $contact = $this->_dao->getRow("select * from sm_order_transaction where ID = '" . $id . "'");
        $zydetail = $this->_dao->query("select *,sm_exc_dd.ID as did,sm_exc_sales_details.ID as ddid from sm_exc_dd,sm_exc_sales_details,sm_exc_sales  where sm_exc_sales_details.Pid=sm_exc_sales.ID and sm_exc_dd.sdid=sm_exc_sales_details.ID and   Dtid='" . $tagid . "'");

        $num = 0;
        foreach ($zydetail as $v) {
            if ($v['XyPrice'] == $params['xieyi' . $v['did']]) {
            } else {
                $num++;
            }
            $this->_dao->execute("update sm_exc_dd set XyPrice = '" . $params['xieyi' . $v['did']] . "' where ID = '" . $v['did'] . "'  ");
        }

        if ($num > 0) {
            if ($contact['Mid_Shipper'] == $_SESSION['SYS_COMPANYID']) {
                //$this->_dao->execute( "update sm_order_transaction SET IsConfirmBo=0 where ID = '".$id."'" );	
                //add by xiakang for yunqian started 2015/10/30
                // $salecom = yqcontract::getShenpiType($contact['ID'], $contact['Mid_Shipper']);
                // if ($salecom['type'] != "0") {
                //     $this->_dao->execute("update sm_order_transaction SET shipper_shenpi_level_no= '-1' where ID = '" . $id . "'");
                // }
                //add by xiakang for yunqian ended 2015/10/30
            }
            if ($contact['Mid_Consignee'] == $_SESSION['SYS_COMPANYID']) {
                //$this->_dao->execute( "update sm_order_transaction SET IsConfirmSo=0 where ID = '".$id."'" );
                //add by xiakang for yunqian started 2015/10/30
                // $buycom = yqcontract::getShenpiType($contact['ID'], $contact['Mid_Consignee']);
                // if ($buycom['type'] != "0") {
                //     $this->_dao->execute("update sm_order_transaction SET consignee_shenpi_level_no= '-1' where ID = '" . $id . "' ");
                // }
                //add by xiakang for yunqian ended 2015/10/30
            }
        }
        goURL("bizorder.php?view=orderdetail&id=" . $tagid);
    }



    //added by hezp sfhysd quanxuan started 2016/09/18
    public function select_oid($params)
    {
        $order_detail_id = $this->_dao->getOnes("select ID from sm_exc_order_detail where Oid = '" . $params['oid'] . "' ", 0);
        //$order_detail_id = implode(",",$order_detail_id);
        $this->_dao->jsonshow(1, $order_detail_id, "");
    }
    //added by hezp sfhysd quanxuan ended 2016/09/18

    //added by shizg for uploadpicture started 2016/10/24
    public function shuploadpicture($params)
    {
        $Oid = $params['Oid'];
        $this->assign("Oid", $Oid);
        $ShorFh = $params['ShorFh'];
        $this->assign("ShorFh", $ShorFh);
    }

    public function uploadpicture($params)
    {

        $Oid = $params['Oid'];
        $ShorFh = $params['ShorFh'];
        if (empty($_FILES["file"]["name"])) {
            alert("请选择！");
            goback();
            exit;
        }
        //检查上传文件是否是图片
        if (strpos($_FILES["file"]["type"], 'image/') === false) {
            alert("只能上传图片文件，请重新选择！");
            goback();
            exit;
        }

        $sql_picnum = "select count(ID) from sm_exc_order_pics where Oid='" . $Oid . "' and Type='" . $ShorFh . "' "; //查询
        $pingjia_picnum = $this->_dao->getOne($sql_picnum);
        //alert($pingjia_picnum);
        if ($pingjia_picnum >= 5) {
            alert("上传图片最多为五张！");
            goback();
            exit;
        }
        if (!$this->is_safe($_FILES["file"]["tmp_name"])) {
            alert("不要恶意伤害我！");
            goback();
            exit;
        }
        include("miniature.php");
        $min = new CreatMiniature();
        $picturename = $_FILES["file"]["name"];
        @$houzhui = end(explode(".", $picturename));
        $sql1 = "select uuid() ";   //取出一个uuid
        $uuid = $this->_dao->getOne($sql1);
        $nametime = date('YmdHis', time());
        $filename = $nametime . "." . $houzhui;
        $filename2 = $nametime . "_small" . "." . $houzhui;
        $path1 = $_SERVER["DOCUMENT_ROOT"] . "/uploadfile/picture/" . $uuid . "/" . $filename;
        $f_dir = $_SERVER["DOCUMENT_ROOT"] . "/uploadfile/picture/" . $uuid . "/";
        if (is_dir($f_dir) || @mkdir($f_dir)) {
        }
        move_uploaded_file($_FILES["file"]["tmp_name"], $f_dir . $filename);
        //生成缩略图
        $path2 = $_SERVER["DOCUMENT_ROOT"] . "/uploadfile/picture/" . $uuid . "/" . $filename2;
        $min->SetVar($path1, "file");
        $image_info = getimagesize($path1);
        $width = $image_info['0'];
        $height = $image_info['1'];
        if ($width > $height) {
            $w = $width;
            $h = $height;
        } else {
            $h = $width;
            $w = $height;
        }
        $small = $min->Prorate($path2, 50, $h * 50 / $w);

        $path11 = "/uploadfile/picture/" . $uuid . "/" . $filename;
        $path21 = "/uploadfile/picture/" . $uuid . "/" . $filename2;
        $sql = "insert into sm_exc_order_pics set Oid='" . $Oid . "',Type='" . $ShorFh . "',Mid='" . $_SESSION['SYS_COMPANYID'] . "',Path = '" . $path11 . "',ThumbnailURL = '" . $path21 . "',Date = '" . date('Y-m-d H:i:s', time()) . "' ";   //上传图片
        $this->_dao->execute($sql);

        alert("新增成功！");
        echo '<script>window.close();</script>';
    }

    public function shpicturelists($params)
    {
        $Oid = $params['Oid'];
        $this->assign("Oid", $Oid);
        $Status = $params['Status'];
        $this->assign("Status", $Status);
        $ShorFh = $params['ShorFh'];
        $this->assign("ShorFh", $ShorFh);
        $xiugai = $params['xiugai'];
        $this->assign("xiugai", $xiugai);

        $sqlsearch = "select * from sm_exc_order_pics where Oid='" . $Oid . "' and Type='" . $ShorFh . "' order by Date desc"; //查询
        $Piclist = $this->_dao->query($sqlsearch);

        foreach ($Piclist as $k => $valus) {

            $Piclists[$k]['ID'] = $valus['ID'];
            $Piclists[$k]['Date'] = $valus['Date'];
            $Piclists[$k]['ThumbnailURL'] = $valus['ThumbnailURL'];
            $Piclists[$k]['Path'] = $valus['Path'];
        }
        $this->assign("Piclists", $Piclists);

        $Canmodify = 0;
        $sqlMid = "select Mid from sm_exc_order_pics where Oid='" . $Oid . "'and Type='" . $ShorFh . "' "; //查询
        $Mid = $this->_dao->getOne($sqlMid);
        if ($Mid == $_SESSION['SYS_COMPANYID']) {
            $Canmodify = 1;
        }
        $this->assign("Canmodify", $Canmodify);
    }

    public function viewpicture($params)
    {

        $ID = $params['picid'];
        $sqlsearch = "select Path from sm_exc_order_pics where ID=$ID"; //查询
        $path = $this->_dao->getOne($sqlsearch);
        $filename = pathinfo($path);
        $filename2 = $filename[basename];
        $fileroat = "/uploadfile/picture/" . $filename2;
        echo "<a href='{$path}'><img src='{$path}' width=\"250\" height=\"300\"></a><br>\n";
        //$contents = file_get_contents($path);
        //print_r("1111");
    }

    public function deletepicture($params)
    {

        $PicId = $params['picid'];
        $sql = "delete from sm_exc_order_pics where ID in($PicId) ";   //删除图片
        $this->_dao->execute($sql);
    }
    //added by shizg for uploadpicture ended 2016/10/24

    /**收货单*/
    public function shysd($params)
    {

        $this->sfhysd_common($params);

        $fbdate = $this->_dao->getOne("select sm_exc_sales.CreateDate from sm_exc_dd_tag,sm_exc_dd,sm_exc_sales where sm_exc_dd_tag.ID=sm_exc_dd.Dtid and sm_exc_dd.Sid=sm_exc_sales.ID and sm_exc_dd_tag.ID='" . $params['id'] . "'");
        $this->assign("fbdate", $fbdate);

        $qrdate = $this->_dao->getRow("select * from sm_contract_transaction where BID='" . $params['id'] . "' ");
        //print_r($qrdate);
        //added by hezp 多次发货 started 2016/11/24
        if ($qrdate['AllowMultiFh'] == '1' && $params['ys_status'] != '1') {
            //$Result['AllowMultiFh'] = "1";
            $qrdate['Status'] = $this->get_AllowMultiFh_status($qrdate['PayType'], $qrdate['Mid_Consignee'], $qrdate['Status'], $qrdate['contractTye']);

            $where_order = " and sm_exc_order.Status in ('1','2') ";
        } else if ($qrdate['AllowMultiFh'] == '1' && $params['ys_status'] == '1') {
            $qrdate['Status'] = "13";
        }

        //能否发货1 已确认的发货单 2 双方确认完成的
        if (strtotime($qrdate['FhDate']) < strtotime("2017-04-11 14:00:00")) {
            $orderstatus = "1";
        } else {
            $orderstatus = $this->_dao->getOne("select count(*) from sm_exc_order where Did = '" . $qrdate['BID'] . "' and Status=1 ", 0);
        }
        $this->assign("orderstatus", $orderstatus);
        //added by hezp 多次发货 ended 2016/11/24
        $this->assign("qrdate", $qrdate);


        //卖家

        $Allsh = 0;
        $Allfh = 0;
        // Updated for shysd by Zhu Dahua started  2015/09/16
        $zydetail = $this->_dao->query("select *,sm_exc_order.ID as oid,sm_exc_order.Status as Status2, sm_exc_dd_tag.ischange from sm_exc_order LEFT JOIN sm_exc_dd_tag ON sm_exc_dd_tag.id = sm_exc_order.ID where   sm_exc_order.Did='" . $params['id'] . "' $where_order ");
        $ischange = $zydetail[0]['ischange'];
        $this->assign("ischange", $ischange);
        // Updated for shysd by Zhu Dahua ended  2015/09/16
        foreach ($zydetail as $val) {
            //Updated by quanjw for meijiao start 2015/1/20 ID冲突,没有取 订单表的ID-->
            $pzzlist[$val['oid']] = $this->_dao->query(
                "select od.id as id, od.*, dd.pinming,dd.caizhi,dd.guige,
                                                            dd.Factory ,sd.cd,sd.yongtu,sd.strength,sd.xincengWeight,
                                                            sd.Vid ,sd.tks_fe,sd.tks_si,sd.tks_al
                                                        from sm_exc_order_detail as od,
                                                            sm_exc_dd as dd , 
                                                            sm_exc_sales_details as sd
                                                        where dd.ID=od.Ddid 
                                                            and dd.Sdid = sd.id 
                                                            and  Oid = '" . $val['oid'] . "' 
                                                            and od.Status=1 
                                                            order by od.ID ASC",
                0
            );
            //Updated by quanjw for meijiao end 2015/1/20-->
            $Allsh = $Allsh + $val['Shsl'];
            $Allfh = $Allfh + $val['Fhsl'];
        }
        $NowDate = date("Y-m-d");
        $this->assign("nowdate", $NowDate);
        $this->assign("pzzlist", $pzzlist);
        $this->assign("zydetail", $zydetail);
        //updated by quanjw for sl start 2015/1/8
        $this->assign("Allsh", sprintf("%.4f", $Allsh));
        $this->assign("Allfh", sprintf("%.4f", $Allfh));
        //updated by quanjw for sl end 2015/1/8
        $this->assign("page_title", "验收单_我的订单_");

        //运输公司
        $yscom2 = $this->_dao->Aquery("select ID,TransName  from sm_user_trans where  Status=1");
        $this->assign("yscom2", $yscom2);

        if ($params['id'] != "") {
            $err = $this->_dao->getRow("select * from sm_order_transaction where (Mid_Shipper = '" . $_SESSION['SYS_COMPANYID'] . "' or Mid_Consignee = '" . $_SESSION['SYS_COMPANYID'] . "' ) and BID = '" . $params['id'] . "'");
            if ($err) {
            } else {
                echo "error!";
                exit;
            }
        }

        //权限判断
        if ($_SESSION['SYS_USERROLE'] == "S") {
            goURL("member.php?view=orderinfodetails&tid=" . $params['id']);
            exit;
        }

        $pytd = $this->_dao->getOne("select pysametd from sys_company where ID='" . $_SESSION['SYS_COMPANYID'] . "'"); //交易密码是否同支付密码
        $this->assign("pytd", $pytd);

        $ht = $this->_dao->getRow("select * from sm_contract_transaction where BID = '" . $params['id'] . "'", 0);
        //added by hezp 多次发货 started 2016/11/24
        if ($ht['AllowMultiFh'] == '1' && $params['ys_status'] != '1') {
            //$Result['AllowMultiFh'] = "1";
            $ht['Status'] = $this->get_AllowMultiFh_status($ht['PayType'], $ht['Mid_Consignee'], $ht['Status'], $ht['contractTye']);
        } else if ($ht['AllowMultiFh'] == '1' && $params['ys_status'] == '1') {
            $ht['Status'] = "13";
        }
        //added by hezp 多次发货 ended 2016/11/24
        $id = $ht['BID'];
        $this->assign("ht", $ht);

        $this->assign("flagid", $id);

        $this->setvars();

        $this->assign("params", $params);

        //提单列表
        $tdlist = $this->_dao->query("select * from sm_contract_transaction_td where Tid ='" . $ht['ID'] . "'");
        $this->assign("tdlist", $tdlist);
    }

    function sfhysd_common($params)
    {

        // Added for quanxian sheding  by hzp started  2014/12/25
        $Mid = $_SESSION['SYS_USERID'];
        $ht = $this->_dao->getRow("select * from sm_contract_transaction where BID = '" . $params['id'] . "'", 0);
        $quanx = $this->_dao->getRow("SELECT childroot,IsMain,PowerLimit FROM sys_adminuser WHERE ID='" . $Mid . "'");
        $pos = strripos($quanx["childroot"], "5");
        if ($pos === false) {
            $pos = -1;
        }
        if ($pos >= 0 || $quanx["IsMain"] == 1) {
            $this->assign("IsModify", 1);
        } else {
            $this->assign("IsModify", 0);
        }
        // Added for quanxian sheding  by hzp end  2014/12/25

        $zy = $this->_dao->getRow("select * from sm_exc_dd_tag where ID='" . $params['id'] . "'");
        $zy['Tmoney'] = number_format($zy['Tmoney'], 2);

        $this->assign("zy", $zy);

        //ADD BY XIAKANG FOR YUNQIAN STARTED 2015/05/29
        $yqsp = $this->_dao->getRow("select * from sm_yq_contract_transcation where dzcontract_id = '" . $qrdate['ID'] . "'");
        //print_r($yqsp);
        //ADD BY XIAKANG FOR YUNQIAN ENDED 2015/06/15

        //买方公司
        //Update by xiakang for yunqian started 2015/07/09
        //采购方
        // $buycom = yqcontract::getShenpiType($qrdate['ID'], $qrdate['Mid_Consignee']);
        //供应方
        // $salecom = yqcontract::getShenpiType($qrdate['ID'], $qrdate['Mid_Shipper']);
        //ADD BY XIAKANG FOR YUNQIAN STARTED 2015/05/22

        // $this->assign("buycom", $buycom);
        // $this->assign("salecom", $salecom);
        //ADD BY XIAKANG FOR YUNQIAN ENDED 2015/06/15
        $this->assign("yqsp", $yqsp);
        //ADD BY XIAKANG FOR YUNQIAN ENDED 2015/05/29
        if ($zy['Did'] == $_SESSION['SYS_COMPANYID']) {
            $this->assign("myorder", 1);
        }

        $this->assign("State", $GLOBALS['CHINA_SHENG']);

        //买家
        if ($zy['SlType'] == "1" || $zy['SlType'] == "5") {  //销售类型订单
            $buycp = $this->_dao->get_sys_company($zy['Mid']);
            $sellcp = $this->_dao->get_sys_company($zy['Did']);
            $sellcp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '" . $zy['Did'] . "' and  IsMain = 1");
            $buycp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '" . $zy['Mid'] . "' and  IsMain = 1");
        } else { //采购类型订单
            $buycp = $this->_dao->get_sys_company($zy['Did']);
            $sellcp = $this->_dao->get_sys_company($zy['Mid']);

            $sellcp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '" . $zy['Mid'] . "' and  IsMain = 1");
            $buycp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '" . $zy['Did'] . "' and  IsMain = 1");
        }
        $this->assign("buycp", $buycp);
        $this->assign("sellcp", $sellcp);

        //订单追踪
        $orderfind = $this->_dao->query("select * from  sm_contract_transaction_status where TID = '" . $ht['ID'] . "' order by ID DESC");
        foreach ($orderfind as &$tmp) {
            $usname = $this->_dao->getOne("select ARealName from sys_adminuser where AUserName = '" . $tmp['CreateUser'] . "' limit 1", 0);
            $tmp['ARealName'] = $usname;
        }
        $this->assign("orderfind", $orderfind);

        //在线洽谈
        $huifu = array();
        $qiatan  = $this->_dao->query("select * from sys_message where OrderID = '" . $params['id'] . "' and ReciveID =0 order by ID ASC");
        foreach ($qiatan as &$val) {
            //	echo	$val['Message'] = $val['Message'];
            $huifu[$val['ID']] = $this->_dao->query("select * from sys_message where ReciveID = '" . $val['ID'] . "' order by ID ASC");
        }

        $this->assign("huifu", $huifu);
        $this->assign("qiatan", $qiatan);
    }



    /**发货单*/
    public function fhysd($params)
    {

        $this->sfhysd_common($params);

        $fbdatas = $this->_dao->getRow("select sm_exc_sales.CreateDate as CreateDate, sm_exc_sales.StoreType as StoreType from sm_exc_dd_tag,sm_exc_dd,sm_exc_sales where sm_exc_dd_tag.ID=sm_exc_dd.Dtid and sm_exc_dd.Sid=sm_exc_sales.ID and sm_exc_dd_tag.ID='" . $params['id'] . "'");
        $this->assign("fbdate", $fbdatas['CreateDate']);
        //$this->assign("storytype",$fbdatas['StoreType']);
        $storytype = $fbdatas['StoreType'];

        $qrdate = $this->_dao->getRow("select * from sm_contract_transaction where BID='" . $params['id'] . "' ");
        //added by hezp 多次发货 started 2016/11/24
        if ($qrdate['AllowMultiFh'] == '1') {
            //$Result['AllowMultiFh'] = "1";
            $qrdate['Status'] = $this->get_AllowMultiFh_status($qrdate['PayType'], $qrdate['Mid_Consignee'], $qrdate['Status'], $qrdate['contractTye']);
        }
        //能否发货1 已确认的发货单 2 双方确认完成的
        $orderstatus = $this->_dao->getOne("select count(*) from sm_exc_order where Did = '" . $qrdate['BID'] . "' and ( Status=3 or Status=0 ) ", 0);
        $this->assign("orderstatus", $orderstatus);

        //added by hezp 多次发货 ended 2016/11/24
        $this->assign("qrdate", $qrdate);

        //卖家
        $Allsl = 0;
        $zydetail = $this->_dao->query("select *,sm_exc_order.ID as oid from sm_exc_order  where   Did='" . $params['id'] . "' order by ID ASC");
        foreach ($zydetail as $val) {
            //Updated by quanjw for meijiao start 2015/1/14

            $pzzlist[$val['oid']] = $this->_dao->query(
                "select od.* ,dd.* , sd.cd ,sd.yongtu, sd.strength , sd.xincengWeight ,
                                                            sd.Vid,sd.tks_fe , sd.tks_si , sd.tks_al
                                                        from sm_exc_sales_details as sd ,
                                                            sm_exc_order_detail as od ,
                                                            sm_exc_dd as dd
                                                        where dd.ID=od.Ddid 
                                                            and dd.Sdid=sd.id 
                                                            and Oid = '" . $val['oid'] . "' and
                                                            od.Status=1 
                                                        order by od.ID ASC",
                0
            );

            $pzzlist2[$val['oid']] = $this->_dao->query(
                "select dd.*,od.* , od.Status as Status2 ,sd.cd ,sd.yongtu,sd.strength,
                                                            sd.xincengWeight , sd.Vid , sd.tks_fe , sd.tks_si , sd.tks_al
                                                        from sm_exc_dd as dd,
                                                            sm_exc_order_detail as od ,
                                                            sm_exc_sales_details as sd
                                                        where dd.ID=od.Ddid 
                                                            and dd.Sdid=sd.id 
                                                            and  Oid = '" . $val['oid'] . "' 
                                                        order by od.ID ASC",
                0
            );

            //Updated by quanjw for meijiao end 2015/1/14
            $Allsl = $Allsl + $val['Fhsl'];

            // 打包资源设置件重件数
            if ($storytype == 2) {
                foreach ($pzzlist[$val['oid']] as &$value) {
                    if (!$value['Fhjz']) {
                        //$value['Fhjz'] = $value['Fhsl'];
                        $value['Fhjs'] = 1;
                    }
                }
                foreach ($pzzlist2[$val['oid']] as &$value) {
                    if (!$value['Fhjz']) {
                        //$value['Fhjz'] = $value['Fhsl'];
                        $value['Fhjs'] = 1;
                    }
                }
            }
        }

        $tempOid = $zydetail[0]['oid'];
        $tempID = $pzzlist2[$tempOid][0]['ID'];
        //$zydetail[0]['FhDate']=date('Y-m-d',time());
        $this->assign("tempOid", $tempOid);
        $this->assign("tempID", $tempID);
        $this->assign("pzzlist", $pzzlist);
        $this->assign("pzzlist2", $pzzlist2);
        $this->assign("zydetail", $zydetail);
        $NowDate = date('Y-m-d', time());
        $this->assign("nowDate", $NowDate);
        $this->assign("Allsl", $Allsl);

        //运输公司
        $yscom = $this->_dao->query("select *  from sm_user_trans where Mid ='" . $_SESSION['SYS_COMPANYID'] . "' and Status=1");
        $this->assign("yscom", $yscom);

        if (empty($yscom)) {
            $this->assign("nullyscom", 1);
        }

        $yscom2 = $this->_dao->Aquery("select ID,TransName  from sm_user_trans where  Status=1");

        $this->assign("yscom2", $yscom2);
        $this->assign("page_title", "发货单_我的订单_");

        $ht = $this->_dao->getRow("select * from sm_contract_transaction where BID = '" . $params['id'] . "'", 0);
        //added by hezp 多次发货 started 2016/11/24
        if ($ht['AllowMultiFh'] == '1') {
            //$Result['AllowMultiFh'] = "1";
            $ht['Status'] = $this->get_AllowMultiFh_status($ht['PayType'], $ht['Mid_Consignee'], $ht['Status'], $ht['contractTye']);
        }
        //added by hezp 多次发货 ended 2016/11/24
        $id = $ht['BID'];
        $this->assign("ht", $ht);
        $this->assign("flagid", $id);
        $this->setvars();
        $this->assign("params", $params);
    }

    public function do_fhysd($params)
    {

        $ID = $params['oid'];
        $temp = $this->_dao->getRow("select * from sm_exc_order where ID=$ID");  //取出ID为$ID的订单

        $fhnum = $this->getSeqNo("FhNumber", "", "", ""); //订单号
        $this->_dao->execute("insert into sm_exc_order (Did,Sdid,Fhsl,Fhjz,Fhjs,Fhlxr,Fhphone,Fhbz,Yscom,Carnum,Shsl,Shjz,Shjs,Shlxr,Shphone,Shbz,Jsjg,Jsmoney,FhDate,ShDate,FhNum) value (\"$temp[Did]\",\"$temp[Sdid]\",\"$temp[Fhsl]\",\"$temp[Fhjz]\",\"$temp[Fhjs]\",\"$temp[Fhlxr]\",\"$temp[Fhphone]\",\"$temp[Fhbz]\",\"$temp[Yscom]\",\"$temp[Carnum]\",\"$temp[Shsl]\",\"$temp[Shjz]\",\"$temp[Shjs]\",\"$temp[Shlxr]\",\"$temp[Shphone]\",\"$temp[Shbz]\",\"$temp[Jsjg]\",\"$temp[Jsmoney]\",\"$temp[FhDate]\",\"$temp[ShDate]\",\"$fhnum\")");

        $sm_id = $this->_dao->insert_id();
        $orderinfo = $this->_dao->query("select * from sm_exc_order_detail where Oid=$ID and IsMain=1");

        foreach ($orderinfo as $tmp) {

            $this->_dao->execute("insert into sm_exc_order_detail set Ddid='" . $tmp['Ddid'] . "',Fhjz='" . $tmp['Fhjz'] . "',Fhjs='" . $tmp['Fhjs'] . "',Fhsl='" . $tmp['Fhsl'] . "',Oid='" . $sm_id . "',Status=1,MfgDate='" . $tmp['MfgDate'] . "',IsMain='" . $tmp['IsMain'] . "',jsjg='" . $tmp['Jsjg'] . "'");
        }

        goURL("bizorder.php?view=fhysd&id=" . $temp['Did']);
    }

    // Added for fhxx by Zhu Dahua started  2015/09/16
    public function fhxx2($params)
    {

        //***********发货数量验证***************
        //deleted by quajw for fhyz start 2016/09/29
        /*	$tempID=$params['id'];//发货单  Order表ID
            
            $ODID=$this->_dao->getOnes("select ID from sm_exc_order_detail where Oid='".$tempID."'");//检索该发货单下所有的详细表ID

            $isEmpty=0;
            foreach($ODID as $val){
                
                if($params['Fhsl'.$val]<=0 && $params['Status'.$val]!=0)//检索传过来的$params里提交的发货数量是否小于等于0
                {
                    echo "9";  //返回值为9 表明发货单中存在数量为0 的订单
                    exit;
                }
                if($params['Status'.$val]!=0){
                    $isEmpty=1;
                }
            }
            if($isEmpty==0){
                echo "99";
                exit;
            }
        */
        //deleted by quajw for fhyz end 2016/09/29
        //***************发货数量验证************

        $id = $params['id'];
        $did = $this->_dao->getOne("select Did from sm_exc_order where ID = '" . $id . "'", 0);
        $status = $this->_dao->getOne("select Status from sm_contract_transaction where BID = '" . $did . "'", 0);
        $shdate = $this->_dao->getOne("select ShDate from sm_contract_transaction where BID = '" . $did . "'", 0);

        $yscom = $this->_dao->Aquery("select ID,TransName  from sm_user_trans where Mid ='" . $_SESSION['SYS_COMPANYID'] . "' and Status=1");


        $info2 = $this->_dao->query("select * from sm_exc_order_detail where Oid='" . $id . "'", 0);

        $sl = 0;

        foreach ($info2 as $k => $val) {
            //if(isset($params["Status"..$val['ID']])){echo $_POST['Status'.$val['Ddid']];exit;
            if ($params['Status' . $val['ID']] == "1") {

                if ($sl2[$val['Ddid']]) {
                } else {
                    $sl2[$val['Ddid']] = 0;
                }
                $BuyQuantity = $this->_dao->getOne("select BuyQuantity from sm_exc_dd where ID='" . $val['Ddid'] . "' ", 0);

                $sl2[$val['Ddid']] = $sl2[$val['Ddid']] + $params["Fhsl" . $val['ID']];

                //update by libing for fahuo start 2015/04/01
                /*
                //if($sl2[$val['Ddid']] > $BuyQuantity*1.05){
                if($sl2[$val['Ddid']] > $BuyQuantity*1.20){
                    echo "1";exit;
                }
                */
                //update by libing for fahuo end 2015/04/01
            }
        }

        foreach ($info2 as $val) {

            $arrayfh2 = array("Fhjs", "Fhjz", "Fhsl", "MfgDate", "Status", "FhNum");
            foreach ($arrayfh2 as $a) {
                //echo  $a.$val['ID'];exit;
                if (isset($params["Status" . $val['ID']])) {

                    if ($a == "MfgDate") {
                        $dataed4[] = $a . "='" . $params[$a . $val['ID']] . "'";
                    } else {
                        $dataed4[] = $a . "='" . $params[$a . $val['ID']] . "'";
                    }
                }
            }
            //file_put_contents("fhxx.txt",print_r('Status'.$val['ID'],true));
            if ($params['Status' . $val['ID']] == "1") {

                $sl = $sl + $params["Fhsl" . $val['ID']];
                // update by quanjw for sl 
                $sl = substr(sprintf("%.4f", $sl), 0, -1);
                //$sl=substr(sprintf("%.3f",$sl),0,-1);
                // update by quanjw for sl

                $dataed5 = implode(",", $dataed4);

                $info = $this->_dao->getRow("select * from  sm_exc_order_detail  where  ID  = '" . $val['ID'] . "' ", 0);

                $this->_dao->execute("update sm_exc_order_detail set $dataed5,Fhsl='" . sprintf("%.4f", $params['Fhsl' . $val['ID']]) . "'   where  ID  = '" . $val['ID'] . "' ");

                $dataed4 = "";
                $dataed5 = "";
            } else {
                $this->_dao->execute("update sm_exc_order_detail set Status=0,Fhsl=0,Fhjz=0,Fhjs=0,Shsl=0,Shjz=0,Shjs=0   where  ID  = '" . $val['ID'] . "' ");
            }
        }

        $zongshu = $this->_dao->getOne("select sum(Shsl) as sl from sm_exc_order_detail where Oid='" . $id . "' and Status=1 ", 0);
        // Updated for fhxx by Zhu Dahua ended  2015/09/15

        // Added for fhxx by Zhu Dahua started  2015/09/21
        $FHzongshu = $this->_dao->getOne("select sum(Fhsl) as sl from sm_exc_order_detail where Oid='" . $id . "' and Status=1 ", 0);
        // Added for fhxx by Zhu Dahua ended  2015/09/21

        if ($shdate = "0000-00-00 00:00:00") { //$status=="12" || $status=="5" || 
            // Updated for fhxx by Zhu Dahua started  2015/09/15
            // 使修改发货数量时不再改变收货数量
            //$this ->_dao->execute("update sm_exc_order_detail set Shjs = Fhjs,Shjz = Fhjz,Shsl = Fhsl  where  Oid='".$id."' ");
            $this->_dao->execute("update sm_exc_order set Shsl='" . $zongshu . "'  where  ID  = '" . $id . "' ");

            $this->_dao->execute("update sm_exc_order_detail set Shjs = Fhjs,Shjz = Fhjz  where  Oid='" . $id . "' ");
            // Updated for fhxx by Zhu Dahua ended  2015/09/15
        }

        $arrayfh = array("Yscom", "Fhlxr", "Fhphone", "Carnum", "Fhbz", "FhDate", "FhNum"); //,"FhNum" 
        foreach ($arrayfh as $a) {


            $dataed[] = $a . "='" . $params[$a . $id] . "'";
        }
        $dataed2 = implode(",", $dataed);

        $this->_dao->execute("update sm_exc_order set $dataed2,Fhsl='" . $FHzongshu . "'  where  ID  = '" . $id . "' ");

        //goURL("bizorder.php?view=fhysd&id=".$id);
        //	  $data2 = implode( "|-|", $datafh );

        //	echo $data2."|-|".$id;
    }
    // Added for fhxx by Zhu Dahua ended  2015/09/16

    public function shxx($params)
    {

        $id = $params['id'];

        $yscom = $this->_dao->Aquery("select ID,TransName  from sm_user_trans where Mid ='" . $_SESSION['SYS_COMPANYID'] . "' and Status=1");
        $info2 = $this->_dao->query("select * from sm_exc_order_detail where Oid='" . $id . "'", 0);

        $sl = 0;
        foreach ($info2 as $val) {

            $arrayfh2 = array("Shjs", "Shjz", "Shsl", "MfgDate", "FhNum");
            foreach ($arrayfh2 as $a) {

                if ($a == "MfgDate") {

                    $dataed4[] = $a . "='" . $params[$a . $val['ID']] . "'";
                } else {

                    $dataed4[] = $a . "='" . $params[$a . $val['ID']] . "'";
                }
            }
            $sl = $sl + $params["Shsl" . $val['ID']];
            $dataed5 = implode(",", $dataed4);

            $info = $this->_dao->getRow("select * from  sm_exc_order_detail  where  ID  = '" . $val['ID'] . "' ", 0);
            $this->_dao->execute("update sm_exc_order_detail set $dataed5   where  ID  = '" . $val['ID'] . "' ");

            $dataed4 = "";
            $dataed5 = "";
        }

        $zongshu = $this->_dao->getOne("select sum(Shsl) as sl from sm_exc_order_detail where Oid='" . $id . "' ", 0);

        $arrayfh = array("Shlxr", "Shphone", "Shbz", "ShDate");
        foreach ($arrayfh as $a) {

            //echo $sss= $params[$a.$id];exit;
            //echo $dataed[] = $a . "='" .$params[$a.$id]. "'";
            $dataed[] = $a . "='" . $params[$a . $id] . "'";
        }
        $dataed2 = implode(",", $dataed);

        $this->_dao->execute("update sm_exc_order set $dataed2,Shsl='" . $zongshu . "'  where  ID  = '" . $id . "' ");
    }

    //确认验收
    public function qrys($params)
    {

        $this->_dao->execute("update  sm_contract_transaction set YsDate=now() where ID = '" . $params['id'] . "' ");
        $arr = $this->_dao->getRow("select * from sm_contract_transaction where ID = '" . $params['id'] . "' ", 0);
        $OrderNo = $this->_dao->getOne("select OrderNo from sm_contract_transaction as t,sm_exc_dd_tag as d where t.BID=d.ID and t.ID='" . $params['id'] . "'");

        if ($arr['PayType'] == "4") {
            $this->_dao->execute("UPDATE sm_contract_transaction SET Status=12 WHERE ID=" . $params['id']);
        } else {
            $this->_dao->execute("UPDATE sm_contract_transaction SET Status=8 WHERE ID=" . $params['id']);
        }

        $other = $this->_dao->query("select * from sm_exc_order where Did = '" . $arr['BID'] . "' ");
        foreach ($other as $tmp) {
            $exits = $this->_dao->getRow("select * from sm_exc_order_cost where ID = '" . $tmp['ID'] . "' and Did='" . $arr['BID'] . "' ");
            if (!empty($exits)) {
                $this->_dao->execute("update sm_exc_order_cost set YsComID = '" . $tmp['Yscom'] . "',Yssl = '" . $tmp['Shsl'] . "',Ysbz = '" . $tmp['Fhbz'] . "' where Oid='" . $tmp['ID'] . "' and  Did='" . $arr['BID'] . "'");
            } else {
                $this->_dao->execute("insert into sm_exc_order_cost set Did='" . $arr['BID'] . "',YsComID = '" . $tmp['Yscom'] . "',Yssl = '" . $tmp['Shsl'] . "',Ysbz = '" . $tmp['Fhbz'] . "',Oid='" . $tmp['ID'] . "' ");
            }

            $jsmoney = $tmp['Shsl'] * $tmp['Jsjg'];

            $this->_dao->execute("UPDATE sm_exc_order SET Jsmoney='" . $jsmoney . "' WHERE ID='" . $tmp['ID'] . "' ");
        }

        if ($arr['SalesType'] == "1" || $arr['SalesType'] == "5") {  ///===|| $arr['SalesType'] == "6" 
            $sellmobile = $this->_dao->getRow("select * from sm_exc_dd_tag where ID = '" . $arr['BID'] . "'");
            //$buymobile = $this->_dao->getRow("select * from sm_exc_sales where ID = '".$sellmobile['Sid']."'");
        }
        if ($arr['SalesType'] == "2" || $arr['SalesType'] == "6"  || $arr['SalesType'] == "8") { ///|| $arr['SalesType'] == "16"
            $buymobile = $this->_dao->getRow("select * from sm_exc_dd where Dtid = '" . $arr['BID'] . "' limit 1", 0);
            //$sellmobile = $this->_dao->getRow("select * from sm_exc_sales where ID = '".$buymobile['Sid']."'");
        }

        $flag = 0;
        $tempSMS = $this->_dao->getOne("SELECT ReceiveSmsType FROM sys_company WHERE ID='" . $arr['Mid_Consignee'] . "' ");
        $tempSMSay = explode(',', $tempSMS);
        foreach ($tempSMSay as $val) {
            if ($val == 13) {
                $flag = 1;
            }
        }

        //发送短消息
        $zymobile = $this->_dao->get_sys_company($arr['Mid_Consignee']);
        $temp = "订单:" . $OrderNo;
        $Message = Msg::setMsgHtysTemp($_SESSION['SYS_COMPABB'], $temp);
        $this->sendMsgFunc($_SESSION['SYS_COMPANYID'], $arr['Mid_Consignee'], $Message, 1);
        if ($flag == 1) {
            $this->sendSmsFunc($arr['Mid_Consignee'], $Message, $sellmobile['CreateUser'], 13);
        }
        $this->xieZt(1, $params['id'], "确认验收");

        $this->sethtlog($arr['SalesType'], "确认验收", $temp, "确认验收", $params['id']);

        echo "1";
    }

    /**结算单*/
    public function account($params)
    {
        // Added for quanxian sheding  by hzp started  2014/12/25
        $Mid = $_SESSION['SYS_USERID'];
        $quanx = $this->_dao->getRow("SELECT childroot,IsMain,PowerLimit FROM sys_adminuser WHERE ID='" . $Mid . "'");
        $pos = strripos($quanx["childroot"], "2");
        //var_dump($pos);
        if ($pos === false) {
            $pos = -1;
        }
        //var_dump($quanx["IsMain"]);
        if ($pos >= 0 || $quanx["IsMain"] == 1) {
            $this->assign("IsModify", 1);
        } else {
            $this->assign("IsModify", 0);
        }
        // Added for quanxian sheding  by hzp end  2014/12/25
        $this->setvars();
        $pzs2 = $this->_dao->AQuery("SELECT VarietyCode, VarietyName FROM sm_base_variety Where 1=1 ORDER BY VarietyType");
        $this->assign("pzs2", $pzs2);

        $pzs = $this->_dao->AQuery("SELECT VarietyCode, VarietyName FROM sm_base_variety_big Where 1=1 ORDER BY ID", 0);
        $this->assign("pzs", $pzs);

        $zy = $this->_dao->getRow("select * from sm_exc_dd_tag where ID='" . $params['id'] . "'");
        $zy['Tmoney'] = number_format($zy['Tmoney'], 2);

        $fbdate = $this->_dao->getOne("select sm_exc_sales.CreateDate from sm_exc_dd_tag,sm_exc_dd,sm_exc_sales where sm_exc_dd_tag.ID=sm_exc_dd.Dtid and sm_exc_dd.Sid=sm_exc_sales.ID and sm_exc_dd_tag.ID='" . $params['id'] . "'");
        $this->assign("fbdate", $fbdate);

        $qrdate = $this->_dao->getRow("select * from sm_contract_transaction where BID='" . $zy['ID'] . "' ");
        $this->assign("qrdate", $qrdate);

        //ADD BY XIAKANG FOR YUNQIAN STARTED 2015/05/29
        $yqsp = $this->_dao->getRow("select * from sm_yq_contract_transcation where dzcontract_id = '" . $qrdate['ID'] . "'");
        //ADD BY XIAKANG FOR YUNQIAN ENDED 2015/06/15

        //买方公司
        //update by xiakang for yunqian started 2015/07/09
        //采购方
        // $buycom = yqcontract::getShenpiType($qrdate['ID'], $qrdate['Mid_Consignee']);
        //供应方
        // $salecom = yqcontract::getShenpiType($qrdate['ID'], $qrdate['Mid_Shipper']);
        //ADD BY XIAKANG FOR YUNQIAN STARTED 2015/05/22
        $yqresult = $this->_dao->getOne("select sys_yq_shenpi_status.result from sm_contract_transaction,sys_yq_shenpi_status where sys_yq_shenpi_status.dzcontract_id =sm_contract_transaction.ID and sys_yq_shenpi_status.Mid='" . $_SESSION['SYS_COMPANYID'] . "' ");

        // $this->assign("buycom", $buycom);
        // $this->assign("salecom", $salecom);
        //ADD BY XIAKANG FOR YUNQIAN ENDED 2015/06/15
        $this->assign("yqsp", $yqsp);
        //ADD BY XIAKANG FOR YUNQIAN ENDED 2015/05/29
        if ($zy['Did'] == $_SESSION['SYS_COMPANYID']) {
            $this->assign("myorder", 1);
        }

        $this->assign("State", $GLOBALS['CHINA_SHENG']);
        //买家
        if ($zy['SlType'] == "1" || $zy['SlType'] == "5") {  //销售类型订单
            $buycp = $this->_dao->get_sys_company($zy['Mid']);
            $sellcp = $this->_dao->get_sys_company($zy['Did']);
            $sellcp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '" . $zy['Did'] . "' and  IsMain = 1");
            $buycp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '" . $zy['Mid'] . "' and  IsMain = 1");
        } else { //采购类型订单
            $buycp = $this->_dao->get_sys_company($zy['Did']);
            $sellcp = $this->_dao->get_sys_company($zy['Mid']);

            $sellcp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '" . $zy['Mid'] . "' and  IsMain = 1");
            $buycp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '" . $zy['Did'] . "' and  IsMain = 1");
        }
        $this->assign("buycp", $buycp);
        $this->assign("sellcp", $sellcp);
        //卖家
        $zydetail = $this->_dao->query("select *,sm_exc_order_detail.ID as oid ,sm_exc_sales_details.Vid,sm_exc_sales_details.cd from sm_exc_order_detail,sm_exc_dd,sm_exc_sales_details  where sm_exc_order_detail.Ddid = sm_exc_dd.ID and sm_exc_sales_details.id=sm_exc_dd.sdid and   Dtid='" . $params['id'] . "' and sm_exc_order_detail.Status=1");

        // Updated for orderdetail by Zhu Dahua started  2015/09/11
        $Jsjgstr = $zydetail[0]["Jsjg"];
        if (strlen($Jsjgstr) > 0) {
            $Jsjgstr_arr = str_split($Jsjgstr);
            $Jsjgstr_flag = 0;
            foreach ($Jsjgstr_arr as $k => $v) {
                if ($v == '.') {
                    if ($k + 1 == strlen($Jsjgstr)) {
                        $Jsjgstr .= "00";
                        $Jsjgstr_flag = 1;
                        break;
                    }
                    if ($k + 1 == strlen($Jsjgstr) - 1) {
                        $Jsjgstr .= "0";
                        $Jsjgstr_flag = 1;
                        break;
                    }
                }
            }
            if ($Jsjgstr_flag == 0) {
                $Jsjgstr .= ".00";
            }
        }
        $zydetail[0]["Jsjg"] = $Jsjgstr;
        // Updated for orderdetail by Zhu Dahua ended  2015/09/11

        // Added for account by Zhu Dahua started  2015/09/15
        if ($zydetail[0]['Jsjg'] == "0" || $zydetail[0]['Jsjg'] == $zydetail[0]['XyPrice']) {
            $zy['JsTmoney'] = $zydetail[0]['XyPrice'] * $zydetail[0]['Shsl'];
        } else {
            $zy['JsTmoney'] = $zydetail[0]['Jsjg'] * $zydetail[0]['Shsl'];
        }
        $this->assign("zy", $zy);
        // Added for account by Zhu Dahua ended  2015/09/15

        $JsTmoney = 0;
        //added by hezp for 17511 started 2015/10/20
        $JsTweight = 0;
        //added by hezp for 17511 ended 2015/10/20
        foreach ($zydetail as &$tmp) {
            // Added for huadong gangshi  by hzp started  2015/03/05
            if ($tmp['Sid'] < "0") {
                $tmp['Sid'] = $tmp['Sid'] * (-1);
            }
            // Added for huadong gangshi  by hzp ended  2015/03/05
            if ($tmp['Jsjg'] == "0") {
                $JsTmoney = $JsTmoney + $tmp['XyPrice'] * $tmp['Shsl'];
                $tmp['Jsmoney'] = $tmp['XyPrice'] * $tmp['Shsl'];
            } else {
                $JsTmoney = $JsTmoney + $tmp['Jsjg'] * $tmp['Shsl'];
                $tmp['Jsmoney'] = $tmp['Jsjg'] * $tmp['Shsl'];
            }
            //added by hezp for 17511 started 2015/10/20
            $JsTweight = $JsTweight + $tmp['Shsl'];
            //added by hezp for 17511 ended 2015/10/20
        }
        $this->assign("JsTmoney", number_format($JsTmoney, 2));
        //added by hezp for 17511 started 2015/10/20
        $this->assign("JsTweight", $JsTweight);
        //added by hezp for 17511 ended 2015/10/20

        $this->assign("zydetail", $zydetail);

        $yscom = $this->_dao->Aquery("select ID,TransName  from sm_user_trans ");
        $this->assign("yscom", $yscom);

        $qtcost = $this->_dao->query("select * from sm_exc_order_cost  where Did='" . $params['id'] . "'");
        $this->assign("qtcost", $qtcost);
        $this->assign("page_title", "结算单_我的订单_");

        if ($params['id'] != "") {
            $err = $this->_dao->getRow("select * from sm_order_transaction where (Mid_Shipper = '" . $_SESSION['SYS_COMPANYID'] . "' or Mid_Consignee = '" . $_SESSION['SYS_COMPANYID'] . "' ) and BID = '" . $params['id'] . "'");
            if ($err) {
            } else {
                echo "error!";
                exit;
            }
        }

        //权限判断
        if ($_SESSION['SYS_USERROLE'] == "S") {
            goURL("member.php?view=orderinfodetails&tid=" . $params['id']);
            exit;
        }

        $pytd = $this->_dao->getOne("select pysametd from sys_company where ID='" . $_SESSION['SYS_COMPANYID'] . "'"); //交易密码是否同支付密码
        $this->assign("pytd", $pytd);

        $ht = $this->_dao->getRow("select * from sm_contract_transaction where BID = '" . $params['id'] . "'", 0);

        $id = $ht['BID'];
        $this->assign("ht", $ht);

        //判断是否需要加上其他费用价格
        if ($ht['Delivery'] == "2" && $ht['ysfy'] == "2") {
            $Allmoney = $JsTmoney + $zy['YsTmoney'];
        } else {
            $Allmoney = $JsTmoney;
        }
        $Allmoney = number_format($Allmoney, 2);
        $this->assign("Allmoney", $Allmoney);

        $this->assign("flagid", $id);

        $this->setvars();

        $this->assign("params", $params);

        //提单列表
        $tdlist = $this->_dao->query("select * from sm_contract_transaction_td where Tid ='" . $ht['ID'] . "'");
        $this->assign("tdlist", $tdlist);

        //订单追踪
        $orderfind = $this->_dao->query("select * from  sm_contract_transaction_status where TID = '" . $ht['ID'] . "' order by ID DESC");
        foreach ($orderfind as &$tmp) {
            $usname = $this->_dao->getOne("select ARealName from sys_adminuser where AUserName = '" . $tmp['CreateUser'] . "' limit 1", 0);
            $tmp['ARealName'] = $usname;
        }
        $this->assign("orderfind", $orderfind);

        //在线洽谈
        $huifu = array();
        $qiatan  = $this->_dao->query("select * from sys_message where OrderID = '" . $params['id'] . "' and ReciveID =0 order by ID ASC");
        foreach ($qiatan as &$val) {
            $huifu[$val['ID']] = $this->_dao->query("select * from sys_message where ReciveID = '" . $val['ID'] . "' order by ID ASC");
        }

        $this->assign("huifu", $huifu);
        $this->assign("qiatan", $qiatan);
    }

    public function jsxx($params)
    {
        $id = $params['Did'];

        $arrayfh2 = $this->_dao->getOnes("select ID from sm_exc_order where Did = '" . $id . "' ");
        $arrayfh2 = implode(",", $arrayfh2);
        $arrayfh = $this->_dao->query("select * from sm_exc_order_detail where Oid in ($arrayfh2) ");
        $Tmoney = 0;
        $money = 0;
        $num++;
        foreach ($arrayfh as $a) {
            $money = $params['Jsjg' . $a['ID']] * $a['Shsl'];
            $Tmoney =  $Tmoney + $money;
            $this->_dao->execute("update sm_exc_order_detail set Jsjg = '" . $params['Jsjg' . $a['ID']] . "',Jsmoney = '" . $money . "' where ID = '" . $a['ID'] . "' ");
            if ($params['Jsjg' . $a['ID']] != $a['Jsjg']) {
                $num++;
            }
        }

        if ($num > 0) {
            $this->_dao->execute("update sm_exc_dd_tag set IsChange=1 where ID ='" . $id . "' ");
        }

        $this->_dao->execute("update sm_exc_dd_tag set JsTmoney='" . $Tmoney . "' where ID ='" . $id . "' ");

        alert("保存成功");
        goURL("bizorder.php?view=account&id=" . $id);
    }


    public function qtfy($params)
    {
        $id = $params['Did'];

        $arrayfh = $this->_dao->query("select * from sm_exc_order_cost where Did = '" . $id . "' ");
        $Tmoney = 0;
        $money = 0;
        $num++;
        foreach ($arrayfh as $a) {
            $money = $params['Ysdj' . $a['ID']] * $a['Yssl'];
            $Tmoney =  $Tmoney + $money;
            $this->_dao->execute("update sm_exc_order_cost set Ysdj = '" . $params['Ysdj' . $a['ID']] . "',Ysmoney = '" . $money . "' where ID = '" . $a['ID'] . "' ");
            if ($params['Ysdj' . $a['ID']] != $a['Ysdj']) {
                $num++;
            }
        }

        if ($num > 0) {
            $this->_dao->execute("update sm_exc_dd_tag set IsChange=1 where ID ='" . $id . "' ");
        }

        $this->_dao->execute("update sm_exc_dd_tag set YsTmoney='" . $Tmoney . "' where ID ='" . $id . "' ");

        alert("保存成功");
        goURL("bizorder.php?view=account&id=" . $id);
    }

    //支付尾款
    public function zfwk($params)
    {
        $bsxmoney = "";
        $ssxmoney = "";
        $PayMid = "";

        /*$comp = $this->_dao->getRow( "SELECT * FROM sys_company WHERE ID='".$_SESSION['SYS_COMPANYID']."' AND TradePassWD=MD5( '$params[mima]' ) " );
        if( empty( $comp ) ){    
         echo "9";
         exit;
        }*/
        // Added for quanxian sheding  by hzp started  2014/12/25 
        $comp = $this->_dao->get_sys_company($_SESSION['SYS_COMPANYID']);
        if (empty($comp)) {

            //   echo "9";
            // exit;
        } else {
            if ($comp['pysametd'] == "0") {
                $comp1 = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $_SESSION['SYS_COMPANYID'] . "' AND PayPassWD=MD5( '$params[mima]' ) ");
                if (empty($comp1)) {
                    echo "9";
                    exit;
                }
            }

            if ($comp['pysametd'] == "1") {
                $comp2 = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $_SESSION['SYS_COMPANYID'] . "' AND TradePassWD=MD5( '$params[mima]' ) ");
                if (empty($comp2)) {
                    echo "9";
                    exit;
                }
            }
        }
        // Added for quanxian sheding  by hzp end  2014/12/25 

        $st = $this->_dao->getRow("select * from sm_contract_transaction where ID = " . $params['id'], 0);
        $OrderNo = $this->_dao->getOne("select OrderNo from sm_contract_transaction as t,sm_exc_dd_tag as d where t.BID=d.ID and t.ID='" . $params['id'] . "'");

        if ($_SESSION['SYS_COMPANYID'] == $st['Mid_Consignee']) {

            //add by xiakang for pingan started 2015/12/29
            $pingan = $this->_dao->getRow("select * from sm_pinganrongzi_contract_transcation where dzcontract_id='" . $st['ID'] . "' and ApprovalMode=2 and (ApprovalStatus1!=1 and ApprovalStatus2!=1 and ApprovalStatus2!=90)");
            if (!empty($pingan)) {
                if ($pingan['ApprovalStatus2'] == "30" && isset($params['isagree']) && isset($params['ApprovalRemark2'])) {
                    $ret = PinganRongZi::PinganFuKuan($st['Mid_Consignee'], $st['ID'], $pingan['ApprovalMode'], $params['isagree'], $params['ApprovalRemark2']);
                    if (!($ret) || $ret['Success'] == "-1") {
                        if ($params['isagree'] == "0") {
                            echo "1" . "|" . $ret['ErrorString']; //出错
                            exit;
                        }
                        if ($params['isagree'] == "1") {
                            //echo "4"."|".$ret['ErrorString'];//出错
                            echo "4" . "|" . $ret['ErrorCode'] . "|" . $ret['ErrorString']; //拒绝出错
                            exit;
                        }
                    }

                    if ($ret['Success'] == "0") {
                        if ($params['isagree'] == "0") {
                            $this->_dao->execute("UPDATE sm_pinganrongzi_contract_transcation SET ApprovalStatus2=2 where dzcontract_id='" . $st['ID'] . "' and ApprovalMode=2 and (ApprovalStatus1!=1 and ApprovalStatus2!=1 and ApprovalStatus2!=90)");
                            echo "2" . "|" . $ret['ErrorString']; //成功
                            exit;
                        }
                        if ($params['isagree'] == "1") {
                            $this->_dao->execute("UPDATE sm_pinganrongzi_contract_transcation SET ApprovalStatus2=3 where dzcontract_id='" . $st['ID'] . "' and (ApprovalStatus1!=1 and ApprovalStatus2!=1 and ApprovalStatus2!=90)");
                            //echo "3"."|".$ret['ErrorString'];//拒绝成功
                            echo "3" . "|" . $ret['ErrorCode'] . "|" . $ret['ErrorString']; //拒绝出错
                            exit;
                        }
                    }
                }
                exit;
            }
            //add by xiakang for pingan ended 2015/12/29

            if ($st['Status'] == "14") {

                $this->_dao->execute("UPDATE sm_contract_transaction SET  Status=15 WHERE ID=" . $params['id']);

                $this->xieZt(1, $params['id'], "确认支付尾款");
            }

            $this->_dao->execute("insert into sm_contract_transaction_status set TID='" . $params['id'] . "',StatusDesc='买方支付尾款',CreateDate=now(),CreateUser='$_SESSION[SYS_USERNAME]'");

            $zymobile = $this->_dao->get_sys_company($st['Mid_Shipper']);
            $temp = "订单:" . $OrderNo;
            $Message = Msg::setMsgFHtwkTemp($_SESSION['SYS_COMPABB'], $temp);

            $this->sendMsgFunc($_SESSION['SYS_COMPANYID'], $st['Mid_Shipper'], $Message, 1);

            //updated by hezp started 2016/04/14
            //$this->TempsendSmsFunc($st['Mid_Shipper'],$Message,12);
            if ($st['Mid_Shipper'] == $GLOBALS["HSHJ_MID"]) {
                $sellmobile = $this->hshjsenduser($arr['Mid_Consignee']);
            }
            $this->sendSmsFunc($st['Mid_Shipper'], $Message, $sellmobile['CreateUser'], 12);
            //updated by hezp ended 2016/04/14

            $this->sethtlog($st['SalesType'], "买方支付尾款", $temp, "买方支付尾款", $params['id']);

            //$sm_id = $this->_dao->getOne("SELECT LAST_INSERT_ID();",0);
            //交易状态表 快速查找
            echo "1";
        }

        if ($_SESSION['SYS_COMPANYID'] == $st['Mid_Shipper']) {

            if ($st['Status'] == "15") {
                $this->_dao->execute("UPDATE sm_contract_transaction SET  Status=10,SwkDate=now(),PayStatus=60 WHERE ID=" . $params['id']);
                $this->xieZt(1, $params['id'], "确认收尾款");
            }

            $this->_dao->execute("insert into sm_contract_transaction_status set TID='" . $params['id'] . "',StatusDesc='卖方确认收尾款',CreateDate=now(),CreateUser='$_SESSION[SYS_USERNAME]'");

            $zymobile = $this->_dao->get_sys_company($st['Mid_Consignee']);
            $temp = "订单:" . $OrderNo;

            $Message = Msg::setMsgSHtwkTemp($_SESSION['SYS_COMPABB'], $temp);
            $this->sendMsgFunc($_SESSION['SYS_COMPANYID'], $st['Mid_Consignee'], $Message, 1);
            $this->TempsendSmsFunc($st['Mid_Consignee'], $Message, 12);
            $this->sethtlog($st['SalesType'], "卖方确认收尾款", $temp, "卖方确认收尾款", $params['id']);

            echo "2";
        }
        //发送短消息

    }

    public function yscomcontact($params)
    {
        $aID = $params["id"];
        $s_userid = $_SESSION['SYS_USERID'];
        $s_comid = $_SESSION['SYS_COMPANYID'];
        $sql = "SELECT * FROM sm_user_trans where Mid ='" . $s_comid . "' and ID = '" . $aID . "'";
        $data = $this->_dao->getrow($sql, 0);
        //print_r($data);
        $array = array(
            'ID' => $data["ID"],
            'TransName' => $data["TransName"],
            'TransMan' => $data["TransMan"],
            'TransPhone' => $data["TransPhone"],

        );
        $this->_dao->jsonshow(1, $array, "");
    }

    // 关键字搜索 add by nicc 2014/5/16

    /*** 单个关键字获得 id **/
    public function getkeyid($keywd)
    {

        //先查别名表
        $sql_a = "SELECT `k_id` FROM `key_alias` WHERE `aname` LIKE BINARY '$keywd' limit 1 ";
        $k_id = $this->_dao->getOne($sql_a);

        if ($k_id > 0) {
            $sql = "SELECT `id`, `kname`, `ktype`, `parentid` FROM `biz_key` WHERE `id` ='$k_id' ";
            $arr = $this->_dao->getRow($sql);
            return $arr;
        } else {
            $sql = "SELECT `id`, `kname`, `ktype`, `parentid` FROM `biz_key` WHERE `kname` LIKE  BINARY  '$keywd' ";
            $arr = $this->_dao->getRow($sql);
            return $arr;
        }
    }

    /**钢厂 交货地 同名*/
    public function getlikecdjhdd($keywd)
    {
        $sql = "SELECT `ktype`  FROM `biz_key` WHERE `kname` = '$keywd'";
        $arr = $this->_dao->query($sql);
        if (count($arr) == 2) {
            for ($i = 0; $i < count($arr); $i++) {
                if ($arr[$i]["ktype"] == 3 || $arr[$i]["ktype"] == 4) {
                    return true;
                }
            }
        }
        return false;
    }

    /***********
     *  
     *  关键字检索  add by  nicc 2014/5/20
     *
     ************/

    public function getsqlbykeyword($keywords)
    {

        $sql = "";

        $keywords = trim($keywords);
        $keywords = eregi_replace("^ +| +$", "", $keywords); //去掉头尾的半角空格
        $keywords = eregi_replace("^　+|　+$", "", $keywords); //去掉头尾的全角空格
        $keywords = eregi_replace("　", " ", $keywords);       //将全角空格转换为半角空格
        $keywords = eregi_replace(" +", " ", $keywords);    //将一个或多个半角空格转换为指定的符号		

        $arr_keyword = explode(" ", trim($keywords));
        /**关键字计数**/
        $cd_num = 0;
        $jhdd_num = 0;
        $cd_jhdd_num_te = 0;

        foreach ($arr_keyword as $key => $value) {

            $keywd = trim($value);
            $arr = $this->getkeyid($keywd);

            $id = $arr["id"];
            $parentid = $arr["parentid"];
            $ktype = $arr["ktype"];

            if ($ktype == 3) {
                $cd_num++;
            }
            if ($ktype == 4) {
                $jhdd_num++;
            }

            $falg = $this->getlikecdjhdd($keywd);
            if ($falg) {
                $cd_jhdd_num_te++;
            }
        }

        /**SQL 匹配**/
        $count_pm = 0;
        $count_cz = 0;
        $count_cd = 0;
        $count_jhdd = 0;
        $count_gg = 0;
        foreach ($arr_keyword as $key => $value) {

            $keywd = trim($value);
            //$keywd = strtoupper($keywd);
            //echo $keywd."<br>";

            $arr = $this->getkeyid($keywd);

            $id = $arr["id"];
            $parentid = $arr["parentid"];
            $ktype = $arr["ktype"];

            if (empty($arr)) {
                // 判断是否公司名
                $sqlcom  = " and (sys_company.ComName like '%" . $keywd . "%' or ComNameShort like  '%" . $keywd . "%')";
            }

            //例如 日照
            $falg = $this->getlikecdjhdd($keywd);
            if ($falg &&  $count_cd == 0 && $count_jhdd == 0 && $cd_jhdd_num_te == 1 && $cd_num == 1 && $jhdd_num == 0) {

                $arr = $this->getkeyidbytype($keywd, 3);
                $id = $arr["id"];
                $parentid = $arr["parentid"];
                $sql = $sql . "  AND (( gca=$id  OR gcb=$id OR gcc=$id ) ";
                $count_cd = 1;

                $arr = $this->getkeyidbytype($keywd, 4);
                $id = $arr["id"];
                $parentid = $arr["parentid"];
                $sql = $sql . "  OR ( cda=$id  OR cdb=$id OR cdc=$id ) )  ";
                $count_jhdd = 1;
            }

            //例如 日照 宁波			
            if ($falg &&  $count_cd == 0 && $count_jhdd == 0 && $cd_jhdd_num_te == 1 && $cd_num == 1 && $jhdd_num == 1) {
                echo "tt";
                $arr = $this->getkeyidbytype($keywd, 3);
                $id = $arr["id"];
                $parentid = $arr["parentid"];
                $sql = $sql . "  AND ( gca=$id  OR gcb=$id OR gcc=$id) ";
                $count_cd = 1;
            }

            //例如 日照 宝钢 			
            if ($falg &&  $count_cd == 0 && $count_jhdd == 0 && $cd_jhdd_num_te == 1 && $cd_num == 2) {

                $arr = $this->getkeyidbytype($keywd, 4);
                $id0 = $arr["id"];
                $parentid0 = $arr["parentid"];
                $sql = $sql . "  AND ( cda=$id  OR cdb=$id OR cdc=$id)   ";
                $count_jhdd = 1;
            }

            //例如 日照 日照
            if ($falg &&  $count_cd == 0 && $count_jhdd == 0 && $cd_jhdd_num_te == 2) {

                $arr = $this->getkeyidbytype($keywd, 3);
                $id = $arr["id"];
                $parentid = $arr["parentid"];
                $sql = $sql . "  AND ( gca=$id  OR gcb=$id OR gcc=$id) ";
                $count_cd = 1;

                $arr = $this->getkeyidbytype($keywd, 4);
                $id = $arr["id"];
                $parentid = $arr["parentid"];
                $sql = $sql . "  AND ( cda=$id  OR cdb=$id OR cdc=$id)  ";
                $count_jhdd = 1;
            }

            //关键字查询
            if ($ktype == 1 && $count_pm == 0) {
                $sql = $sql . "  AND ( pm_id=$id OR pm_parentid=$id OR pm_ppid=$id ) ";
                $count_pm = 1;
            }
            if ($ktype == 2 && $count_cz == 0) {
                $sql = $sql . "  AND ( cz_id=$id  ) ";
                $count_cz = 1;
            }
            if ($ktype == 3 && $count_cd == 0) {
                $sql = $sql . "  AND ( gca=$id OR gcb=$id OR gcc=$id) ";
                $count_cd = 1;
            }
            if ($ktype == 4 && $count_jhdd == 0) {
                $sql = $sql . "  AND ( cda=$id  OR cdb=$id OR cdc=$id )  ";
                $count_jhdd = 1;
            }
        }
        return $sqlcom . $sql;
    }

    public function ordercancel($params)
    {
        // Added for quanxian sheding  by hzp started  2014/12/25
        $quanx = $this->_dao->getRow("SELECT childroot,IsMain,PowerLimit FROM sys_adminuser WHERE ID='" . $_SESSION['SYS_USERID'] . "'");
        $pos = strripos($quanx["childroot"], "1");
        $pos1 = strripos($quanx["childroot"], "4");

        if ($pos === false) {
            $pos = -1;
        }
        if ($pos1 === false) {
            $pos1 = -1;
        }

        if ($pos1 >= 0 || $pos >= 0 || $quanx["IsMain"] == 1) {
        } else {
            alert("您没有权限");
            echo '<script>window.close();</script>';
        }
        // Added for quanxian sheding  by hzp end  2014/12/25
        $this->setvars();

        $zy = $this->_dao->getRow("select * from sm_exc_dd_tag where ID='" . $params['id'] . "'");
        $this->assign("zy", $zy);

        $this->assign("State", $GLOBALS['CHINA_SHENG']);
        //买家
        if ($zy['SlType'] == "1" || $zy['SlType'] == "5") {  //销售类型订单
            $buycp = $this->_dao->get_sys_company($zy['Mid']);
            $sellcp = $this->_dao->get_sys_company($zy['Did']);
            $sellcp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '" . $zy['Did'] . "' and  IsMain = 1");
            $buycp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '" . $zy['Mid'] . "' and  IsMain = 1");
        } else { //采购类型订单
            $buycp = $this->_dao->get_sys_company($zy['Did']);
            $sellcp = $this->_dao->get_sys_company($zy['Mid']);
            $sellcp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '" . $zy['Mid'] . "' and  IsMain = 1");
            $buycp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '" . $zy['Did'] . "' and  IsMain = 1");
        }
        $this->assign("buycp", $buycp);
        $this->assign("sellcp", $sellcp);
    }

    public function ajaxaddorder($params) {}

    public function ajaxgetysfs($params)
    {
        //交货地址，到站，码头
        $jhinfo = array();
        $gsinfo = $this->_dao->get_sys_company($_SESSION['SYS_COMPANYID']);

        if ($params['id'] == 1) {
            $jhinfo2 = $this->_dao->getOnes("select Address from sm_user_address where Mid = '" . $_SESSION['SYS_COMPANYID'] . "' and  Address!=''");
            foreach ($jhinfo2 as $key => $val) {
                $jhinfo[$key]['Address'] = $val;
            }
        }

        if ($params['id'] == 2) {

            for ($i = 1; $i < 6; $i++) {
                if ($i == "1") { //echo "a1";
                    if ($gsinfo['Tieluz'] == '') {
                    } else {
                        $jhinfo[]['Address'] = $gsinfo['Tieluz'];
                    }
                } else {
                    if ($gsinfo['Tieluz' . $i] == '') {
                    } else {
                        $jhinfo[]['Address'] = $gsinfo['Tieluz' . $i];
                    }
                }
            }
        }

        if ($params['id'] == 3) {

            for ($i = 1; $i < 6; $i++) {
                if ($i == "1") { //echo "a1";
                    if ($gsinfo['Matou'] == '') {
                    } else {
                        $jhinfo[]['Address'] = $gsinfo['Matou'];
                    }
                } else {
                    if ($gsinfo['Matou' . $i] == '') {
                    } else {
                        $jhinfo[]['Address'] = $gsinfo['Matou' . $i];
                    }
                }
            }
        }
        echo   json_encode($jhinfo);
        exit;
    }

    function changed($bid)
    {
        $ht = $this->_dao->getRow("select * from sm_order_transaction where BID='" . $bid . "'", 0);
        if ($ht['Mid_Shipper'] == $_SESSION['SYS_COMPANYID']) {
            $type = 1;
        } else if ($ht['Mid_Consignee'] == $_SESSION['SYS_COMPANYID']) {
            $type = 2;
        }

        if ($type == "1") {
            $exist = $this->_dao->getRow("select * from sm_order_transaction_change where BID='" . $bid . "' and SalesStatus=1");
        } else {
            $exist = $this->_dao->getRow("select * from sm_order_transaction_change where BID='" . $bid . "' and BuyStatus=1");
        }
        if (!empty($exist)) {
            $this->_dao->execute("delete from sm_order_transaction_change where ID='" . $exist['ID'] . "'");
        }
        $this->_dao->execute("insert into  sm_order_transaction_change (`GUID`, `BID`, `SalesType`, `ContractNo`, `OrderNoBI`, `OrderNoSI`, `Mid_Shipper`, `Mid_Consignee`, `TotalWeight`, `TotalMoney`, `AssureTypeB`, `AssureGuarpayTypeB`, `AssureGuarpayMoneyB`, `AssureGuarpayMoneyTotalB`, `AssureTypeS`, `AssureGuarpayTypeS`, `AssureGuarpayMoneyS`, `AssureGuarpayMoneyTotalS`, `PickUpType`, `PickUpDate`, `PickUpState`, `PickUpCity`, `PickUpAddress`, `ShipperMan`, `ShipperPhone`, `ShipperMobile`, `ShipperFax`, `ShipperEmail`, `ShipperAddress`, `ShipperPostCode`, `ShipperQQNum`, `ShipperAddressState`, `ShipperAddressCity`, `ConsigneeMan`, `ConsigneePhone`, `ConsigneeMobile`, `ConsigneeFax`, `ConsigneeEmail`, `ConsigneeAddress`, `ConsigneePostCode`, `ConsigneeQQNum`, `ConsigneeAddressState`, `ConsigneeAddressCity`, `Mid_Transportation`, `TransMan`, `TransPhone`, `TransMobile`, `TransVehicleNo`, `IsConfirmBo`, `ConfirmBoDate`, `ConfirmBoUser`, `IsConfirmSo`, `ConfirmSoDate`, `ConfirmSoUser`, `IsConfirmBj`, `IsConfirmBjDate`, `IsConfirmBjUser`, `IsConfirmSj`, `IsConfirmSjDate`, `IsConfirmSjUser`, `PayType`, `zlbz`, `bzbz`, `ysfy`, `xgzs`, `hwsl`, `kjfp`, `fkqx`, `jhqx`, `fkxs`, `cdhp`, `txcd`, `bzjflag`, `lybzj`, `yqwyj`, `yqqx`, `fjtk`, `qdtime`, `qdaddress`, `sxflx`, `sxfzf`, `sxfzt`, `sxf`, `persxf`, `CreateDate`, `CreateUser`, `Status`, `IsKp`, `KpMoney`, `KpShui`, `TwoDimCode`, `kctime`, `txl`, `dwname`, `danwei`, `ShipperFhck`, `ShipperYsfs`, `ConsigneeDhck`, `ConsigneeYsfs`, `Delivery`, `ycfkwyj`, `jhbd`, `jhck`, `kpbank`, `DisputeSettlement`, `DisputeSettlement_city` , `contract_bd`, `yzf_Shipper`, `yzf_Consignee`) select  `GUID`, `BID`, `SalesType`, `ContractNo`, `OrderNoBI`, `OrderNoSI`, `Mid_Shipper`, `Mid_Consignee`, `TotalWeight`, `TotalMoney`, `AssureTypeB`, `AssureGuarpayTypeB`, `AssureGuarpayMoneyB`, `AssureGuarpayMoneyTotalB`, `AssureTypeS`, `AssureGuarpayTypeS`, `AssureGuarpayMoneyS`, `AssureGuarpayMoneyTotalS`, `PickUpType`, `PickUpDate`, `PickUpState`, `PickUpCity`, `PickUpAddress`, `ShipperMan`, `ShipperPhone`, `ShipperMobile`, `ShipperFax`, `ShipperEmail`, `ShipperAddress`, `ShipperPostCode`, `ShipperQQNum`, `ShipperAddressState`, `ShipperAddressCity`, `ConsigneeMan`, `ConsigneePhone`, `ConsigneeMobile`, `ConsigneeFax`, `ConsigneeEmail`, `ConsigneeAddress`, `ConsigneePostCode`, `ConsigneeQQNum`, `ConsigneeAddressState`, `ConsigneeAddressCity`, `Mid_Transportation`, `TransMan`, `TransPhone`, `TransMobile`, `TransVehicleNo`, `IsConfirmBo`, `ConfirmBoDate`, `ConfirmBoUser`, `IsConfirmSo`, `ConfirmSoDate`, `ConfirmSoUser`, `IsConfirmBj`, `IsConfirmBjDate`, `IsConfirmBjUser`, `IsConfirmSj`, `IsConfirmSjDate`, `IsConfirmSjUser`, `PayType`, `zlbz`, `bzbz`, `ysfy`, `xgzs`, `hwsl`, `kjfp`, `fkqx`, `jhqx`, `fkxs`, `cdhp`, `txcd`, `bzjflag`, `lybzj`, `yqwyj`, `yqqx`, `fjtk`, `qdtime`, `qdaddress`, `sxflx`, `sxfzf`, `sxfzt`, `sxf`, `persxf`, `CreateDate`, `CreateUser`, `Status`, `IsKp`, `KpMoney`, `KpShui`, `TwoDimCode`, `kctime`, `txl`, `dwname`, `danwei`, `ShipperFhck`, `ShipperYsfs`, `ConsigneeDhck`, `ConsigneeYsfs`, `Delivery`, `ycfkwyj`, `jhbd`, `jhck`, `kpbank`, `DisputeSettlement`, `DisputeSettlement_city`, `contract_bd`, `yzf_Shipper`, `yzf_Consignee` from sm_order_transaction where  BID = '" . $bid . "' ");
        //add 字段`DisputeSettlement`,`DisputeSettlement_city`,contract_bd by xiakang for pdf 2016/05/03
        $sm_id = $this->_dao->insert_id();
        if ($type == "1") {
            $this->_dao->execute("update sm_order_transaction_change set SalesStatus=1 where  ID = '" . $sm_id . "' ");
        } else {
            $this->_dao->execute("update sm_order_transaction_change set BuyStatus=1 where  ID = '" . $sm_id . "' ");
        }
    }

    public function payment($params)
    {

        // Added for quanxian shezhi  by hzp started  2014/12/25
        //用户信息
        $userinfo = $this->_dao->getRow("select * from sys_adminuser where ID = '" . $_SESSION['SYS_USERID'] . "'");
        $this->assign("userinfo", $userinfo);
        // Added for quanxian shezhi  by hzp end  2014/12/25
        $this->assign("status", $GLOBALS['Status']); //处理状态
        $this->assign("ddpaystatus", $GLOBALS['PayStatus']); //支付状态
        $this->assign("yfpaytype", $GLOBALS['Pay_Type']);
        $this->assign("FKFS", $GLOBALS['ZFKXS']);
        // added by quanjw start 2015/12/8
        $com = $this->_dao->get_sys_company($_SESSION['SYS_COMPANYID']); //交易密码是否同支付密码
        //$com['GroupID'] == "0" ? $comid = $com['ID']:$comid = $com['GroupID'];
        $canrz = in_array($_SESSION['SYS_COMPANYID'], $GLOBALS['canUsePinganRongziMid']);
        if (!$canrz) {
            $canrz = in_array($com['GroupID'], $GLOBALS['canUsePinganRongziMid']);
        }

        $this->assign("canrz", $canrz); //平安融资
        // added by quanjw end 2015/12/8

        //print_r($paction->asd123());
        $pzs2 = $this->_dao->AQuery("SELECT VarietyCode, VarietyName FROM sm_base_variety Where 1=1 ORDER BY VarietyType");
        $this->assign("pzs2", $pzs2);

        $pzs = $this->_dao->AQuery("SELECT VarietyCode, VarietyName FROM sm_base_variety_big Where 1=1 ORDER BY ID", 0);
        $this->assign("pzs", $pzs);

        $zy = $this->_dao->getRow("select * from sm_exc_dd_tag where ID='" . $params['id'] . "'");

        $tempTmoney = $zy['Tmoney'];
        $zy['Tmoney'] = number_format($zy['Tmoney'], 2);

        $this->assign("zy", $zy);

        $zy['Tmoney'] = $tempTmoney;

        $fbdate = $this->_dao->getOne("select sm_exc_sales.CreateDate from sm_exc_dd_tag,sm_exc_dd,sm_exc_sales where sm_exc_dd_tag.ID=sm_exc_dd.Dtid and sm_exc_dd.Sid=sm_exc_sales.ID and sm_exc_dd_tag.ID='" . $params['id'] . "'");
        $this->assign("fbdate", $fbdate);

        $qrdate = $this->_dao->getRow("select * from sm_contract_transaction where BID='" . $zy['ID'] . "' ");
        $this->assign("qrdate", $qrdate);
        if ($zy['Did'] == $_SESSION['SYS_COMPANYID']) {
            $this->assign("myorder", 1);
        }

        $this->assign("State", $GLOBALS['CHINA_SHENG']);
        //买家
        if ($zy['SlType'] == "1" || $zy['SlType'] == "5") {  //销售类型订单
            $buycp = $this->_dao->get_sys_company($zy['Mid']);
            $sellcp = $this->_dao->get_sys_company($zy['Did']);
            $sellcp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '" . $zy['Did'] . "' and  IsMain = 1");
            $buycp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '" . $zy['Mid'] . "' and  IsMain = 1");

            $zyfbflx = "1";
        } else { //采购类型订单
            $buycp = $this->_dao->get_sys_company($zy['Did']);
            $sellcp = $this->_dao->get_sys_company($zy['Mid']);

            $sellcp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '" . $zy['Mid'] . "' and  IsMain = 1");
            $buycp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '" . $zy['Did'] . "' and  IsMain = 1");

            $zyfbflx = "2";
        }
        $this->assign("zyfbflx", $zyfbflx);
        $this->assign("buycp", $buycp);
        $this->assign("sellcp", $sellcp);
        //卖家
        $zydetail = $this->_dao->query("select *,sm_exc_dd.pinming as a,sm_exc_dd.caizhi as b,sm_exc_dd.guige as c,sm_exc_dd.Factory as d,sm_exc_dd.ID as did,sm_exc_dd.PickUpDate as picdate from sm_exc_dd,sm_exc_dd_tag  where sm_exc_dd.Dtid=sm_exc_dd_tag.ID and Dtid='" . $params['id'] . "'");

        $zydetail2 = $this->_dao->query("select *,sm_exc_order_detail.ID as oid from sm_exc_order_detail,sm_exc_dd  where sm_exc_order_detail.Ddid = sm_exc_dd.ID  and   Dtid='" . $params['id'] . "' and sm_exc_order_detail.Status=1");


        $JsTmoney = 0;

        foreach ($zydetail2 as &$tmp) {
            if ($tmp['Jsjg'] == "0") {
                $JsTmoney = $JsTmoney + $tmp['XyPrice'] * $tmp['Shsl'];
                $tmp['Jsmoney'] = $tmp['XyPrice'] * $tmp['Shsl'];
            } else {
                $JsTmoney = $JsTmoney + $tmp['Jsjg'] * $tmp['Shsl'];
                $tmp['Jsmoney'] = $tmp['Jsjg'] * $tmp['Shsl'];
            }
        }

        //可议价处理
        $tongj = 0;
        $STmoney = 0;
        $XyTmoney = 0;
        foreach ($zydetail as $val) {
            //	if($val['Yijia'] == "1"){
            //	}else{
            if ($val['XyPrice'] != $val['PriceContention']) {
                $tongj++;
            }


            //	}
            $STmoney = $STmoney + $val['SalesMinPrice'] * $val['QuantitySales'];
            $XyTmoney = $XyTmoney + $val['XyPrice'] * $val['QuantitySales'];
        }
        $this->assign("STmoney", $STmoney);
        $this->assign("XyTmoney", $XyTmoney);
        $this->assign("tongj", $tongj);
        $this->assign("zydetail", $zydetail);

        $this->assign("page_title", "订单：" . $zy['OrderNo'] . "_我的订单_");

        if ($params['id'] != "") {
            $err = $this->_dao->getRow("select * from sm_order_transaction where (Mid_Shipper = '" . $_SESSION['SYS_COMPANYID'] . "' or Mid_Consignee = '" . $_SESSION['SYS_COMPANYID'] . "' ) and BID = '" . $params['id'] . "'");
            if ($err) {
            } else {
                echo "error!";
                exit;
            }
        }

        //权限判断
        if ($_SESSION['SYS_USERROLE'] == "S") {
            goURL("member.php?view=orderinfodetails&tid=" . $params['id']);
            exit;
        }

        $pytd = $this->_dao->getOne("select pysametd from sys_company where ID='" . $_SESSION['SYS_COMPANYID'] . "'"); //交易密码是否同支付密码
        $this->assign("pytd", $pytd);

        $ht2 = $this->_dao->getRow("select * from sm_contract_transaction where BID = '" . $params['id'] . "'", 0);
        $this->assign("ht2", $ht2);
        $ht = $this->_dao->getRow("select * from sm_order_transaction where BID = '" . $params['id'] . "'", 0);
        $phpsigncode = $GLOBALS['PHPSIGNCODE'];
        $signphpmd5 = md5($ht['BID'] . $phpsigncode);
        $this->assign("signphpmd5", $signphpmd5);

        if ($ht['Status'] == "5") {
            $ht = $this->_dao->getRow("select * from sm_contract_transaction where BID = '" . $params['id'] . "'", 0);
        }

        $ht['yfkje'] = $ht['lybzj'] * $zy['Tmoney'] / 100;

        if ($ht['Delivery'] == "2" && $ht['ysfy'] == "2") {
            $Allmoney = $JsTmoney;
            $ht['nextpay'] = ($Allmoney + $zy['YsTmoney']) - $ht['yfkje'];
            $ht['jszje'] = $Allmoney + $zy['YsTmoney'];
            /*
                echo "运输".$zy['YsTmoney'];
                echo "总金额".$Allmoney;
                echo "预付款".$ht['yfkje'];*/
        } else {
            $Allmoney = $JsTmoney;
            $ht['nextpay'] = $Allmoney - $ht['yfkje'];
            $ht['jszje'] = $Allmoney;
        }
        $this->assign("Allmoney", $Allmoney);
        $id = $ht['BID'];
        $ht['jszje'] = number_format($ht['jszje'], 2);
        $ht['yfkje'] = number_format($ht['yfkje'], 2);
        $ht['nextpay'] = number_format($ht['nextpay'], 2);
        //added by shizg for 尾款为0的处理started 2018/12/3
        if (abs(floatval($ht['nextpay'])) < 0.01) {
            $ht['nextpay'] = 0;
        }
        //added by shizg for 尾款为0的处理ended 2018/12/3
        $this->assign("jszje", $jszje);
        $this->assign("ht", $ht);
        $this->assign("sta", $sta);
        $this->assign("zt", $zt);
        $this->assign("flagid", $id);

        //$zydetail = $this->_dao->query("select * from sm_order_transaction_detail where TID = '".$ht['ID']."'");
        //$this->assign("zydetail",$zydetail);

        //ADD BY XIAKANG FOR YUNQIAN STARTED 2015/05/28
        $yqsp = $this->_dao->getRow("select * from sm_yq_contract_transcation where dzcontract_id = '" . $ht['ID'] . "'");
        $this->assign("yqsp", $yqsp);
        //ADD BY XIAKANG FOR YUNQIAN ENDED 2015/06/15

        //买方公司
        //update by xiakang for yunqian started 2015/07/09
        //采购方
        // $buycom = yqcontract::getShenpiType($ht['ID'], $ht['Mid_Consignee']);
        //供应方
        // $salecom = yqcontract::getShenpiType($ht['ID'], $ht['Mid_Shipper']);
        //ADD BY XIAKANG FOR YUNQIAN STARTED 2015/05/22
        $yqresult = $this->_dao->getOne("select sys_yq_shenpi_status.result from sm_contract_transaction,sys_yq_shenpi_status where sys_yq_shenpi_status.dzcontract_id =sm_contract_transaction.ID and sys_yq_shenpi_status.Mid='" . $_SESSION['SYS_COMPANYID'] . "' ");

        // $this->assign("buycom", $buycom);
        // $this->assign("salecom", $salecom);
        //ADD BY XIAKANG FOR YUNQIAN ENDED 2015/06/15
        $this->setvars();

        $this->assign("params", $params);

        //支付
        $orderpay = $this->_dao->query("select * from sm_order_payment where OrderNo = '" . $zy['OrderNo'] . "' and InterFaceType=11 ");
        $PayFaceTemp = $orderpay[0]['PayFace'];
        $this->assign("PayFaceTemp", $PayFaceTemp);
        $this->assign("orderpay", $orderpay);
        if (!empty($orderpay)) {
            $paystatus = $this->_dao->getOne("select Status from sm_order_payment where OrderNo = '" . $zy['OrderNo'] . "' and InterFaceType=11 ");
            $this->assign("paystatus", $paystatus);
            //$paystatus2 = $this->_dao->getOne("select Status from sm_order_paysettlement where OrderNo = '".$zy['OrderNo']."' and InterFaceType=11 ");
            //$this->assign("paystatus2",$paystatus2);

            $this->assign("exits", 1);
        }
        $orderpaymon = $this->_dao->getOne("select sum(PayField04) as money from sm_order_payment where OrderNo = '" . $zy['OrderNo'] . "'  and  PayStatus=40");

        $orderjspaymon = $this->_dao->getOne("select sum(PayField04) as money from sm_order_paysettlement where OrderNo = '" . $zy['OrderNo'] . "'  and InterFaceType=12 and  PayStatus=40");
        $jsbackpaymon = $this->_dao->getOne("select sum(PayField04) as money from sm_order_paysettlement where OrderNo = '" . $zy['OrderNo'] . "'  and InterFaceType=13 and  PayStatus=40");
        $this->assign("orderpaymon", $orderpaymon);
        $this->assign("orderjspaymon", $orderjspaymon);
        $this->assign("jsbackpaymon", $jsbackpaymon);

        //支付结算
        $orderjspay = $this->_dao->query("select * from sm_order_paysettlement where OrderNo = '" . $zy['OrderNo'] . "' and InterFaceType=12");
        $this->assign("orderjspay", $orderjspay);
        if (!empty($orderjspay)) {
            $this->assign("	", 1);
        }

        //支付结算退款
        $jsbackpay = $this->_dao->query("select * from sm_order_paysettlement where OrderNo = '" . $zy['OrderNo'] . "' and InterFaceType=13");
        $this->assign("jsbackpay", $jsbackpay);
        if (!empty($jsbackpay)) {
            $this->assign("exits3", 1);
        }

        $this->assign("PayStatus", $GLOBALS['PayStatus']);
        //支付结算退款申请
        $jsbackask = $this->_dao->query("select * from sm_order_paymentconfirm where OrderNo = '" . $zy['OrderNo'] . "'");
        $this->assign("jsbackask", $jsbackask);
        if (!empty($jsbackask)) {
            $this->assign("exits4", 1);
        }
        //$this->assign("Status",$GLOBALS['Status']);

        //确认付款申请表	
        $info3 = $this->_dao->query("SELECT * FROM sm_order_paysettlement  where OrderNo = '" . $zy['OrderNo'] . "'");

        $this->assign("info3", $info3);
        if (!empty($info3)) {
            $this->assign("exits5", 1);
        }

        $this->assign("Status", $GLOBALS['Status']);

        $info2 = $this->_dao->query("SELECT *,sopp.paystatus FROM sm_order_paysettlement as sopt,sm_order_payment as sopp where sopt.OrderNo=sopp.OrderNo and sopt.OrderNo = '" . $zy['OrderNo'] . "'");
        //echo $where;
        $this->assign("info2", $info2);

        //支付结算
        $orderjspay = $this->_dao->query("select * from sm_order_paysettlement where OrderNo = '" . $zy['OrderNo'] . "' and InterFaceType=12");
        $this->assign("orderjspay", $orderjspay);
        if (!empty($orderjspay)) {
            $this->assign("	", 1);
        }

        //支付结算退款
        $jsbackpay = $this->_dao->query("select * from sm_order_paysettlement where OrderNo = '" . $zy['OrderNo'] . "' and InterFaceType=13");
        $this->assign("jsbackpay", $jsbackpay);
        if (!empty($jsbackpay)) {
            $this->assign("exits3", 1);
        }

        //支付结算退款申请
        $info2 = $this->_dao->query("select * from sm_order_paymentconfirm where OrderNo = '" . $zy['OrderNo'] . "'");
        $this->assign("jsbackask", $jsbackask);
        if (!empty($jsbackask)) {
            $this->assign("exits2", 1);
        }
        $this->assign("info2", $info2);

        //平安融资支付
        if ($ht['Status'] == "5" || $ht['Status'] == "17") {
            $ApprovalMode = "1"; //预付款支付
        } else if ($ht['Status'] == "14") {
            $ApprovalMode = "2"; //尾款支付
        }
        $pingan = $this->_dao->getRow("select * from sm_pinganrongzi_contract_transcation where dzcontract_id='" . $ht['ID'] . "' and ApprovalMode='" . $ApprovalMode . "'  and (ApprovalStatus1!=1 and ApprovalStatus2!=1 and ApprovalStatus2!=90)");
        $this->assign("pingan", $pingan);
        //平安融资支付
    }
    // Added for zaixian zhifu  by xiakang started 2015/1/9
    public function getStatus($params)
    {
        $bid = $params['id'];
        $status = $this->_dao->getRow("Select * FROM sm_contract_transaction where 
		 BID='" . $bid . "'");
        echo $status['PayStatus'];
    }
    // Added for zaixian zhifu  by xiakang ended 2015/1/9

    public function zydetail($params)
    {
        //同类资源
        $citys = $this->_dao->AQuery("SELECT cityid as CityCode, cityname as CityName FROM city Where 1=1 ORDER BY id");
        $this->assign("citys", $citys);
        $zyrec = $this->_dao->query("select sm_exc_sales.*,sm_exc_sales_details.VarietyName,sm_exc_sales_details.MaterialCode,sm_exc_sales_details.SalesMinPrice from sm_exc_sales,sm_exc_sales_details where sm_exc_sales.ID= sm_exc_sales_details.Pid and SalesType ='" . $params['cat'] . "' order by CreateDate DESC limit 10", 0);
        $this->assign("zyrec", $zyrec);

        $company = $this->_dao->query("select * from sys_company where IsTj = 1 order by px,FirstSignDate DESC limit 10", 0);
        $this->assign("company", $company);


        if ($_SESSION['SYS_ROOT'] == "SA") {
        } else {
            $this->assign("isnotsq", 1);
        }
        $com = $this->_dao->get_sys_company($_SESSION['SYS_COMPANYID']);
        $this->assign("com", $com);
        //$zy = $this->_dao->getRow("select sm_exc_sales.*,sm_exc_sales_details.* left join sm_exc_sales_details on sm_exc_sales.ID=sm_exc_sales_details.Pid from sm_exc_sales where sm_exc_sales.ID = '".$params['id']."'");

        $zydetail = $this->_dao->getRow("SELECT sm_exc_sales_details.*,sm_exc_dd.*,sm_exc_sales.*, sys_company.ComName,sys_company.ComNameShort FROM sm_exc_sales,sys_company,sm_exc_sales_details,sm_exc_dd where sys_company.ID = sm_exc_sales.Mid and sm_exc_sales.ID=sm_exc_sales_details.Pid  and sm_exc_dd.sdid=sm_exc_sales_details.ID and sm_exc_dd.ID='" . $params['did'] . "'  and sm_exc_sales.ID='" . $params['id'] . "' ");

        $zydetail['Quantity'] =  $zydetail['QuantitySales'] - $zydetail['QuantitySalesed'];

        if (!$zydetail) {
            if ($params['cat'] == "5") {        //现货销售
                goURL("listing.php?view=sellview");
            }
            if ($params['cat'] == "6" || $params['cat'] == "16") {   //6远期协议销售  16采购
                goURL("listing.php?view=yqjy");
            }
            if ($params['cat'] == "1") {        //竞价销售
                goURL("listing.php?view=sellview");
            }
            if ($params['cat'] == "2") {        //竞价采购
                goURL("listing.php?view=buyview");
            }
            exit;
        }

        if ($zydetail['StoreType'] == 2) {
            goURL("index.php?view=jinjia_infotc&id=" . $zydetail['ID']);
            exit;
        }

        $this->assign("maxjy", $zydetail['QuantitySales'] - $zydetail['QuantitySalesed']);
        $gcs = $this->_dao->AQuery("SELECT OriginCode, OriginNameShort FROM sm_base_placeorigin");
        $pzs = $this->_dao->AQuery("SELECT VarietyCode, VarietyName FROM sm_base_variety Where 1=1 ORDER BY ID");
        $bpzs = $this->_dao->AQuery("SELECT VarietyCode, VarietyName FROM sm_base_variety_big Where 1=1 ORDER BY ID");
        $zmds = $this->_dao->AQuery("SELECT MID, StoreShortName FROM sm_sys_storecustom ORDER BY StoreOrder ");
        $citys = $this->_dao->AQuery("SELECT cityid as CityCode, cityname as CityName FROM city Where 1=1 ORDER BY id");
        $guige = $this->_dao->AQuery("SELECT SpecCode, SpecName FROM sm_base_specification  Where 1=1 ORDER BY ID");
        $caizhi = $this->_dao->AQuery("SELECT MaterialCode, MaterialName FROM sm_base_material  Where 1=1 ORDER BY ID");

        $this->assign("caizhi", $caizhi);
        $this->assign("guige", $guige);
        $this->assign("zmds", $zmds);
        $this->assign("pzs", $pzs);
        $this->assign("citys", $citys);
        $this->assign("gcs", $gcs);
        $this->assign("zy", $zydetail);
        $this->assign("zysaletypes", $GLOBALS['ZY_ALL_TYPES']);
        $this->assign("storetypes", $GLOBALS['STORE_TYPES']);
        $this->assign("danbaos", $GLOBALS['DANBAO_TYPES']);
        $this->assign("zytype", $GLOBALS['ZYTYPE']);
        $this->assign("jgtypes", $GLOBALS['JIAOGE_TYPES']);
        $this->assign("Delivery", $GLOBALS['DELIVERY']); //提货方式
        $this->assign("yijia", $GLOBALS['YIJIA']);
        $this->assign("PAYTYPE", $GLOBALS['PAYTYPE']);
        $this->assign("fbbzj", $GLOBALS['fbbzj']);
        $dzzx = $this->_dao->query("SELECT * FROM  `zixun_info` where jyzn='3'  ORDER BY createdate  DESC LIMIT 0 , 5 ");
        $this->assign("dzzx", $dzzx);
        $this->assign("jyzn", $GLOBALS['jyzn']);

        if ($zydetail['SalesType'] == "1") {
            $this->assign("url", "listing.php?view=sellview");
        }
        if ($zydetail['SalesType'] == "2") {
            $this->assign("url", "listing.php?view=buyview");
        }
        //if($zydetail['SalesType'] == "3"){$this->assign("url","listing.php?view=buyview");} //xhcg
        if ($zydetail['SalesType'] == "5") {
            $this->assign("url", "listing.php?view=sellview");
        }  //xhxs
        if ($zydetail['SalesType'] == "6") {
            $this->assign("url", "listing.php?view=buyview");
        }
        //if($zydetail['SalesType'] == "16"){$this->assign("url","listing.php?view=yqcg");}

        $this->assign("params", $params);
        $nowtime = date('Y-m-d H:i:s');
        $this->assign("nowtime", $nowtime);

        $flag = '';
        if ($zydetail['SalesStartDate'] > $nowtime) {
            $flag = "1";
        }
        if ($zydetail['SalesEndDate'] < $nowtime) {
            $flag = "2";
        }
        if ($zydetail['SalesStartDate'] < $nowtime && ($zydetail['SalesEndDate'] > $nowtime)) {
            $flag = "3";
        }
        $this->assign("flag", $flag);

        $this->assign("xyjgtypes", $GLOBALS['XYJG_TYPES']);

        $this->assign("ref", urlencode("index.php?view=jinjia_info&cat=" . $params['cat'] . "&id=" . $params['id'] . "&talk=1#foot"));

        //公司简介
        $gc = $this->_dao->getRow("select * from `sm_sys_storecustom` where Mid = '" . $zydetail['Mid'] . "' limit 1");
        $this->assign("gc", $gc);
        $StoreDesc = $this->turntextarea($gc['StoreDesc']);
        $ProDesc = $this->turntextarea($gc['ProDesc']);
        $this->assign("StoreDesc", $StoreDesc);
        $this->assign("ProDesc", $ProDesc);

        //诚信通
        $cxt = $this->_dao->getOne("select CxtNum1  from sys_adminuser where ComID = '" . $zydetail['Mid'] . "'");

        $level = "";
        if ($cxt <= 100) {
            $level = 1;
        }
        if ($cxt > 100 && $cxt <= 200) {
            $level = 2;
        }
        if ($cxt > 200 && $cxt <= 400) {
            $level = 3;
        }
        if ($cxt > 400 && $cxt <= 800) {
            $level = 4;
        }
        if ($cxt > 800 && $cxt <= 1600) {
            $level = 5;
        }
        if ($cxt > 1600 && $cxt <= 3200) {
            $level = 6;
        }
        if ($cxt > 3200 && $cxt <= 6400) {
            $level = 7;
        }
        if ($cxt > 6400 && $cxt <= 12800) {
            $level = 8;
        }

        $star = array(1, 2, 3, 4, 5);
        $this->assign("star", $star);
        $this->assign("level", $level);
        //added by shizg for gongsipingjia started 2017/01/10
        //公司评价分数
        $gspjxx = $this->_dao->getRow("select PingJiaZongFen1,PingJiaNum1,PingJiaZongFen2,PingJiaNum2 from sys_company where ID = '".(int)$zydetail['Mid']."'");
        if ($gspjxx['PingJiaNum1'] == 0) {
            $fenshu1 = 0;
        } else {
            $fenshu1 = round($gspjxx['PingJiaZongFen1'] / $gspjxx['PingJiaNum1'], 1);
        }
        $this->assign("fenshu1", $fenshu1);
        if ($gspjxx['PingJiaNum2'] == 0) {
            $fenshu2 = 0;
        } else {
            $fenshu2 = round($gspjxx['PingJiaZongFen2'] / $gspjxx['PingJiaNum2'], 1);
        }
        $this->assign("fenshu2", $fenshu2);
        //added by shizg for gongsipingjia ended 2017/01/10
        $this->assign("cxt", $cxt);

        $comp2 = $this->_dao->get_sys_company($zydetail['Mid']);
        $this->assign("comp2", $comp2);

        //判断此资源授权交易会员类型
        $empower = $this->_dao->getRow("select * from sm_own_member where ApplyMid ='" . $_SESSION['SYS_COMPANYID'] . "' and ReviewMid='" . $zydetail['Mid'] . "' ");

        $emm = "";

        if ($zydetail['EmpowerType'] == 2) {
            if ($empower['Status'] != 1) {
                $emm = 2;
                //$this->assign("empower","2");//专区交易会员
            }
        } elseif ($zydetail['EmpowerType'] == 3) {
            if ($comp2['GroupID'] != $com['GroupID'] || $comp2['GroupID'] == "0") {

                $emm = 3;
                //$this->assign("empower","3");//集团交易会员
            }
        } else {
            $emm = 1;
            //$this->assign("empower","1");
        }
        /*
        if($com['MemberType'] == "4"){
        
            if($zydetail['EmpowerType'] != "2" || $empower['Status']!=1){
                $emm = 4;
                //$this->assign("empower","4");
            }else{
                $emm = 1;
                //$this->assign("empower","1");
            }
        
        }
        */
        $this->assign("empower", $emm);
        $this->assign("page_title", $zydetail['VarietyName'] . "_" . $comp2['ComName'] . "_");

        //判断时间段 
        $sjd = $this->_dao->getRow("select * from sm_sys_storecustom where Mid = '" . $zydetail['Mid'] . "' and IsTime = 1 limit 1", 0);

        if ($sjd['IsWorkDay'] == "1") {
            $strd = "工作日";
        }

        if ($sjd['Mshour'] != "") {

            if ($sjd['Mshour'] != "") {
                $strd .= " 上午 " . $sjd['Mshour'] . ":" . $sjd['Msfen'] . " - " . $sjd['Mehour'] . ":" . $sjd['Mefen'];
            }
            if ($sjd['Ashour'] != "") {
                $strd .= " 下午 " . $sjd['Ashour'] . ":" . $sjd['Asfen'] . " - " . $sjd['Aehour'] . ":" . $sjd['Aefen'];
            }
            $this->assign("strd", $strd);
        }

        //获取自定义列
        $selfname = $this->_dao->query("select * from sm_user_sname where Mid = '" . $zydetail['Mid'] . "' and Status = 1");
        foreach ($selfname as &$tmp) {
            $conts = $this->_dao->getOne("select Conts from sm_exc_self where Zid = '" . $zydetail['ID'] . "' and Nid = '" . $tmp['ID'] . "' limit 1");
            if ($conts) {
                $tmp['conts'] = $conts;
            } else {

                unset($selfname);
            }
        }

        $this->assign("selfname", $selfname);
        //Added by xiakang for jinshuzhipin start 2015/07/16
        $this->assign("MEITAN_VID", $GLOBALS["MEITAN_VID"]);
        $this->assign("JIAOTAN_VID", $GLOBALS["JIAOTAN_VID"]);
        $this->assign("SHUINI_VID", $GLOBALS["SHUINI_VID"]);
        $this->assign("JINSHU_VID", $GLOBALS["JINSHU_VID"]);
        $this->assign("HW_VID", $GLOBALS["HW_VID"]);
        $this->assign("ZY_VID", $GLOBALS["ZY_VID"]);
        //Added by xiakang for jinshuzhipin end 2015/07/16

    }

    function turntextarea($arr)
    {
        return str_replace(Chr(13), "<br/>", str_replace(Chr(32), "&nbsp;", $arr));
    }

    public function cancelgwc2($params)
    {
        unset($_SESSION['mygwc'][$params['resid']]);
        echo $params['resid'];
        //goBack();
    }

    public function pl_del_gwc($params){
        $resids = $params["resids"];
        $resid_arr = explode(",",$resids);
        $type = $params["type"];
        foreach($resid_arr as $key=>$item){
            if($type == 1)
                unset( $_SESSION['mygwc_buy'][$item]);
            else
                unset( $_SESSION['mygwc_pro'][$item]);
        }
	    // unset( $_SESSION['mygwc'][$params['resid']] );
        // echo $params['resid'];
		goURL( "bizorder.php?view=shoppingcart&type=".$type );
    }

    public function rebackgwc($params){
        $_SESSION['mygwc'][$params['id']]['dddate'] = date("Y-m-d H:i:s", time());
    }

    //释放24小时内未确认订单
    public function  overdue($params)
    {
        $nowtime = date("Y-m-d H:i:s", time());
        $order = $this->_dao->query("select * from sm_exc_dd_tag  where Status=1", 0);
        foreach ($order as $val) {
            $orderdt = $this->_dao->query("select * from sm_exc_dd  where Dtid = '" . $val['ID'] . "'", 0);
            //$order['CreateDate']
            $hour = (strtotime($nowtime) - strtotime($val['CreateDate'])) / 3600;
            if ($hour > 24) {
                $this->_dao->execute("update sm_exc_dd_tag set Status=4 where ID='" . $val['ID'] . "' ");
                if ($val['SlType'] == "5") {
                    foreach ($orderdt as $v) {
                        $zl = $this->_dao->getOne("select QuantitySalesed from  sm_exc_sales_details  where ID='" . $v['sdid'] . "'");
                        $sy = $zl - $v['BuyQuantity'];
                        $this->_dao->execute("update sm_exc_sales_details set QuantitySalesed = '" . $v['sy'] . "' where ID='" . $v['sdid'] . "'");
                    }
                }
            }
        }
    }

    public function printsession($params)
    {
        echo APP_DIR . '<br>';
        echo    '用户名：' . $_SESSION['SYS_USERNAME'] . '<br>';
        echo    '公司名：' . $_SESSION['SYS_COMPANYNAME'] . '<br>';
        echo    '公司简称：' . $_SESSION['SYS_COMPABB'] . '<br>';
        echo    '公司ID：' . $_SESSION['SYS_COMPANYID'] . '<br>';
        echo    '用户ID：' . $_SESSION['SYS_USERID'] . '<br>';
        echo    '用户姓名：' . $_SESSION['SYS_TRUENAME'] . '<br>';
        echo    'SYS_USERROLE：' . $_SESSION['SYS_USERROLE'] . '<br>';
        echo    'SYS_USERIsLimit：' . $_SESSION['SYS_USERIsLimit'] . '<br>';
        echo    'SYS_ROOT：' . $_SESSION['SYS_ROOT'];
    }

    public function getranscode()
    {
        $transcode = time() . mt_rand(100, 999);
        return $transcode;
    }

    public function bjxiugai($params)
    {

        $id = $params['tagid'];
        $tagid = $params['tagid'];
        $tQuantity = array();
        $contact = $this->_dao->getRow("select * from sm_order_transaction where BID = '" . $id . "'");

        $buy = $this->_dao->query("select Sid,sdid from sm_exc_dd where Dtid='" . $tagid . "'");
        $salesA = array();
        foreach ($buy as $temp) {
            // Added for huadong gangshi  by hzp started  2015/02/27
            if ($temp['Sid'] < 0) {
                //虚资源更改做判断
                $flag_hdgs = 1;
                $temp['Sid'] = $temp['Sid'] * (-1);
            }
            //华东钢市，现货销售。B公司与华东钢市确认完虚订单，且支付完预付款。A公司在与华东钢市确认订单时，华东钢市无法修改单价及数量，保存后显示【购买数量超过库存量】。
            if ($contact['Mid_Consignee'] == $GLOBALS['HDGSMID'] && $contact['SalesType'] == "5") {
                $flag_hdgs = 1;
            }

            $zydetail2[$temp['sdid']] = $this->_dao->getRow("SELECT QuantitySales,QuantitySalesed FROM sm_exc_sales_details where id='" . $temp['sdid'] . "'");

            if ($flag_hdgs == 1) {
                $tempSid_hdgs = $temp['Sid'] * (-1);
                $zydetail2_hdgs[$temp['sdid']] = $this->_dao->getRow("SELECT sum(BuyQuantity) as QuantitySalesed FROM sm_exc_dd,sm_exc_dd_tag where sm_exc_dd_tag.ID = sm_exc_dd.Dtid and   sdid='" . $temp['sdid'] . "' and sm_exc_dd_tag.Status = 2 ");
            }

            if ($flag_hdgs == 1) {
                $tempSid_hdgs = $temp['Sid'] * (-1);
                $zydetail3[$temp['sdid']] = $this->_dao->getRow("select BuyQuantity from sm_exc_dd,sm_exc_sales_details,sm_exc_sales  where sm_exc_sales_details.Pid=sm_exc_sales.ID and sm_exc_dd.sdid=sm_exc_sales_details.ID and sm_exc_dd.sdid=" . $temp['sdid'] . " and Dtid='" . $tagid . "'");
            } else {
                $zydetail3[$temp['sdid']] = $this->_dao->getRow("select BuyQuantity from sm_exc_dd,sm_exc_sales_details,sm_exc_sales  where sm_exc_sales_details.ID=sm_exc_dd.sdid and sm_exc_dd.Sid=sm_exc_sales.ID and sm_exc_dd.sdid=" . $temp['sdid'] . " and Dtid='" . $tagid . "'"); //查询该用户购买数量
            }
            // Updated for huadong gangshi  by hzp end  2015/02/28

            // Updated for huadong gangshi  by hzp started  2015/02/28
            if ($flag_hdgs == 1) {
                //$zydetail2[$temp['Sid']]['Quantity']=  $zydetail2[$temp['Sid']]['QuantitySales'] - $zydetail2_hdgs[$temp['Sid']]['QuantitySalesed'];
                $zydetail2[$temp['sdid']]['Quantity'] =  $zydetail2[$temp['sdid']]['QuantitySales'] - $zydetail2_hdgs[$temp['sdid']]['QuantitySalesed'];
            } else {
                //$zydetail2[$temp['Sid']]['Quantity']=  $zydetail2[$temp['Sid']]['QuantitySales'] - $zydetail2[$temp['Sid']]['QuantitySalesed'];
                $zydetail2[$temp['sdid']]['Quantity'] =  $zydetail2[$temp['sdid']]['QuantitySales'] - $zydetail2[$temp['sdid']]['QuantitySalesed'];
            }
            // Updated for huadong gangshi  by hzp end  2015/02/28
            array_push($tQuantity, $zydetail2[$temp['sdid']]['QuantitySalesed'] - $zydetail3[$temp['sdid']]['BuyQuantity']);
            array_push($salesA, $zydetail2[$temp['sdid']]['Quantity']); //将剩余值写入数组~

        }

        // Updated for huadong gangshi  by hzp started  2015/02/28
        $zydetail = $this->_dao->query("select *,sm_exc_dd.ID as did,sm_exc_dd.CreateDate as dddate,sm_exc_sales.CreateDate as fbdate from sm_exc_dd,sm_exc_sales_details,sm_exc_sales  where sm_exc_sales_details.Pid=sm_exc_sales.ID and sm_exc_dd.sdid=sm_exc_sales_details.ID and   Dtid='" . $tagid . "'");
        // Updated for huadong gangshi  by hzp ended  2015/02/28
        $salesB = array();
        foreach ($zydetail as $v) {
            array_push($salesB, $params['sl' . $v['did']]); //将剩余值写入数组
        }

        //salesA 为库存剩余量
        //salesB 为前台购买量

        for ($i = 0; $i < sizeof($salesA); $i++) {
            if ($salesA[$i] < $salesB[$i]) {

                goURL("bizorder.php?view=orderdetail&flag=1&id=" . $tagid);
                break;
            } else {
                /*echo "销售总量减去该用户购买量".$tQuantity[$i]."<br />";
                echo "前台购买量".$salesB[$i]."<br />";*/
                $tQuantity[$i] += $salesB[$i];
                /*echo "当前总的购买量".$tQuantity[$i]."<br />";*/
            }
        }
        $tempSid = $this->_dao->getOnesArray("select Sid from sm_exc_dd where Dtid='" . $tagid . "'");

        //修改资源剩余量
        //for($i=0;$i<sizeof($salesA);$i++)
        //{
        //	$this->_dao->execute("UPDATE sm_exc_sales_details SET QuantitySalesed =".$tQuantity[$i]." where Pid=".$tempSid[$i]);
        //	/*echo "UPDATE sm_exc_sales_details SET QuantitySalesed =".$tQuantity[$i]." where ID=".$tempSid[$i];*/
        //}

        $num = 0;
        $tmoney = 0;
        $weight = 0;
        foreach ($zydetail as $v) {
            if (($v['PriceContention'] == $params['jg' . $v['did']]) && ($v['BuyQuantity'] == $params['sl' . $v['did']]) && ($v['PickUpDate'] == $params['jhrq' . $v['did']])  && ($v['Factory'] == $params['gc' . $v['did']])) {
            } else {
                $num++;
            }

            $this->_dao->execute("update sm_exc_dd set PriceContention = '" . $params['jg' . $v['did']] . "',BuyQuantity =  '" . $params['sl' . $v['did']] . "',PickUpDate='" . $params['jhrq' . $v['did']] . "',Factory='" . $params['gc' . $v['did']] . "'   where ID = '" . $v['did'] . "' ");

            $tmoney = $tmoney +    $params['jg' . $v['did']] * $params['sl' . $v['did']];
            $weight = $weight + $params['sl' . $v['did']];
            $this->_dao->execute("update sm_exc_sales set WeightPerOne = '".$params['dw'.$v['did']]."'   where ID = '".$v['salesID']."' ");
        }

        if ($num > 0) {

            $this->_dao->execute("update sm_exc_dd_tag set Tmoney = '" . $tmoney . "',Tweight =  '" . $weight . "'  where ID = '" . $tagid . "'  ");

            if ($contact['Mid_Shipper'] == $_SESSION['SYS_COMPANYID']) {
                $this->_dao->execute("update sm_order_transaction SET TotalWeight= '" . $weight . "',TotalMoney='" . $tmoney . "' where BID = '" . $id . "'");

                //add by xiakang for yunqian started 2015/10/30
                // $salecom = yqcontract::getShenpiType($contact['ID'], $contact['Mid_Shipper']);
                // if ($salecom['type'] != "0") {
                //     $this->_dao->execute("update sm_order_transaction SET shipper_shenpi_level_no= '-1' where BID = '" . $id . "'");
                // }
                //add by xiakang for yunqian ended 2015/10/30

            } //,IsConfirmBo=0
            if ($contact['Mid_Consignee'] == $_SESSION['SYS_COMPANYID']) {
                $this->_dao->execute("update sm_order_transaction SET  TotalWeight= '" . $weight . "',TotalMoney='" . $tmoney . "' where BID = '" . $id . "'");

                //add by xiakang for yunqian started 2015/10/30
                // $buycom = yqcontract::getShenpiType($contact['ID'], $contact['Mid_Consignee']);
                // if ($buycom['type'] != "0") {
                //     $this->_dao->execute("update sm_order_transaction SET consignee_shenpi_level_no= '-1' where BID = '" . $id . "' ");
                // }
                //add by xiakang for yunqian ended 2015/10/30

            } //,IsConfirmSo=0
        }

        goURL("bizorder.php?view=orderdetail&id=" . $tagid);
    }

    //added for jinshu zhipin by hzp Strarted 2015/03/19
    public function ajaxgetindexinfo_jszq($params)
    {

        $mid = $_SESSION['SYS_COMPANYID'];
        $params['gcm'] = $GLOBALS['JSGSMID'];
        $this->assign("mid", $params['gcm']);
        $params['comname'] =  URLdecode($params['comname']);
        $params['search'] =  URLdecode($params['search']);
        $params['city'] =  URLdecode($params['city']);
        $params['factory'] =  URLdecode($params['factory']);
        $params['variety'] =  URLdecode($params['variety2']);

        $where .= $this->getwhere("sm_exc_sales.TradeType", $params['TradeType'], EQUAL);
        if ($params['salestype'] == "1") {
            $where .= "	AND sm_exc_sales.SalesType in (1,5) ";
        } elseif ($params['salestype'] == "2") {
            $where .= "	AND sm_exc_sales.SalesType in (2,6) ";
        }
        //用途
        $where .= $this->getwhere("yongtu", $params['yongtu'], LIKE);
        //强度
        $where .= $this->getwhere("strength", $params['strength'], LIKE);
        //锌层重量
        $where .= $this->getwhere("xincengWeight", $params['xincengWeight'], LIKE);

        if ($params['search'] != '') {
            $searchsql = $this->getsqlbykeyword($params['search']);
            $where .= $searchsql;
            $str .= "&search=" . $params['search'];
        }

        $where .= $this->getwhere("VarietyName", $params['pm'], LIKE);
        $where .= $this->getwhere("PickUpCity", $params['city'], LIKE);
        $where .= $this->getwhere("OriginCode", $params['gc'], LIKE);
        $where .= $this->getwhere("MaterialCode", $params['caizhi'], LIKE);
        $where .= $this->getwhere("SpecCode", $params['guige'], LIKE);
        $where .= $this->getwhere("jhck", $params['jhck'], LIKE);


        $where .= $this->getwhere("SalesMinPrice", $params['jg1'], GREATER_THAN);
        $where .= $this->getwhere("SalesMinPrice", $params['jg2'], LESS_THAN);

        $where .= $this->getwhere("hd", $params['hd1'], GREATER_THAN);
        $where .= $this->getwhere("hd", $params['hd2'], LESS_THAN);

        $where .= $this->getwhere("kd", $params['kd1'], GREATER_THAN);
        $where .= $this->getwhere("kd", $params['kd2'], LESS_THAN);

        $where .= $this->getwhere("cd", $params['cd1'], GREATER_THAN);
        $where .= $this->getwhere("cd", $params['cd2'], LESS_THAN);

        //厂家
        $factory = $this->getNotNull($params['factory'], $params['factory1']);
        $where .= $this->getwhere("OriginCode", $factory, LIKE);
        //城市
        $city = $this->getNotNull($params['city'], $params['city1']);
        $where .= $this->getwhere("PickUpCity", $city, LIKE);

        //品种
        $city = $this->getNotNull($params['variety'], $params['variety1']);
        $where .= $this->getwhere("pm_parentid", $variety, EQUAL);

        //材质
        $materialcode = $this->getNotNull($params['materialcode'], $params['materialcode1']);
        $where .= $this->getwhere("MaterialCode", $materialcode, LIKE);

        //规格
        $size = $this->getNotNull($params['size'], $params['size1']);
        $where .= $this->getwhere("SpecCode", $size, LIKE);

        if ($params['sort'] == "1") {
            $where2 = " ORDER BY CreateDateOD DESC, sm_exc_sales_details.SalesMinPrice ,sm_exc_sales_details.zylevel DESC,sm_exc_sales.CreateDate DESC,";
        }
        if ($params['sort'] == "2" || $params['sort'] == '') {
            $where2 = " ORDER BY CreateDateOD DESC, sm_exc_sales_details.zylevel DESC ,sm_exc_sales.CreateDate DESC,";
        }

        //foreach($search as $k=>$v){
        //	$str .= "&".$k."=".$v;
        //}
        //$this->assign("str",$str);
        //$this->assign("search",$search);

        if ($params['sptype'] == "7") {
            $where .= " and  sm_exc_sales.SalesType =2 ";
        } elseif ($params['sptype'] == "8") {
            $where .= " and  sm_exc_sales.SalesType =5 ";
        }
        if ($params['sptype'] == "7" && $GLOBALS['JSGSMID'] != $_SESSION['SYS_COMPANYID']) {
            $where .= " and sm_exc_sales.Status in(2,8)  ";
        } else {
            $where .= " and sm_exc_sales.Status =2  ";
        }

        //获取大品种 数目
        $bigpz = $this->_dao->AQuery("select ID,PzName from sys_big_pz  where Status = 1  order by Ssort", 3600);
        $this->assign("bigpz", $bigpz);

        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 25;
        $start = ($page - 1) * $per;

        if ($GLOBALS['JSGSMID'] != $_SESSION['SYS_COMPANYID']) {
            $jszq_status = " and JSZQ_RESID_VIEW.Status=0 ";
        }

        $total = $this->_dao->getOne("SELECT COUNT(sm_exc_sales.ID) FROM sm_exc_sales,sm_exc_sales_details, sys_company,JSZQ_RESID_VIEW WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.ID = JSZQ_RESID_VIEW.ID and  ((JSZQ_RESID_VIEW.IsVirtual=1 {$jszq_status}) or (JSZQ_RESID_VIEW.IsVirtual=0 and JSZQ_RESID_VIEW.Status=2)) and sm_exc_sales_details.yongtu !=''  and sm_exc_sales.TradeType != 2 $where $keyserach  ",3600);

        $data = $this->_dao->query("SELECT DATE_FORMAT(sm_exc_sales.CreateDate,'%Y-%m-%d') as CreateDateOD,sm_exc_sales_details.*,sm_exc_sales.*, sys_company.ComNameShort,sys_company.ComName,(select ARealName from sys_adminuser where ComID = sys_company.ID  and IsMain=1 and Status=1 ) as LinkMan,(select QQNum from sys_adminuser where ComID = sys_company.ID  and IsMain=1 and Status=1 ) as QQNum ,sys_company.ContactTel,sys_company.Address FROM sm_exc_sales_details, sys_company,sm_exc_sales force index(PRIMARY),JSZQ_RESID_VIEW WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID  AND sm_exc_sales.ID = JSZQ_RESID_VIEW.ID  and  ((JSZQ_RESID_VIEW.IsVirtual=1 {$jszq_status}) or (JSZQ_RESID_VIEW.IsVirtual=0 and JSZQ_RESID_VIEW.Status=2)) and sm_exc_sales_details.yongtu !=''  and sm_exc_sales.TradeType != 2  $where   $where2 sm_exc_sales.ID DESC LIMIT $start, $per");

        $totalpage = ceil($total / $per);
        //----------------组织数据开始
        $record = array();

        foreach ($data as &$d) {
            //资源购供图标
            $zy_flag = $this->zy_flag($d['SalesStartDate'], $d['SalesEndDate'], $d['SalesType']);
            $d['flag'] = $zy_flag['flag'];
            $d['salestp'] = $zy_flag['salestp'];
            //add by xiakang started 2015/03/18
            if ($_SESSION['SYS_COMPANYID'] == '') {
                $res =  hidtel($d['ContactTel'], $_SESSION['SYS_COMPANYID']);
                $d['ContactTel'] = $res;
            }
            //add by xiakang ended 2015/03/18
            if ($params['sptype'] == "8") {
                if ($d['Mid'] != $GLOBALS['JSGSMID'] && $mid != $GLOBALS['JSGSMID']) {
                    $C1 = $this->_dao->getOne("SELECT C1 FROM sm_diff_price WHERE  Mid='" . $d['Mid'] . "' and Pid='" . $GLOBALS['JSGSMID'] . "' ");
                    $tmp['SalesMinPrice_gs'] = $tmp['SalesMinPrice'] + $C1;
                } else if ($d['Mid'] != $GLOBALS['JSGSMID'] && $mid == $GLOBALS['JSGSMID']) {
                    $C2 = $this->_dao->getOne("SELECT C2 FROM sm_diff_price WHERE  Mid='" . $d['Mid'] . "' and Pid='" . $GLOBALS['JSGSMID'] . "'");
                    $d['SalesMinPrice_gs'] = $d['SalesMinPrice'] - $C2;
                } else {
                    $d['SalesMinPrice_gs'] = $d['SalesMinPrice'];
                }
            } else if ($params['sptype'] == "7") {
                if ($d['Mid'] != $GLOBALS['JSGSMID'] && $mid != $GLOBALS['JSGSMID']) {
                    $C3 = $this->_dao->getOne("SELECT C3 FROM sm_diff_price WHERE  Mid='" . $d['Mid'] . "' and Pid='" . $GLOBALS['JSGSMID'] . "'");
                    $d['SalesMinPrice_gs'] = $d['SalesMinPrice'] - $C3;
                } else if ($d['Mid'] != $GLOBALS['JSGSMID'] && $mid == $GLOBALS['JSGSMID']) {
                    $C4 = $this->_dao->getOne("SELECT C4 FROM sm_diff_price WHERE  Mid='" . $d['Mid'] . "' and Pid='" . $GLOBALS['JSGSMID'] . "'");
                    $d['SalesMinPrice_gs'] = $d['SalesMinPrice'] + $C4;
                } else {
                    $d['SalesMinPrice_gs'] = $d['SalesMinPrice'];
                }

                if ($mid != $GLOBALS['JSGSMID'] && $d['Mid'] != $GLOBALS['JSGSMID']) {

                    $hdgsSID = $d['ID'] * (-1);

                    $C = $this->_dao->getOne("SELECT SUM(BuyQuantity) as c FROM sm_exc_dd,sm_exc_dd_tag WHERE sm_exc_dd_tag.ID = sm_exc_dd.Dtid and  Sid='" . $hdgsSID . "' and sm_exc_dd_tag.Did='" . $GLOBALS['JSGSMID'] . "' and sm_exc_dd_tag.Status=2 ");
                    $CC = $this->_dao->getOne("SELECT SUM(BuyQuantity) as c FROM sm_exc_dd,sm_exc_dd_tag WHERE sm_exc_dd_tag.ID = sm_exc_dd.Dtid and  Sid='" . $d['ID'] . "'  and sm_exc_dd_tag.Did != '" . $GLOBALS['JSGSMID'] . "' and sm_exc_dd_tag.Mid != '" . $GLOBALS['JSGSMID'] . "' and sm_exc_dd_tag.Status=2 ");
                    if ($C == null) {
                        $C = 0;
                    }
                    if ($CC == null) {
                        $CC = 0;
                    }
                    $d['QuantitySalesed'] = $C + $CC;
                }
            }
        }
        $html_str = '';
        for ($i = 0; $i < count($data); $i++) {
            $index = $i + 1;
            $html_str = $html_str . "<tr><td align=\"center\"><span id=\"jjcg2_xuanz_$index\" ><input type=\"checkbox\"  name=\"res_id[]\" type=\"checkbox\" value=\"{$data[$i]['ID']}\"/></span></td>";

            $VarietyName = substr($data[$i]['VarietyName'], 0, 12);

            $html_str = $html_str . "<td><span id=\"jjcg2_VarietyName_$index\" ><a href=\"metal.php?view=jinjia_info_jszp&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">$VarietyName</a></span></td>";

            $SpecCode = substr($data[$i]['SpecCode'], 0, 6);
            $html_str = $html_str . "<td><span id=\"jjcg2_SpecCode_$index\" ><a href=\"metal.php?view=jinjia_info_jszp&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">$SpecCode</a></span></td>";

            $strength = substr($data[$i]['strength'], 0, 10);
            $html_str = $html_str . "<td><span id=\"jjcg2_strength_$index\" ><a href=\"metal.php?view=jinjia_info_jszp&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">$strength</a></span></td>";

            $xincengWeight = substr($data[$i]['xincengWeight'], 0, 10);
            $html_str = $html_str . "<td><span id=\"jjcg2_xincengWeight_$index\" ><a href=\"metal.php?view=jinjia_info_jszp&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">$xincengWeight</a></span></td>";

            if ($mid == $GLOBALS['JSGSMID']) {
                $html_str = $html_str . "<td><span id=\"jjcg2_SalesMinPrice_$index\" ><a href=\"metal.php?view=jinjia_info_jszp&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">";
                if ($pzzy[0][$i]['SalesMinPrice'] != '0.00' && $data[$i]['SalesMinPrice'] != "") {
                    $html_str = $html_str . "{$data[$i]['SalesMinPrice']}/{$data[$i]['SalesMinPrice_gs']}</a></span></td>";
                } else {
                    $html_str = $html_str . "协议价</a></span></td>";
                };
            } else {
                $html_str = $html_str . "<td><span id=\"jjcg2_SalesMinPrice_$index\" ><a href=\"metal.php?view=jinjia_info_jszp&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">";
                if ($pzzy[0][$i]['SalesMinPrice'] != '0.00' && $data[$i]['SalesMinPrice'] != "") {
                    $html_str = $html_str . "{$data[$i]['SalesMinPrice_gs']}</a></span></td>";
                } else {
                    $html_str = $html_str . "协议价</a></span></td>";
                };
            }
            $html_str = $html_str . "<td align=\"center\"><span id=\"jjcg2_QuantitySales_$index\" ><a href=\"metal.php?view=jinjia_info_jszp&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">{$data[$i]['QuantitySalesed']}/{$data[$i]['QuantitySales']}</a></span></td>";

            $OriginCode = substr($data[$i]['OriginCode'], 0, 12);
            $html_str = $html_str . "<td><span id=\"jjcg2_OriginCode_$index\" ><a href=\"metal.php?view=jinjia_info_jszp&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">$OriginCode</a></span></td>";

            $html_str = $html_str . "<td><span id=\"jjcg2_PickUpCity_$index\" ><a href=\"metal.php?view=jinjia_info_jszp&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">{$data[$i]['PickUpCity']}</a></span></td>";

            $CreateDate = date("Y-m-d", strtotime($data[$i]['CreateDate']));
            $html_str = $html_str . "<td><span id=\"jjcg2_CreateDate_$index\" ><a href=\"metal.php?view=jinjia_info_jszp&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\">$CreateDate</a></span></td>";
            $html_str = $html_str . "<td><span id=\"jjcg2_contact_$index\" ><a href=\"javascript:checkHasAndChatTo('<{$data[$i]['CreateUser']}>')\"><img class=\"img_<{$data[$i]['CreateUser']}>\" id=\"img_<{$data[$i]['CreateUser']}>\" src=\"images/gmt4.gif\" style=\"border:0;\" ></a>";
            if ($data[$i]['QQNum'] != "") {
                $html_str = $html_str . "<a href=\"//wpa.qq.com/msgrd?v=3&uin={$data[$i]['QQNum']}&site=qq&menu=yes\" target=\"_blank\"><img src=\"img/talk_qq.png\" /></a></span></td>";
            } else {
                $html_str = $html_str . "</span></td>";
            }

            $html_str = $html_str . "<td><span id=\"jjcg2_liuyan_$index\" ><a  href=\"metal.php?view=jinjia_info_jszp&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}&talk=1\"  target=\"_blank\">留言</a></span></td>";
            if ($data[$i]['flag'] == 1 && $data[$i]['salestp'] == 1) {
                $html_str = $html_str . "<td><a href=\"metal.php?view=jinjia_info_jszp&cat={$data[$i]['SalesType']}&id={$data[$i]['ID']}\" target=\"_blank\"><img  src=\"images/buy.jpg\" style=\"border:0;\" ></a></td></tr>";
            }
            if ($data[$i]['flag'] == 2 && $data[$i]['salestp'] == 1) {
                $html_str = $html_str . "<td><img  src=\"images/buy2.jpg\" style=\"border:0;\" ></td></tr>";
            }
            if ($data[$i]['flag'] == 1 && $data[$i]['salestp'] == 2) {
                $html_str = $html_str . "<td><a href=\"metal.php?view=jinjia_info_jszp&cat={$pzzy[0][$i]['SalesType']}&id={$pzzy[0][$i]['ID']}\" target=\"_blank\"><img  src=\"images/sales.jpg\" style=\"border:0;\" ></a></td></tr>";
            }
            if ($data[$i]['flag'] == 2 && $data[$i]['salestp'] == 2) {
                $html_str = $html_str . "<td><img  src=\"images/sales2.jpg\" style=\"border:0;\" >
            </td></tr>";
            }
        }
        $html_str = $html_str . "~" . $page;
        echo $html_str;
    }
    //added for jinshu zhipin bu hzp ended 2015/03/19
    // Added for yunqian  by tuxw started  2015/07/01
    public function saveNextShenpiUser($dzcontract_id, $Mid, $level, $No)
    {

        $noshenpi = $this->_dao->getOne("select dzcontract_id from sys_yq_shenpi_status where dzcontract_id='" . $dzcontract_id . "' and Mid='" . $Mid . "' and result=0");
        $noqianshu = $this->_dao->getOne("select dzcontract_id from sys_yq_qianshu_status where dzcontract_id='" . $dzcontract_id . "' and Mid='" . $Mid . "'");
        if ($noshenpi != null || $noqianshu != null) {
            return null;
        }

        //判断是否还有未审批人
        //update by xiakang for yunqian started 2015/10/26

        $contractlc = $this->_dao->getRow("select Mid_Shipper,Mid_Consignee,shipper_shenpi_level_no,consignee_shenpi_level_no from sm_yq_contract_transcation where dzcontract_id='" . $dzcontract_id . "' ");
        //$xuanzelc="";
        if ($Mid == $contractlc['Mid_Shipper']) {
            $xuanzelc = $contractlc['shipper_shenpi_level_no'];
        }
        if ($Mid == $contractlc['Mid_Consignee']) {
            $xuanzelc = $contractlc['consignee_shenpi_level_no'];
        }


        $No3 = $No + 1;
        for ($i = 0; $i < 50; $i++) {
            $No1 = $No - $i;
            $shenpi_userid = $this->_dao->getOne("select userid from sys_yq_shenpi_status where dzcontract_id='" . $dzcontract_id . "' and Mid='" . $Mid . "' 
                and level='" . $level . "' and No='" . $No1 . "' and level_no='" . $xuanzelc . "' and result=1");
            $shenpi_userid2 = $this->_dao->getOnes("select userid from sys_yq_shenpi_status where dzcontract_id='" . $dzcontract_id . "' and Mid='" . $Mid . "' 
                and level='" . $level . "' and level_no='" . $xuanzelc . "' and result=1");

            //add by xiakang started 2016/04/29
            $other_userid = array();
            $other_userid2 = array();
            $first_other = "";
            foreach ($shenpi_userid2 as $z) {
                $first_no = $this->_dao->getOne("select No from sys_yq_shenpi_liucheng where level='" . $level . "' and Mid='" . $Mid . "' and userid='" . $z . "' and level_no='" . $xuanzelc . "' ");

                $shenpi_userid3 = implode(',', $shenpi_userid2);
                if ($first_no != "") {
                    if ($shenpi_userid2 != "") {
                        $other_userid[] = $this->_dao->getOnes("select userid from sys_yq_shenpi_liucheng where Mid='" . $Mid . "' and level='" . $level . "' and No='" . $first_no . "' and level_no='" . $xuanzelc . "' and userid not in($shenpi_userid3) order by No ASC");
                    }
                }
            }
            if ($other_userid != "") {
                foreach ($other_userid as $k => $v) {
                    foreach ($v as $k2 => $v2) {
                        $other_userid2[] = $v2;
                    }
                }
            }
            $other_userid3 = implode(',', $other_userid2);

            $shenpi_userid2 = implode(',', $shenpi_userid2);

            if ($other_userid3 != "") {
                $frist_other = $shenpi_userid2 . "," . $other_userid3;
            } else {
                $frist_other = $shenpi_userid2;
            }
            //add by xiakang ended 2016/04/29
            $shenpi_userid_no = $this->_dao->getOne("select No from sys_yq_shenpi_liucheng where Mid='" . $Mid . "' and level='" . $level . "' and userid='" . $shenpi_userid . "' and level_no='" . $xuanzelc . "' ");

            $No2 = $shenpi_userid_no + 1;
            if ($shenpi_userid_no != "") {
                $next_useridAndUmid = $this->_dao->query("select userid,Umid from sys_yq_shenpi_liucheng where Mid='" . $Mid . "' and level='" . $level . "' and No='" . $No2 . "' and level_no='" . $xuanzelc . "' and userid not in($frist_other) order by No ASC");
                if ($next_useridAndUmid != null) {
                    break;
                }
            } else if ($shenpi_userid_no == "" && $No1 == "0") {
                $next_useridAndUmid = $this->_dao->query("select userid,Umid from sys_yq_shenpi_liucheng where Mid='" . $Mid . "' and level='" . $level . "' and No>=0 and level_no='" . $xuanzelc . "' and userid not in($frist_other) order by No ASC");
                break;
            }
        }


        if ($next_useridAndUmid != null) {

            foreach ($next_useridAndUmid as $tmp) {
                $this->_dao->execute("insert into sys_yq_shenpi_status set 
                    dzcontract_id='" . $dzcontract_id . "',Mid='" . $Mid . "',level='" . $level . "',
                    shenpi_status=0,No='" . $No3 . "',Umid='" . $tmp['Umid'] . "',
                    userid='" . $tmp['userid'] . "',result=0,level_no='" . $xuanzelc . "' ");


                //审批有结果发短信通知下一个人
                $BID = $this->_dao->getOne("select BID from sm_contract_transaction where ID='" . $dzcontract_id . "'");
                $ContractNo = $this->_dao->getOne("select OrderNo from sm_exc_dd_tag where ID='" . $BID . "'");
                $mobile = $this->_dao->getRow("select sys_adminuser.yq_telnumber as mobile,sys_company.ComName from sys_adminuser,sys_company where sys_adminuser.ComID = sys_company.ID and sys_adminuser.ID ='" . $tmp['userid'] . "'");
                $smscontent = $mobile['ComName'] . "的" . $ContractNo . "合同需要审批，请您登录中国大宗物资网进行审批";
                $data = array('mobile' => $mobile['mobile'], 'smscontent' => $smscontent);

                $this->yqspsend($data);
            }
        } else {
            //如果没有未审批人，增加未签署记录
            //$qianshu_userid = $this->_dao->getOne("select userid from sys_yq_qianshu where level='".$level."' and Mid='".$Mid."'");
            //updated by hezp for yunqian started 2015/11/02
            //签署人员在创建合同时已发给云签，所以应用云签合同表中的数据
            //$qianshu_userid = $this->_dao->getOne("select userid from sys_yq_qianshu where level='".$level."' and Mid='".$Mid."' and level_no='".$xuanzelc."' ");
            $qianshu = $this->_dao->getRow("select Mid_Shipper,Mid_Consignee,shipper_userid,consignee_userid  from sm_yq_contract_transcation where dzcontract_id='" . $dzcontract_id . "' ");
            if ($qianshu['Mid_Shipper'] == $Mid) {
                $qianshu_userid = $qianshu['shipper_userid'];
            } else {
                $qianshu_userid = $qianshu['consignee_userid'];
            }
            //updated by hezp for yunqian ended 2015/11/02
            if ($qianshu_userid == null) {
                $qianshu_userid = $this->_dao->getOne("select ID from sys_adminuser where ComID='" . $Mid . "' and IsMain=1");
            }
            $this->_dao->execute("insert into sys_yq_qianshu_status set dzcontract_id='" . $dzcontract_id . "' , Mid='" . $Mid . "' , userid='" . $qianshu_userid . "'");

            //added by hzp for yunqian duanxin started 2015/07/20
            //审批有结果发短信通知签署人
            $BID = $this->_dao->getOne("select BID from sm_contract_transaction where ID='" . $dzcontract_id . "'");
            $ContractNo = $this->_dao->getOne("select OrderNo from sm_exc_dd_tag where ID='" . $BID . "'");
            $mobile = $this->_dao->getRow("select sys_adminuser.yq_telnumber as mobile,sys_company.ComName from sys_adminuser,sys_company where sys_adminuser.ComID = sys_company.ID and sys_adminuser.ID ='" . $qianshu_userid . "'");

            $smscontent = $mobile['ComName'] . "的" . $ContractNo . "合同审批完成，请您登录中国大宗物资网进行签署";
            $data = array('mobile' => $mobile['mobile'], 'smscontent' => $smscontent);

            $this->yqspsend($data);

            //added by hzp for yunqian duanxin ended 2015/07/20

        }
    }
    // Added for yunqian  by tuxw ended  2015/07/01
    //added by hezp for ksdj started 2015/09/07
    public function ksdj_paytype($params)
    {

        $contact = $this->_dao->getRow("select SalesType,Mid_Shipper from sm_order_transaction where BID = '" . $params['id'] . "'");

        if ((in_array($_SESSION['SYS_COMPANYID'], $GLOBALS["JITUANID"]) || in_array($_SESSION['SYS_GroupID'], $GLOBALS["JITUANID"])) && $contact['SalesType'] == '8') {
            //集团供货商等级
            $Level = Partner::getPartnerLevel($contact['Mid_Shipper']);

            if ($Level == 'C' && $params['intHot'] == '1') {
                echo "1";
            }
        }
    }
    //added by hezp for ksdj ended 2015/09/07
    //added by tuxw for parz started 20151207
    public function rzorder($params)
    {
        $sta = $params['sta'];
        $compid = $_SESSION['SYS_COMPANYID'];
        $userid = $_SESSION['SYS_USERID'];

        $canViewPinganRongZiUser = "";
        foreach ($GLOBALS['canViewPinganRongZiUser'] as $k => $v) {
            foreach ($v as $k2 => $v2) {
                if ($v2 == $userid) {
                    //$canViewPinganRongZiUser=$k;
                    if ($canViewPinganRongZiUser == "") {
                        $canViewPinganRongZiUser += $k;
                    } else {
                        $canViewPinganRongZiUser += "," . $k;
                    }
                }
            }
        }

        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 10;
        $start = ($page - 1) * $per;
        $url = "bizorder.php";
        unset($params['page']);

        $zys[] = array();

        $orderID = "";
        if (isset($params['orderID']) && $params['orderID'] != null) {
            $orderID = $params['orderID'];
        }
        $contractID = "";
        if (isset($params['contractID']) && $params['contractID'] != null) {
            $contractID = $params['contractID'];
        }
        $thirdlogno = "";
        if (isset($params['thirdlogno']) && $params['thirdlogno'] != null) {
            $thirdlogno = $params['thirdlogno'];
        }
        $busiesslogno = "";
        if (isset($params['busiesslogno']) && $params['busiesslogno'] != null) {
            $busiesslogno = $params['busiesslogno'];
        }
        $financetype = "";
        $financetypewhere = "";
        if (isset($params['financetype']) && $params['financetype'] != null) {
            $financetype = $params['financetype'];
            if ($financetype == "all") {
                $financetype = "";
                $financetypewhere = " ";
            } else {
                $financetypewhere = " and spb.financetype='" . $financetype . "' ";
            }
        }
        $startdate1 = "";
        $startdatewhere = "";
        if (isset($params['startdate1']) && $params['startdate1'] != null) {
            $startdate1 = $params['startdate1'];
            $startdatewhere .= " and date_format(spbd.startdate,'%Y年%m月%d日')>'" . $startdate1 . "' ";
        }
        $startdate2 = "";
        if (isset($params['startdate2']) && $params['startdate2'] != null) {
            $startdate2 = $params['startdate2'];
            $startdatewhere .= " and date_format(spbd.startdate,'%Y年%m月%d日')<='" . $startdate2 . "' ";
        }
        $enddate1 = "";
        $enddatewhere = "";
        if (isset($params['enddate1']) && $params['enddate1'] != null) {
            $enddate1 = $params['enddate1'];
            $enddatewhere .= " and date_format(spbd.enddate,'%Y年%m月%d日')>'" . $enddate1 . "' ";
        }
        $enddate2 = "";
        if (isset($params['enddate2']) && $params['enddate2'] != null) {
            $enddate2 = $params['enddate2'];
            $enddatewhere .= " and date_format(spbd.enddate,'%Y年%m月%d日')<='" . $enddate2 . "' ";
        }
        //(select id from sys_adminuser where ID='".$userid."' and (IsMain=1 or PowerLimit=1))=spsl.userid
        $ddall1 = $this->_dao->query("select distinct sedt.*,spct.*,sct.* from sm_exc_dd_tag sedt,sm_pinganrongzi_contract_transcation spct, sm_contract_transaction sct, sys_pinganrongzi_shenpi_liucheng spsl 
			where sedt.id=sct.BID and sct.id=spct.dzcontract_id and spsl.mid=spct.Mid_Consignee 
			and (spsl.userid='" . $userid . "' or (select ComID from sys_adminuser where ID='" . $userid . "' and (IsMain=1 or PowerLimit=1))=spsl.Umid or spsl.Umid IN ('$canViewPinganRongZiUser') )			
			and spct.ApprovalMode=1 and spct.ApprovalStatus1=-1 and sct.ContractNo like '%" . $contractID . "%' and sedt.OrderNo like '%" . $orderID . "%' order by spct.id desc");
        $total1 = $this->_dao->getOne("select count(distinct spct.id,sedt.id,sedt.id,spct.id,sct.id) from sm_exc_dd_tag sedt,sm_pinganrongzi_contract_transcation spct, sm_contract_transaction sct, sys_pinganrongzi_shenpi_liucheng spsl 
			where sedt.id=sct.BID and sct.id=spct.dzcontract_id and spsl.mid=spct.Mid_Consignee 
			and (spsl.userid='" . $userid . "' or (select ComID from sys_adminuser where ID='" . $userid . "' and (IsMain=1 or PowerLimit=1))=spsl.Umid or spsl.Umid IN ('$canViewPinganRongZiUser') )
			and spct.ApprovalMode=1 and spct.ApprovalStatus1=-1 order by spct.id desc");

        $ddall2 = $this->_dao->query("select distinct sedt.*,spct.*,sct.* from sm_exc_dd_tag sedt,sm_pinganrongzi_contract_transcation spct, sm_contract_transaction sct, sys_pinganrongzi_shenpi_liucheng spsl 
			where sedt.id=sct.BID and sct.id=spct.dzcontract_id and spsl.mid=spct.Mid_Consignee 
			and (spsl.userid='" . $userid . "' or (select ComID from sys_adminuser where ID='" . $userid . "' and (IsMain=1 or PowerLimit=1))=spsl.Umid or spsl.Umid IN ('$canViewPinganRongZiUser') )
			and ApprovalMode=2 and spct.ApprovalStatus1=-1 and sct.ContractNo like '%" . $contractID . "%' and sedt.OrderNo like '%" . $orderID . "%' order by spct.id desc");
        $total2 = $this->_dao->getOne("select count(distinct spct.id,sedt.id,sedt.id,spct.id,sct.id) from sm_exc_dd_tag sedt,sm_pinganrongzi_contract_transcation spct, sm_contract_transaction sct, sys_pinganrongzi_shenpi_liucheng spsl 
			where sedt.id=sct.BID and sct.id=spct.dzcontract_id and spsl.mid=spct.Mid_Consignee 
			and (spsl.userid='" . $userid . "' or (select ComID from sys_adminuser where ID='" . $userid . "' and (IsMain=1 or PowerLimit=1))=spsl.Umid or spsl.Umid IN ('$canViewPinganRongZiUser') )
			and ApprovalMode=2 and spct.ApprovalStatus1=-1 order by spct.id desc");

        $ddall3 = $this->_dao->query("select distinct spct.id id2,sedt.*,spct.*,sct.* from sm_exc_dd_tag sedt,sm_pinganrongzi_contract_transcation spct, sm_contract_transaction sct, sys_pinganrongzi_shenpi_liucheng spsl 
			where sedt.id=sct.BID and sct.id=spct.dzcontract_id and spsl.mid=spct.Mid_Consignee 
			and (spsl.userid='" . $userid . "' or (select ComID from sys_adminuser where ID='" . $userid . "' and (IsMain=1 or PowerLimit=1))=spsl.Umid or spsl.Umid IN ('$canViewPinganRongZiUser') )
			and ApprovalMode=1 and spct.ApprovalStatus1<>-1 and sct.ContractNo like '%" . $contractID . "%' and sedt.OrderNo like '%" . $orderID . "%' order by spct.id desc");
        $total3 = $this->_dao->getOne("select count(distinct spct.id,sedt.id,sedt.id,spct.id,sct.id) from sm_exc_dd_tag sedt,sm_pinganrongzi_contract_transcation spct, sm_contract_transaction sct, sys_pinganrongzi_shenpi_liucheng spsl 
			where sedt.id=sct.BID and sct.id=spct.dzcontract_id and spsl.mid=spct.Mid_Consignee 
			and (spsl.userid='" . $userid . "' or (select ComID from sys_adminuser where ID='" . $userid . "' and (IsMain=1 or PowerLimit=1))=spsl.Umid or spsl.Umid IN ('$canViewPinganRongZiUser') )
			and ApprovalMode=1 and spct.ApprovalStatus1<>-1 order by spct.id desc");

        $ddall4 = $this->_dao->query("select distinct spct.id id2,sedt.*,spct.*,sct.* from sm_exc_dd_tag sedt,sm_pinganrongzi_contract_transcation spct, sm_contract_transaction sct, sys_pinganrongzi_shenpi_liucheng spsl 
			where sedt.id=sct.BID and sct.id=spct.dzcontract_id and spsl.mid=spct.Mid_Consignee 
			and (spsl.userid='" . $userid . "' or (select ComID from sys_adminuser where ID='" . $userid . "' and (IsMain=1 or PowerLimit=1))=spsl.Umid or spsl.Umid IN ('$canViewPinganRongZiUser') )
			and ApprovalMode=2 and spct.ApprovalStatus1<>-1 and sct.ContractNo like '%" . $contractID . "%' and sedt.OrderNo like '%" . $orderID . "%' order by spct.id desc");
        $total4 = $this->_dao->getOne("select count(distinct spct.id,sedt.id,sedt.id,spct.id,sct.id) from sm_exc_dd_tag sedt,sm_pinganrongzi_contract_transcation spct, sm_contract_transaction sct, sys_pinganrongzi_shenpi_liucheng spsl 
			where sedt.id=sct.BID and sct.id=spct.dzcontract_id and spsl.mid=spct.Mid_Consignee 
			and (spsl.userid='" . $userid . "' or (select ComID from sys_adminuser where ID='" . $userid . "' and (IsMain=1 or PowerLimit=1))=spsl.Umid or spsl.Umid IN ('$canViewPinganRongZiUser') )
			and ApprovalMode=2 and spct.ApprovalStatus1<>-1 order by spct.id desc");

        $ddall5 = $this->_dao->query("select distinct spb.*,spbd.*,sedt.*,spct.*,sct.*,spb.date date2 from sys_pinganrongzi_billinfo spb, sys_pingan_billlist_detail spbd, sys_pinganrongzi_shenpi_liucheng spsl, sm_pinganrongzi_contract_transcation spct, sm_contract_transaction sct, sm_exc_dd_tag sedt
			where sedt.id=sct.BID and spb.id=spbd.Pid and spsl.mid=spct.Mid_Consignee and spct.thirdlogno=spb.thirdlogno
			and (spsl.userid='" . $userid . "' or (select ComID from sys_adminuser where ID='" . $userid . "' and (IsMain=1 or PowerLimit=1))=spsl.Umid or spsl.Umid IN ('$canViewPinganRongZiUser') )
			and sct.id=spct.dzcontract_id and sct.ContractNo=substring(spb.contractno,3) and sct.ContractNo like '%" . $contractID . "%' and sedt.OrderNo like '%" . $orderID . "%' and spct.thirdlogno like '%" . $thirdlogno . "%' and spct.busiesslogno like '%" . $busiesslogno . "%' $financetypewhere $startdatewhere $enddatewhere order by spb.date desc ");
        $total5 = $this->_dao->getOne("select count(distinct spb.id,spbd.id,sct.id,sedt.id,spct.id) from sys_pinganrongzi_billinfo spb, sys_pingan_billlist_detail spbd, sys_pinganrongzi_shenpi_liucheng spsl, sm_pinganrongzi_contract_transcation spct, sm_contract_transaction sct, sm_exc_dd_tag sedt
			where sedt.id=sct.BID and spb.id=spbd.Pid and spsl.mid=spct.Mid_Consignee and spct.thirdlogno=spb.thirdlogno
			and (spsl.userid='" . $userid . "' or (select ComID from sys_adminuser where ID='" . $userid . "' and (IsMain=1 or PowerLimit=1))=spsl.Umid or spsl.Umid IN ('$canViewPinganRongZiUser') )
			and sct.id=spct.dzcontract_id and sct.ContractNo=substring(spb.contractno,3) order by spb.date desc ");

        if ($sta == "1" || $sta == null) {
            $ddall = $ddall1;
        } elseif ($sta == "2") {
            $ddall = $ddall2;
        } elseif ($sta == "3") {
            $ddall = $ddall3;
        } elseif ($sta == "4") {
            $ddall = $ddall4;
        } elseif ($sta == "5") {
            $ddall = $ddall5;
        }


        foreach ($ddall as $key => &$tmp) {

            $tmp["Mid_Consignee"] = $this->_dao->get_dd_detail_list($tmp['Mid_Consignee']);
            $tmp["Mid_Shipper"] = $this->_dao->get_dd_detail_list($tmp['Mid_Shipper']);
            $tmp["ApprovalStatus1"] = $GLOBALS["PA_ApprovalStatus1"][$tmp["ApprovalStatus1"]];
            $tmp["ApprovalStatus2"] = $GLOBALS["PA_ApprovalStatus2"][$tmp["ApprovalStatus2"]];
            $tmp["financetype"] = $GLOBALS["PA_Financetype"][$tmp["financetype"]];
            $tmp["opertype"] = $GLOBALS["PA_opertype"][$tmp["opertype"]];
            $tmp["ApprovalMode"] = $GLOBALS["PA_ApprovalMode"][$tmp["ApprovalMode"]];
            if ($sta == "5") {
                $tmp["startdate"] = date("Y年m月d日", strtotime($tmp["startdate"]));
                $tmp["enddate"] = date("Y年m月d日", strtotime($tmp["enddate"]));
                $tmp["date2"] = date("Y年m月d日 H点i分s秒", strtotime($tmp["date2"]));
                //$tmp["firstmoney"] = (string)$tmp["firstmoney"];
                $detail = $this->_dao->query("select * from sys_pingan_billlist_detail where Pid=" . $tmp["ID"]);
                $tmp["detail"] = $detail;
            } else {
                $tmp["CreateDate"] = date("Y年m月d日 H点m分s秒", strtotime($tmp["CreateDate"]));
                $tmp["date"] = date("Y年m月d日 H点m分s秒", strtotime($tmp["date"]));
                //订单详细
                $zys[$key] = $this->_dao->get_dd_detail_list($tmp['BID']);

                $ht_data = $this->_dao->get_sm_contract_transaction($tmp['BID']);
                $tmp['htstatu'] = $ht_data['Status'];
                $tmp['PayStatus'] = $ht_data['PayStatus'];
                $tmp['fkxs'] = $ht_data['fkxs'];

                //付款形式大类 现款，承兑
                $tmp['fkxss'] = $this->get_fkxss($tmp['fkxs']);
            }
        }


        //excel导出
        if (isset($params['excel'])) {
            $date = date('Ymd');
            header("Content-type:application/vnd.ms-excel");
            header("Content-Disposition:attachment;filename=$date.csv");
            foreach ($ddall as $listrow) {
                echo "厂商融资申请号,";
                echo $listrow["thirdlogno"] . ",";
                echo "银行业务编号,";
                echo $listrow["busiesslogno"] . "\t,";
                echo "融资类型,";
                echo $listrow["financetype"] . ",";
                echo "融资申请金额,";
                echo $listrow["appamount"] . ",";
                echo "客户保证金金额,";
                echo $listrow["firstmoney"] . "\t,";
                echo "预付款/尾款融资,";
                echo $listrow["ApprovalMode"] . ",\n";
                echo "合同编号,";
                echo substr($listrow["contractno"], 2) . ",";
                echo "合同金额,";
                echo $listrow["contractamt"] . ",";
                echo "票据号,";
                echo $listrow["billno"] . "\t,";
                echo "金额,";
                echo $listrow["billamount"] . ",";
                echo "出票人,";
                echo $listrow["custaccountname"] . ",";
                echo "出票人账号,";
                echo $listrow["custaccount"] . ",\n";
                echo "出票人银行行号,";
                echo $listrow["custbanknumber"] . "\t,";
                echo "出票人银行名称,";
                echo $listrow["custbankname"] . ",";
                echo "收票人,";
                echo $listrow["partaccountname"] . ",";
                echo "收票人账号,";
                echo $listrow["partaccount"] . ",";
                echo "收票人银行行号,";
                echo $listrow["partbanknumber"] . "\t,";
                echo "收票人银行名称,";
                echo $listrow["partbankname"] . ",\n";
                echo "订单编号,";
                echo $listrow["OrderNo"] . ",";
                echo "采购方,";
                echo $listrow["Mid_Consignee"] . ",";
                echo "供应方,";
                echo $listrow["Mid_Shipper"] . ",\n";

                echo "操作结果,";
                echo "操作结果内容,";
                echo "出票日期,";
                echo "票据到期日,";
                echo "反馈时间,\n";
                /*foreach($listrow["detail"] as $detailrow)
                {
                    echo $detailrow['opertype']."\t";
                    echo $detailrow['refusemsg']."\t";
                    echo $detailrow['startdate']."\t";
                    echo $detailrow['enddate']."\t";
                    echo $detailrow['date']."\t\n";
                }*/
                echo $listrow['opertype'] . ",";
                echo $listrow['refusemsg'] . ",";
                echo $listrow['startdate'] . ",";
                echo $listrow['enddate'] . ",";
                echo $listrow['date2'] . ",\n";
                echo "\n\n";
            }
            exit;
        }

        $this->assign("total1", $total1);
        $this->assign("total2", $total2);
        $this->assign("total3", $total3);
        $this->assign("total4", $total4);
        $this->assign("total5", $total5);
        $this->assign("PingAnNeiBuShenHeURL", $GLOBALS["PingAnNeiBuShenHeURL"]);
        //分页判断-----------------------started
        if ($sta == "1" || $sta == "") {
            $pagebar = $this->pagebar($url, $params, $per, $page, count($ddall));
        }
        if ($sta == "2") {
            $pagebar = $this->pagebar($url, $params, $per, $page, count($ddall2));
        }
        if ($sta == "3") {
            $pagebar = $this->pagebar($url, $params, $per, $page, count($ddall3));
        }
        if ($sta == "4") {
            $pagebar = $this->pagebar($url, $params, $per, $page, count($ddall4));
        }
        if ($sta == "5") {
            $pagebar = $this->pagebar($url, $params, $per, $page, count($ddall5));
        }

        //分页判断-----------------------ended
        $this->assign("page", $page);
        $this->assign("pagebar", $pagebar);
        $this->assign("zys", $zys);
        $this->assign("ddall", $ddall);
        //$this->assign("page_title","订单列表_会员中心_");
        $this->assign("params", $params);

        //权限字段
        $this->assign("userrole", $_SESSION['SYS_USERROLE']);
        $this->assign("ddtype", $GLOBALS['DDTYPE']);
        $this->assign("htstatus", $GLOBALS['HT_STATUS']);

        $this->assign("htpaystaus", $GLOBALS['HTPAYSTATUS']);

        $this->setvars();

        $this->assign("PA_Financetype", $GLOBALS["PA_Financetype"]);
    }

    public function czinfoorder($params)
    {
        //$sta=$params['sta'];
        $compid = $_SESSION['SYS_COMPANYID'];
        $userid = $_SESSION['SYS_USERID'];
        $where = "";
        if ($params['orderID'] != "") {
            $where .= " and OrderNo like '%" . $params['orderID'] . "%'";
        }

        if (in_array($compid, $GLOBALS['canUsePinganRongziMid'])) {
            $ComID = $this->_dao->getOnes("SELECT ComID from sys_adminuser where ID='" . $userid . "' and (IsMain=1 or PowerLimit=1)");
            if ($ComID) {
                $branchcompanys = $this->_dao->getOnes("SELECT id from sys_company where GroupID='" . $compid . "'");
                $viewMid = implode(",", array_merge($ComID, $branchcompanys));

                $page = $params['page'] == '' ? 1 : $params['page'];
                $per = 10;
                $start = ($page - 1) * $per;
                $url = "bizorder.php";
                unset($params['page']);

                $zys[] = array();

                $orderID = "";
                if ($params['orderID'] != null) {
                    $orderID = $params['orderID'];
                }
                $contractID = "";
                if ($params['contractID'] != null) {
                    $contractID = $params['contractID'];
                }

                $ddall = $this->_dao->query("select * from sys_pinganrongzi_billinfo spb, sys_pingan_billlist_detail spbd where spb.id=spbd.Pid order by spb.date desc LIMIT $start,$per");

                foreach ($ddall as $key => &$tmp) {

                    if (in_array($compid, $GLOBALS['JITUANID'])) {

                        $selectmid = $this->_dao->getOne("select Mid FROM sys_yq_shenpi_status where dzcontract_id='" . $tmp['dzcontract_id'] . "'  and userid='" . $userid . "' and Mid = '" . $tmp['mid'] . "' ");
                    } else {
                        $selectmid = $compid;
                    }

                    $tmp['ComName'] = $this->_dao->get_ComName($selectmid);

                    $zys[$key] = $this->_dao->query("SELECT sm_exc_dd.*,sm_exc_sales_details.Vid,sm_exc_sales_details.cd ,sm_exc_sales_details.strength ,sm_exc_sales_details.xincengWeight FROM sm_exc_dd,sm_exc_sales_details where sm_exc_sales_details.id=sm_exc_dd.sdid and sm_exc_dd.Dtid='" . $tmp['ddid'] . "' ", 0);

                    foreach ($zys[$key] as &$tmp1) {
                        if ($tmp1['Sid'] < 0) {
                            $tmp1['Sids'] = $tmp1['Sid'] * (-1);
                        }
                    }

                    $ht_data = $this->_dao->get_sm_contract_transaction($tmp['ddid']);
                    $tmp['htstatu'] = $ht_data['Status'];
                    $tmp['PayStatus'] = $ht_data['PayStatus'];
                    $tmp['fkxs'] = $ht_data['fkxs'];
                    //付款形式大类 现款，承兑
                    $tmp['fkxss'] = $this->get_fkxss($tmp['fkxs']);
                }

                $this->assign("total", count($ddall));
                //分页判断-----------------------started
                $pagebar = $this->pagebar($url, $params, $per, $page, count($ddall));

                //分页判断-----------------------ended
                $this->assign("page", $page);
                $this->assign("pagebar", $pagebar);
                $this->assign("zys", $zys);
                $this->assign("ddall", $ddall);
                //$this->assign("page_title","订单列表_会员中心_");  
                $this->assign("params", $params);

                //权限字段
                $this->assign("userrole", $_SESSION['SYS_USERROLE']);
                $this->assign("ddtype", $GLOBALS['DDTYPE']);
                $this->assign("htstatus", $GLOBALS['HT_STATUS']);
                $this->assign("htpaystaus", $GLOBALS['HTPAYSTATUS']);

                $this->setvars();
            }
        }
    }
    //added by tuxw for parz ended 20151207
    //added by quanjw for isec
    public function ajaxgetnewzyinfo($params)
    {

        $where .= $this->getwhere("sm_exc_sales.Mid", (int)$params['gcm'], EQUAL);

        //品种
        $where .= $this->getwhere("pm_parentid", $params['variety'], EQUAL);

        //材质
        if ($params['materialcode'] != "") {
            $notinmj = " and vid not in (" . $GLOBALS["MEITAN_VID"] . "," . $GLOBALS["JIAOTAN_VID"] . "," . $GLOBALS["SHUINI_VID"] . "," . $GLOBALS["JINSHU_VID"] . ")";
            $where .= " and MaterialCode like '%" . mysqli_real_escape_string($this->mysqli, $params['materialcode']) . "%' $notinmj";
        }

        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 50;
        $start = ($page - 1) * $per;

        $total = $this->_dao->getOne("SELECT COUNT(sm_exc_sales.ID) FROM sm_exc_sales,sm_exc_sales_details, sys_company WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.Status =2   and sm_exc_sales.SalesEndDate > now() $where ");
        $data = $this->_dao->query("SELECT  sm_exc_sales_details.*,sm_exc_sales.*, sys_company.ComNameShort,sys_company.ComName,(select ARealName from sys_adminuser where ComID = sys_company.ID  and IsMain=1 and Status=1 ) as LinkMan ,sys_company.ContactTel,sys_company.ContactFax   FROM sm_exc_sales_details, sys_company,sm_exc_sales WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.Status =2   and sm_exc_sales.SalesEndDate > now() $where order by   sm_exc_sales.CreateDate DESC,sm_exc_sales.ID DESC LIMIT $start,$per",3600);
        $totalpage = ceil($total / $per);
        //----------------组织数据开始
        $record = array();
        $nowtime = date('Y-m-d H:i:s');
        $data = $this->zy_format($data);
        foreach ($data as $d) {

            $tmp =  array();
            $tmp[] = "<a href='listing.php?view=zylist&mid=" . $d['Mid'] . " ' target='_blank'>" . mb_substr($d['ComNameShort'], 0, 6, 'gb2312') . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['VarietyName'], 0, 8, 'gb2312') . "</a>";

            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['MaterialCode'], 0, 16, 'gb2312') . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['SpecCode'], 0, 8, 'gb2312') . "</a>";

            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['OriginCode'], 0, 6, 'gb2312') . "</a>";

            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['PickUpCity'], 0, 4, 'gb2312') . "</a>";
            if ($d['SalesMinPrice'] == '0.00' || $d['SalesMinPrice'] == ''  || $d['TradeType'] == '3') {
                $tmp[] = "协议价";
            } else {
                $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank' >" . floor($d['SalesMinPrice']) . "</a>";
            }

            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank' >" . $d['QuantitySalesed'] . "/" . $d['QuantitySales'] . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank' >" . $d['QuantitySalesed'] . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank' >" . date("m/d", strtotime($d['CreateDate'])) . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank' >" . $GLOBALS['ZY_ALL_TYPES'][$d['TradeType']] . "</a>";


            $phone = PHONENUM;

            $tmp[] = "<span></span>
                    <dt>" . $d['ComName'] . "</dt>
                    <dd>
                        <p>公司地址：" . $d['Address'] . "</p>
                        <p>联系电话：" . $d['ContactTel'] . "</p>
                        <p>" . kefu . "" . $phone . "</p>
                    </dd> ";

            $tmp[] = "<TABLE width=\"100%\" border=0 cellPadding=1 cellSpacing=1 bgcolor=\"#BFE1FF\">
                                  <TBODY>
                                  <TR bgColor=#FFFFFF >
                                    <TD colspan=\"2\" style=\"FONT-SIZE: 12px;line-height:24px;background-image:url(/images/company_cmanbg_new.gif);\" >
                                    <strong>" . $d['ComName'] . "</strong></TD>
                                  </TR>
                                  <TR bgColor=#FFFFFF>
                                    <TD colspan=\"2\"  style=\"FONT-SIZE: 12px; line-height:24px;\">
                                      　交货仓库：" . $d['jhck'] . "<br>
                                      　交货地址：" . $d['PickUpAddress'] . "<br></TD>
                                    </TR>
                                  </TBODY>
                                </TABLE> ";

            $tmp[] = "<input name=\"res_id[]\" type=\"checkbox\" value=" . $d['ID'] . ">";

            if ($d['QQNum'] != "") {
                $xxxt = "<a href=\"//wpa.qq.com/msgrd?v=3&uin=" . $d['QQNum'] . "&site=qq&menu=yes\"><img src=\"/img/talk_qq.png\" /></a>";
            } else {

                $xxxt = "";
            }

            $tmp[] = "<a href=\"javascript:checkHasAndChatTo(" . $d['CreateUser'] . ")\"><img class=\"img_" . $d['CreateUser'] . "\" id=\"img_" . $d['CreateUser'] . "\" src=\"images/gmt4.gif\" style=\"border:0;\" ></a>" . $xxxt;
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "&talk=1' target='_blank'>留言</a>";

            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . date('m/d', strtotime($d['CreateDate'])) . "</a>";

            if ($d['flag'] == 1 && $d['salestp'] == 1) {

                $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'><img  src=\"/images/buy.jpg\" /></a>";
            } else if ($d['flag'] == 2 && $d['salestp'] == 1) {

                $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'><img  src=\"/images/buy2.jpg\" /></a>";
            } else if ($d['flag'] == 1 && $d['salestp'] == 2) {

                $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'><img  src=\"/images/sales.jpg\" /></a>";
            } else if ($d['flag'] == 2 && $d['salestp'] == 2) {
                $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'><img  src=\"/images/sales2.jpg\" /></a>";
            }

            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['cd'], 0, 6, 'gb2312') . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['strength'], 0, 6, 'gb2312') . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['xincengWeight'], 0, 6, 'gb2312') . "</a>";

            $record[] = implode("|X|", $tmp);
        }
        $record = implode("|O|", $record);
        //总页, 当前页
        $others = $totalpage . "|A|" . $page;
        file_put_contents("/tmp/zyinfo", "==:" . $record . "|H|" . $others . "==", FILE_APPEND);
        echo $record . "|H|" . $others;
        exit;
    }
    //added by quanjw for isec

    //added by tuxw for isec started 20160128
    public function recresources($params)
    {
        $page = $params['page'] == '' ? 1 : $params['page'];
        if ($params['more'] == "more") {
            $per = 100;
        } else {
            $per = 54;
        }
        $start = ($page - 1) * $per;

        $this->setvars();
        $this->assign("params", $params);

        // 头部成交量统计
        $this->getCJTongji();
        $str = "";

        $str .= $this->getRequestStr("search", $params['search']);
        $str .= $this->getRequestStr("comname", $params['comname']);
        $str .= $this->getRequestStr("pm", $params['pm']);
        $str .= $this->getRequestStr("city", $params['city']);
        $str .= $this->getRequestStr("gc", $params['gc']);
        $str .= $this->getRequestStr("caizhi", $params['caizhi']);
        $str .= $this->getRequestStr("guige", $params['guige']);
        $str .= $this->getRequestStr("jg1", $params['jg1']);
        $str .= $this->getRequestStr("jg2", $params['jg2']);
        $str .= $this->getRequestStr("cd1", $params['cd1']);
        $str .= $this->getRequestStr("cd2", $params['cd2']);
        $str .= $this->getRequestStr("kd1", $params['kd1']);
        $str .= $this->getRequestStr("kd2", $params['kd2']);
        $str .= $this->getRequestStr("hd1", $params['hd1']);
        $str .= $this->getRequestStr("hd2", $params['hd2']);
        $str .= $this->getRequestStr("jhck", $params['jhck']);
        $str .= $this->getRequestStr("sort", $params['sort']);
        //传递参数

        $this->assign("str", $str);

        $xzcity = $this->_dao->AQuery("select cityid,cityname from city  where  	city_keys != ''  limit 30");
        $this->assign("xzcity", $xzcity);

        //最新加盟
        $sys_companysjm = $this->_dao->query("SELECT * FROM sys_company WHERE `MemberType` >1 and Status = 1 and IsShowWeb = 1 ORDER BY FirstSignDate DESC,CreateDate DESC limit 10");
        foreach ($sys_companysjm as &$tmp) {
            $myArray = explode("://", $tmp['ComUrl'], 2);
            if ($tmp['ComUrl'] != '') {
                if ($myArray[0] == "http" || $myArray[0] == "https") {
                } else {
                    $tmp['ComUrl'] = "//" . $tmp['ComUrl'];
                }
            }
        }
        $this->assign("jm", $sys_companysjm);

        //最新加盟V2
        $sys_companysjm2 = $this->_dao->query("SELECT * FROM sys_company WHERE `MemberType` >1 and Status = 1 and IsShowWeb = 1 ORDER BY FirstSignDate DESC,CreateDate DESC limit 10");
        foreach ($sys_companysjm2 as &$tmp) {
            $myArray = explode("://", $tmp['ComUrl'], 2);
            if ($tmp['ComUrl'] != '') {
                if ($myArray[0] == "http" || $myArray[0] == "https") {
                } else {
                    $tmp['ComUrl'] = "//" . $tmp['ComUrl'];
                }
            }
        }
        $this->assign("jm2", $sys_companysjm2);

        //Updated by quanjw for news start 2015/9/11 包括公告和成交
        //最新公告 
        $newgg = $this->_dao->query("SELECT * FROM `zixun_info` where  Status = 1 and category not in (1)  and if(category=15,IsRec=1,'1=1')  and if(category=12,IsRec=1,'1=1')    ORDER BY createdate  DESC limit 10");
        $this->assign("newgg", $newgg);
        //Updated by quanjw for news end  2015/9/11

        //add by xiakang for isechome started 2015/09/15
        $newggisec = $this->_dao->query("SELECT * FROM `zixun_info` where  Status = 1 and category not in (1,22) ORDER BY createdate  DESC limit 10");
        $this->assign("newggisec", $newggisec);
        //add by xiakang for isechome ended 2015/09/15

        //Added by quanjw for news start 2015/9/12
        //招标采购 cnbc isec
        $zbcg = $this->_dao->query("SELECT * FROM `zixun_info` where 1 and Status = 1 and category =22  ORDER BY IsTop DESC, createdate  DESC limit 10");
        $this->assign("zbcg", $zbcg);
        //Added by quanjw for news end 2015/9/12

        //成交动态cnbc
        $cjyb = $this->_dao->query("SELECT * FROM `zixun_info` where 1 and Status = 1 and category in (5,21)  ORDER BY IsTop DESC, createdate  DESC limit 10");
        $this->assign("cjyb", $cjyb);

        //成交月报 大宗v2
        $cjyb2 = $this->_dao->query("SELECT * FROM `zixun_info` where 1 and Status = 1 and category =21  ORDER BY IsTop DESC, createdate  DESC limit 6");
        $this->assign("cjyb2", $cjyb2);

        //成交公告 大宗v2
        $cjgg2 = $this->_dao->query("SELECT * FROM `zixun_info` where 1 and Status = 1 and category =5  ORDER BY IsTop DESC, createdate  DESC limit 6");
        $this->assign("cjgg2", $cjgg2);

        //销售资源
        $xszy = $this->_dao->query("SELECT * FROM `zixun_info` where 1 and Status = 1 and category in (15,11)  ORDER BY IsTop DESC, createdate  DESC limit 10");
        $this->assign("xszy", $xszy);

        //采购资源
        $cgzy = $this->_dao->query("SELECT * FROM `zixun_info` where 1 and Status = 1 and category in (12,13)  ORDER BY IsTop DESC, createdate  DESC limit 10");
        $this->assign("cgzy", $cgzy);

        //每日综述
        $mrzs = $this->_dao->query("SELECT * FROM `zixun_info` where 1 and Status = 1 and category = 18  ORDER BY IsTop DESC, createdate  DESC limit 10");
        $this->assign("mrzs", $mrzs);

        //每日综述V2
        $mrzs2 = $this->_dao->query("SELECT * FROM `zixun_info` where 1 and Status = 1 and category = 18  ORDER BY IsTop DESC, createdate  DESC limit 6");
        $this->assign("mrzs2", $mrzs2);

        //固定一条置顶
        $zhid = $this->_dao->getRow("SELECT * FROM `zixun_info` where 1 and Status = 1  ORDER BY IsTop DESC, createdate  DESC limit 1");
        $this->assign("zhid", $zhid);

        $this->assign("newstype", $GLOBALS['NEWS_TYPE']);

        //最新公告V2
        $newgg2 = $this->_dao->query("SELECT * FROM `zixun_info` where 1 and Status = 1 and ID != '" . $zhid['ID'] . "' and ID not in (21035,21029) and category not in (16,26) and if(category=15,IsRec=1,'1=1')  and if(category=12,IsRec=1,'1=1')  ORDER BY createdate  DESC limit 5");
        $this->assign("newgg2", $newgg2);


        //热点聚焦
        $hotnews = $this->_dao->query("SELECT * FROM `zixun_info` where  Status = 1 and category = 1   ORDER BY createdate  DESC limit 10");
        $this->assign("hotnews", $hotnews);

        $this->assign("newstype", $GLOBALS['NEWS_TYPE']);

        //Updated by quanjw for meijiao start 2015/7/15
        $arraypz = $this->getPZArray();
        $this->assign("arraypz", $arraypz);
        //Updated by quanjw for meijiao start 2015/7/15
        //--

        //资源推荐
        $citys = $this->_dao->AQuery("SELECT cityid as CityCode, cityname as CityName FROM city Where 1=1 ORDER BY id");
        $this->assign("citys", $citys);
        $zyrec = $this->_dao->query("select sm_exc_sales.*,sm_exc_sales_details.VarietyName,sm_exc_sales_details.MaterialCode,sm_exc_sales_details.SalesMinPrice from sm_exc_sales,sm_exc_sales_details where sm_exc_sales.ID= sm_exc_sales_details.Pid and IsRec=1  order by CreateDate DESC limit 10", 0);
        $this->assign("zyrec", $zyrec);

        //new add    bin    
        $list1 = $this->_dao->query("SELECT *  FROM `sys_news_question` where  Pid=1  limit 0,4", "no");
        $list2 = $this->_dao->query("SELECT *  FROM `sys_news_question` where  Pid=1  limit 4,4", "no");
        $list3 = $this->_dao->query("SELECT *  FROM `sys_news_question` where  Pid=1  limit 8,4", "no");

        $this->assign("list1", $list1);
        $this->assign("list2", $list2);
        $this->assign("list3", $list3);
        //广告

        $changeads = $this->_dao->getRow("select * from sys_ad where AdType = 2");
        $this->assign("ggjs", $changeads);

        $changeads2 = $this->_dao->getRow("select * from sys_ad where AdType = 3");
        $this->assign("ggjs2", $changeads2);
        //added by tuxw for isec started 20160125
        $nowdate = date("Y-m-d H:i:s", time());
        $wherepm = "";
        if ($params["pm"] != "") {
            $pmid = $this->_dao->getOne("select id from biz_key where kname='" . $params["pm"] . "'");
            if ($pmid == "" || $pmid == null) {
                $pmid = $this->_dao->getOne("select k_id from key_alias where aname='" . $params["pm"] . "'");
            }
            $wherepm = " and (pm_id=" . $pmid . " or pm_parentid=" . $pmid . " or pm_ppid=" . $pmid . ") ";
        }
        $wherecity = "";
        if ($params["city"] != "") {
            $wherecity = " and PickUpCity like '%" . $params["city"] . "%' ";
        }
        //$company2 = $this->_dao->query("select distinct ComName from sm_exc_sales, sys_company where IsRec = 1 and sys_company.id=sm_exc_sales.mid order by RecDate DESC limit 10",0);
        $company2 = $this->_dao->query("select sys_company.id mid, ComName from sm_exc_sales, sys_company, sm_exc_sales_details 
			where IsRec = 1 and sys_company.id=sm_exc_sales.mid and sm_exc_sales.ID=sm_exc_sales_details.Pid and sm_exc_sales.status=2 and sm_exc_sales.SalesEndDate>now() and sm_exc_sales.IsRec=1 $wherepm $wherecity
			group by ComName order by RecDate DESC limit $per", 0);
        foreach ($company2 as &$v) {
            $v['Quantity'] = $this->_dao->getOne("select sum(QuantitySales-QuantitySalesed-IFNULL( sm_lock_sales.Num, 0 )) Quantity from sm_exc_sales, sm_exc_sales_details left join sm_lock_sales on sm_exc_sales_details.ID=sm_lock_sales.STID and sm_lock_sales.status='1' where " . $v['mid'] . "=sm_exc_sales.mid and sm_exc_sales.ID=sm_exc_sales_details.Pid and sm_exc_sales.status=2 and sm_exc_sales.SalesEndDate>now() $wherepm $wherecity
			");
        }

        $totalalls = $this->_dao->query("select ComName, sum(QuantitySales-QuantitySalesed-IFNULL( sm_lock_sales.Num, 0 )) Quantity from sm_exc_sales, sys_company, sm_exc_sales_details  left join sm_lock_sales on sm_exc_sales_details.ID=sm_lock_sales.STID and sm_lock_sales.status='1' where IsRec = 1 and sys_company.id=sm_exc_sales.mid and sm_exc_sales.ID=sm_exc_sales_details.Pid and sm_exc_sales.status=2 and sm_exc_sales.SalesEndDate>now() and sm_exc_sales.IsRec=1 group by ComName", 0);
        //added by tuxw for isec ended 20160125

        //added by tuxw for isec started 20160125
        $this->assign("company2", $company2); //echo "<pre>";print_r($company2);
        //added by tuxw for isec ended 20160125
        //all
        $totalall = count($totalalls);
        $totalall = ceil($totalall / $per);
        if ($totalall < 1) {
            $totalall = 1;
        }
        $this->assign("totalall", $totalall);

        $this->assign("per", $per);
        $this->assign("page", $page);
        $pre = $page - 1 < 1 ? 1 : $page - 1;
        //all
        $nextall = $page + 1 > $totalall ? $totalall : $page + 1;

        $this->assign("pre", $pre);
        $this->assign("nextall", $nextall);
    }

    public function getPZArray()
    {
        $arr = array(
            array(
                'bname' => '长材',
                'show_num' => 3,
                'vs' => array('线材', '螺纹钢', '盘螺', '圆钢', '槽钢', 'H型钢', '工字钢', '钢板桩', '球扁钢', '扁钢', '异型材', '方钢', '冷弯型钢', 'Z字钢', 'U型钢', '丁字钢', '钢轨', '铁道用材', '其它型材'),
            ),
            array(
                'bname' => '扁平材',
                'show_num' => 3,
                'vs' => array('普中板', '合金中板', '造船板', '锅炉板', '容器板', '桥梁板', '中厚板', '翼缘板', '其他中板', 'br', '热轧普板卷', '合金钢板卷', '花纹板卷', '船卷', '管线钢', '酸洗板卷', '中宽带', 'br', '冷轧板卷', '轧硬卷', '热镀锌', '电镀锌', '彩涂板卷', '镀铬板卷', '镀锡板卷', '镀铝锌', '其它涂镀', '电工钢', 'br', '热轧窄带', '冷轧窄带'),
            ),
            array(
                'bname' => '管材',
                'show_num' => 3,
                'vs' => array('无缝管', '焊管', '镀锌管', '铸铁管', '方管'),
            ),
            array(
                'bname' => '不锈钢',
                'show_num' => 3,
                'vs' => array('不锈板卷', '不锈钢棒', '不锈线材', '不锈钢管', '不锈钢坯'),
            ),
            array(
                'bname' => '优特钢',
                'show_num' => 3,
                'vs' => array('碳结钢', '合结钢', '齿轮钢', '工具钢', '模具钢', '轴承钢', '易切削钢', '弹簧钢', '优硬线', '拉丝线', '冷镦钢', '合金线材', 'PC钢棒', '焊线', '其它特钢'),
            ),
            array(
                'bname' => '炉料',
                'show_num' => 3,
                'vs' => array('铁矿石', '锰矿石', '铬矿石', '合金矿石', 'br', '炼钢生铁', '铸造生铁', '球墨铸铁', '热压铁块', 'br', '方坯', '板坯', '管坯', 'br', '重废', '中废', '统废', '合金废钢', '废铁'),
            ),
            array(
                'bname' => '煤焦',
                'show_num' => 3,
                'vs' => array('动力煤', '炼焦煤', '喷吹煤', '冶金焦', '铸造焦', '兰炭', '粗苯', '煤焦油', '硫酸铵', '甲醇', '氯化苯', '焦化苯', '石油苯', '二甲苯', '加氢苯', '顺酐', '工业萘', '煤沥青', '粗酚'),
            ),
            array(
                'bname' => '铁合金',
                'show_num' => 3,
                'vs' => array('硅铁', '锰铁', '硅锰', '铬铁', '钼铁' . '钒铁', '钨铁'),
            ),
            array(
                'bname' => '有色',
                'show_num' => 4,
                'vs' => array('铜', '铝', '锌', '镍', 'br', ' 铂', '钯', '黄金', '白银', 'br', '锑', '铟钴', '镁', '锗', '汞', '硒', '铋', 'br', '废铜', '废铝' . '废铅', '废锡', '废锌'),
            ),
        );
        return $arr;
    }

    public function reczy2($params)
    {
        $indexaction = new IndexAction();
        $params['isrec'] = 1;
        $params['st'] = 1;
        $indexaction->pzlist1($params, $this);
        $listAttrs = $this->getAttributes();
        $this->assign("listAttrs", $listAttrs);
    }

    public function ajaxgetreczy($params)
    {

        $params['search'] =  URLdecode($params['search']);
        $params['comname'] =  URLdecode($params['comname']);
        $params['pm'] =  URLdecode($params['pm']);
        $params['caizhi'] =  URLdecode($params['caizhi']);
        $params['jhck'] =  URLdecode($params['jhck']);
        $params['guige'] =  URLdecode($params['guige']);

        if ($params['search'] != '') {
            $mid = $this->_dao->getOnes("select ID from sys_company where ( ComName  LIKE '%" . $params['search'] . "%'   or ComNameShort  LIKE '%" . $params['search'] . "%'  )     ");
            $mid = implode(",", $mid);
            if ($mid) {
                $wherecg .= "and ( VarietyName like '%" . mysqli_real_escape_string($this->mysqli, $params['search']) . "%' or   MaterialCode like '%" . mysqli_real_escape_string($this->mysqli, $params['search']) . "%'   or    SpecCode like '%" . mysqli_real_escape_string($this->mysqli, $params['search']) . "%'  or Mid  in ($mid))  ";
            } else {
                $wherecg .= "and ( VarietyName like '%" . mysqli_real_escape_string($this->mysqli, $params['search']) . "%' or   MaterialCode like '%" . mysqli_real_escape_string($this->mysqli, $params['search']) . "%'   or    SpecCode like '%" . mysqli_real_escape_string($this->mysqli, $params['search']) . "%'  or Mid  =0 )  ";
            }
        }

        $where .= $this->getwhere("VarietyName", $params['pm'], LIKE);
        $where .= $this->getwhere("PickUpCity", $params['city'], LIKE);
        $where .= $this->getwhere("OriginCode", $params['gc'], LIKE);
        $where .= $this->getwhere("MaterialCode", $params['caizhi'], LIKE);
        $where .= $this->getwhere("SpecCode", $params['guige'], LIKE);
        $where .= $this->getwhere("jhck", $params['jhck'], LIKE);

        $where .= $this->getwhere("SalesMinPrice", $params['jg1'], GREATER_THAN);
        $where .= $this->getwhere("SalesMinPrice", $params['jg2'], LESS_THAN);

        $where .= $this->getwhere("hd", $params['hd1'], GREATER_THAN);
        $where .= $this->getwhere("hd", $params['hd2'], LESS_THAN);

        $where .= $this->getwhere("kd", $params['kd1'], GREATER_THAN);
        $where .= $this->getwhere("kd", $params['kd2'], LESS_THAN);

        $where .= $this->getwhere("cd", $params['cd1'], GREATER_THAN);
        $where .= $this->getwhere("cd", $params['cd2'], LESS_THAN);

        $aa = URLdecode($params['comname']);
        if ($params['comname'] != "") {

            $mid = $this->_dao->getOnes("select ID from sys_company where ( ComName   LIKE '%" . $params['comname'] . "%'   or ComNameShort   LIKE '%" . $params['comname'] . "%'  )     ");
            $mid = implode(",", $mid);
            if ($mid) {
                $where .= " and Mid  in ($mid) ";
            } else {
                $where .= " and Mid = 0 ";
            }
        }

        if ($params['sort'] == "1") {
            $where2 = " ORDER BY SalesMinPrice DESC,";
        }
        if ($params['sort'] == "2" || $params['sort'] == '') {
            $where2 = " ORDER BY sm_exc_sales.CreateDate DESC,";
        }

        // updated by quanjw for isec start 2016/2/1
        if (DZ_OR_YGW == "0") {
            if (!$params['d'] || $params['d'] == 'all' || $params['d'] == 'undefined') {
                $where .= " AND sm_exc_sales_details.Vid not in ( " . $GLOBALS['MEITAN_VID'] . "," . $GLOBALS['JIAOTAN_VID'] . "," . $GLOBALS['SHUINI_VID'] . "," . $GLOBALS['JINSHU_VID'] . ")";
            }
        }
        // updated by quanjw for isec end 2016/2/1

        if ($params['st'] == "1") {
            if (DZ_OR_YGW == "0") { //大宗
                $where .= " and SalesType  in (1,5) ";
            }
        }
        if ($params['st'] == "2") {
            if (DZ_OR_YGW == "0") { //大宗
                $where .= " and SalesType  = 2 ";
            } else {  // 易钢云商 商务通
                $where .= " and SalesType in(2,8) ";
            }
        }
        $qhstr = "reczy";
        if ($params['st'] == "3" || $params['st'] == "10") {
            $where .= " and SalesType  in (1,5) ";
            $where .= "and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom where CompanyType = '1')";
            $qhstr = "gczq";
        }
        if ($params['st'] == "4" || $params['st'] == "9") {
            $where .= "and sm_exc_sales.Mid in (select Mid from sm_sys_storecustom where CompanyType = '2')";
            $qhstr = "gcwl";
        }
        if ($params['st'] == "5") {
            if (DZ_OR_YGW == "0") { //大宗
                $where .= "and sm_exc_sales.Mid = 153";
            } else {    // 易钢云商 商务通
                $where .= "and sm_exc_sales.Mid = 1";
            }
            $qhstr = "bzzy";
        }

        if ($params['st'] == "6") {
            $where .= " and SalesType=8  and Isdxcg!=1";
            $qhstr = "gcwl";
        }
        if ($params['st'] == "7") {
            $where .= " and SalesType=2 ";
            $qhstr = "xhcg";
        }
        if ($params['st'] == "8") {
            $where .= " and SalesType=5 ";
            $qhstr = "xhxs";
        }
        $this->assign("qhstr", $qhstr);
        if ($params['new'] == "1") {
            $wheregp = " GROUP BY Mid";
        }

        //获取大品种 数目
        $bigpz = $this->_dao->AQuery("select ID,PzName from sys_big_pz  where Status = 1  order by Ssort", 3600);
        $this->assign("bigpz", $bigpz);


        $page = $params['page'] == '' ? 1 : $params['page'];
        //Updated by quanjw for yema start 2015/3/24
        $per = 23;
        if (DZ_OR_YGW == '1' || DZ_OR_YGW == '2') { //易钢云商 商务通
            $per = 25;
        }

        //$per = 26;
        //Update by quanjw for yema end 2015/3/24
        $start = ($page - 1) * $per;

        $wherepz = "";
        if ($params["pz"] == 1) {
            $wherepz = " and Vid=22 ";
        } elseif ($params["pz"] == 2) {
            $wherepz = " and Vid=1 ";
        } elseif ($params["pz"] == 3) {
            $wherepz = " and Vid=4 ";
        } elseif ($params["pz"] == 4) {
            $wherepz = " and Vid=5 ";
        } elseif ($params["pz"] == 5) {
            $wherepz = " and Vid=3 ";
        } elseif ($params["pz"] == 6) {
            $wherepz = " and Vid=17 ";
        } elseif ($params["pz"] == 7) {
            $wherepz = " and Vid=10 ";
        } elseif ($params["pz"] == 8) {
            $wherepz = " and Vid=11 ";
        } elseif ($params["pz"] == 9) {
            $wherepz = " and Vid=9 ";
        } elseif ($params["pz"] == 10) {
            $wherepz = " and Vid=12 ";
        } elseif ($params["pz"] == "") {
            $wherepz = " ";
        }
        $wherepm = "";
        if ($params["pm"] != "") {
            $pmid = $this->_dao->getOne("select id from biz_key where kname='" . $params["pm"] . "'");
            if ($pmid == "" || $pmid == null) {
                $pmid = $this->_dao->getOne("select k_id from key_alias where aname='" . $params["pm"] . "'");
            }
            $wherepm = " and (pm_id=" . $pmid . " or pm_parentid=" . $pmid . " or pm_ppid=" . $pmid . ") ";
        }
        $wherecity = "";
        if ($params["city"] != "") {
            $wherecity = " and PickUpCity like '%" . $params["city"] . "%' ";
        }
        $data = $this->_dao->query("SELECT sm_exc_sales_details.*,sm_exc_sales.*, sys_company.ComNameShort,sys_company.ComName,(select ARealName from sys_adminuser where ComID = sys_company.ID  and IsMain=1 and Status=1 ) as LinkMan ,sys_company.ContactTel,sys_company.Address  FROM sm_exc_sales, sys_company,sm_exc_sales_details 
				WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID AND sm_exc_sales.Status =2 and SalesEndDate>'" . date("Y-m-d H:i:s", time()) . "' " . $wherepz . $wherepm . $wherecity . " and sm_exc_sales.IsRec=1
				ORDER BY sm_exc_sales.CreateDate DESC LIMIT $start, $per", 0);

        $total = $this->_dao->getOne("SELECT COUNT(sm_exc_sales_details.ID) FROM sm_exc_sales_details,sm_exc_sales, sys_company 
			WHERE sys_company.ID = sm_exc_sales.Mid and sm_exc_sales_details.Pid=sm_exc_sales.ID  AND sm_exc_sales.Status =2 $where $wherepz $wherepm $wherecity and SalesEndDate>'" . date("Y-m-d H:i:s", time()) . "' and sm_exc_sales.IsRec=1");
        //}
        $totalpage = ceil($total / $per);
        //----------------组织数据开始
        $record = array();

        foreach ($data as $d) {
            //Add by xiakang started 2015/03/20
            $d['ContactTel'] = hidtel($d['ContactTel'], $_SESSION['SYS_COMPANYID']);
            //Add by xiakang ended 2015/03/20
            $tmp =  array();
            $tmp[] = "<a href='listing.php?view=zylist&mid=" . $d['Mid'] . " ' target='_blank'>" . mb_substr($d['ComNameShort'], 0, 5, 'gb2312') . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['VarietyName'], 0, 4, 'gb2312') . "</a>";

            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['MaterialCode'], 0, 6, 'gb2312') . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['SpecCode'], 0, 7, 'gb2312') . "</a>";

            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['OriginCode'], 0, 4, 'gb2312') . "</a>";

            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['PickUpCity'], 0, 4, 'gb2312') . "</a>";
            if ($d['SalesMinPrice'] == '0.00' || $d['SalesMinPrice'] == '' || $d['TradeType'] == '3') {
                $tmp[] = "协议价";
            } else {
                $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank' >" . floor($d['SalesMinPrice']) . "</a>";
            }
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank' >" . $d['QuantitySalesed'] . "/" . $d['QuantitySales'] . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank' >" . $d['QuantitySalesed'] . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank' >" . date("m/d", strtotime($d['CreateDate'])) . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank' >" . $GLOBALS['ZY_ALL_TYPES'][$d['TradeType']] . "</a>";

            //Update by xiakang for haomajiami started 2015/03/20
            $phone = hidtel(PHONENUM, $_SESSION['SYS_COMPANYID']);

            $tmp[] = "<span></span>
					<dt>" . $d['ComName'] . "</dt>
					<dd>
						<p>公司地址：" . $d['Address'] . "</p>
						<p>联系电话：" . $d['ContactTel'] . "</p>
						<p>" . kefu . "" . $phone . "</p>
					</dd> ";
            //Update by xiakang for haomajiami ended 2015/03/20


            $tmp[] = "<TABLE width=\"100%\" border=0 cellPadding=1 cellSpacing=1 bgcolor=\"#BFE1FF\">
								  <TBODY>
								  <TR bgColor=#FFFFFF >
									<TD colspan=\"2\" style=\"FONT-SIZE: 12px;line-height:24px;background-image:url(/images/company_cmanbg_new.gif);\" >
									<strong>" . $d['ComName'] . "</strong></TD>
								  </TR>
								  <TR bgColor=#FFFFFF>
									<TD colspan=\"2\"  style=\"FONT-SIZE: 12px; line-height:24px;\">
									  　交货仓库：" . $d['jhck'] . "<br>
									  　交货地址：" . $d['PickUpAddress'] . "<br></TD>
									</TR>
								  </TBODY>
								</TABLE> ";

            //update by xiakang started 2015/10/16
            //$tmp[] = "<input name=\"res_id[]".$qhstr."\" type=\"checkbox\" value=".$d['ID'].">";
            $tmp[] = "<input name=\"res_id[]\" type=\"checkbox\" value=" . $d['ID'] . ">";
            //update by xiakang ended 2015/10/16
            if ($d['QQNum'] != "") {
                $xxxt = "<a href=\"//wpa.qq.com/msgrd?v=3&uin=" . $d['QQNum'] . "&site=qq&menu=yes\"><img src=\"/img/talk_qq.png\" target='_top' /></a>";
            } else {
                $xxxt = "";
            }

            $tmp[] = "<a href=\"javascript:checkHasAndChatTo(" . $d['CreateUser'] . ")\"><img class=\"img_" . $d['CreateUser'] . "\" id=\"img_" . $d['CreateUser'] . "\" src=\"images/gmt4.gif\" style=\"border:0;\" ></a>" . $xxxt;

            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "&talk=1' target='_blank'>留言</a>";

            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . date('m/d', strtotime($d['CreateDate'])) . "</a>";

            //Updated by quanjw for jinshuzhipin start 2015/3/30	
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['cd'], 0, 6, 'gb2312') . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['strength'], 0, 6, 'gb2312') . "</a>";
            $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'>" . mb_substr($d['xincengWeight'], 0, 6, 'gb2312') . "</a>";
            if (DZ_OR_YGW == '1' || DZ_OR_YGW == '2') { //易钢云商 商务通
                $flag = "2";
                $nowtime = date('Y-m-d H:i:s');
                if ($d['SalesStartDate'] < $nowtime && ($d['SalesEndDate'] > $nowtime)) {
                    $flag = "";
                }

                $salestp = "sales";
                if ($d['SalesType'] == 1 || $d['SalesType'] == 5  || $d['SalesType'] == 6) {
                    $salestp = "buy";
                }
                $img = "images/" . $salestp . $flag . ".jpg";
                $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'><img src='images/$salestp$flag.jpg' style='border:0' /></a>";
            } else {
                $flag = "2";
                $nowtime = date('Y-m-d H:i:s');
                if ($d['SalesStartDate'] < $nowtime && ($d['SalesEndDate'] > $nowtime)) {
                    $flag = "";
                }
                $salestp = "sales";
                if ($d['SalesType'] == 1 || $d['SalesType'] == 5  || $d['SalesType'] == 6) {
                    $salestp = "buy";
                }
                $img = "images/" . $salestp . $flag . ".jpg";
                $tmp[] = "<a href='index.php?view=jinjia_info&cat=" . $d['SalesType'] . "&id=" . $d['ID'] . "' target='_blank'><img src='images/$salestp$flag.jpg' style='border:0' /></a>";
            }
            //Updated by quanjw for jinshuzhipin end 2015/3/30
            //  $tmp[]=$d['SalesType'];
            $record[] = implode("|X|", $tmp);
        }
        $record = implode("|O|", $record);
        //总页, 当前页
        $others = $totalpage . "|A|" . $page;
        echo $params['type'] . "|T|" . $params['d'] . "|T|" . $record . "|H|" . $others;
        exit;
    }

    public function ajaxgetreczyqy($params)
    {
        $page = $params['page'] == '' ? 1 : $params['page'];
        if ($params['more'] == "more") {
            $per = 100;
        } else {
            $per = 54;
        }
        $start = ($page - 1) * $per;
        $nowdate = date("Y-m-d H:i:s", time());
        $wherepm = "";
        if ($params["pm"] != "") {
            $pmid = $this->_dao->getOne("select id from biz_key where kname='" . $params["pm"] . "'");
            if ($pmid == "" || $pmid == null) {
                $pmid = $this->_dao->getOne("select k_id from key_alias where aname='" . $params["pm"] . "'");
            }
            $wherepm = " and (pm_id=" . $pmid . " or pm_parentid=" . $pmid . " or pm_ppid=" . $pmid . ") ";
        }
        $wherecity = "";

        $wherecity .= $this->getwhere("PickUpCity", $params['city'], LIKE);

        $company2 = $this->_dao->query("select sys_company.id mid, ComName, sum(QuantitySales-QuantitySalesed) Quantity from sm_exc_sales, sys_company, sm_exc_sales_details 
			where IsRec = 1 and sys_company.id=sm_exc_sales.mid and sm_exc_sales.ID=sm_exc_sales_details.Pid and sm_exc_sales.status=2 and sm_exc_sales.SalesEndDate>'" . $nowdate . "' and sm_exc_sales.IsRec=1 $wherepm $wherecity
			group by ComName order by RecDate DESC limit $start, $per", 0);
        foreach ($company2 as &$v) {
            $v['Quantity'] = $this->_dao->getOne("select sum(QuantitySales-QuantitySalesed) Quantity from sm_exc_sales, sm_exc_sales_details 
			where " . $v['mid'] . "=sm_exc_sales.mid and sm_exc_sales.ID=sm_exc_sales_details.Pid and sm_exc_sales.status=2 and sm_exc_sales.SalesEndDate>'" . $nowdate . "'
			");
        }
        $totalalls = $this->_dao->query("select ComName, sum(QuantitySales-QuantitySalesed) Quantity from sm_exc_sales, sys_company, sm_exc_sales_details 
			where IsRec = 1 and sys_company.id=sm_exc_sales.mid and sm_exc_sales.ID=sm_exc_sales_details.Pid and sm_exc_sales.status=2 and sm_exc_sales.SalesEndDate>'" . $nowdate . "' and sm_exc_sales.IsRec=1 $wherepm $wherecity
			group by ComName", 0);

        foreach ($company2 as $d) {
            $tmp =  array();
            $tmp[] = "<a href='listing.php?view=zylist&mid=" . $d['Mid'] . " ' target='_blank'>" . $d['ComName'] . "资源&nbsp;" . $d['Quantity'] . "吨</a>";
            //$tmp[] = "<a href='index.php?view=jinjia_info&cat=".$d['SalesType']."&id=".$d['ID']."' target='_blank'>"."bbbb"."</a>";

            $record[] = implode("|X|", $tmp);
        }
        $record = implode("|O|", $record);
        //总页, 当前页
        $totalpage = ceil($total / $per);
        $others = $totalpage . "|A|" . $page;
        echo "|T|" . "|T|" . $record . "|H|" . $others;
        exit;
    }
    //added by tuxw for isec ended 20160128
    //added by quanjw for pushres
    public function updgzjres($params)
    {
        $resall = json_decode(html_entity_decode(urldecode($params['res'])), true);
        foreach ($resall as $resid) {

            $res = $this->_dao->getRow("select * from sm_exc_sales where fromResId = '" . $resid['id'] . "'");
            if ($res) {
                $sql_upd_res = "update sm_exc_sales set SalesStartDate='" . date('Y-m-d H:i:s') . "',SalesEndDate= '" . date("Y-m-d H:i:s", strtotime("+5 day")) . "'where Resources = 2 and fromResId = '" . $resid['id'] . "'";
                $this->_dao->execute($sql_upd_res);
            } else {
                $this->pushgzjres($resid['id']);
            }
        }
    }

    private function pushgzjres($id)
    {
        $push_url = GZJ_WEB_SITE . "/phptools/pushres/pushres.php?pushResId=" . $id . "&url=" . WEB_SITE;
        file_get_contents($push_url);
        file_put_contents("/tmp/pushgzjres", $push_url);
    }
    //added by quanjw for pushres
    //added by shizg for dingdangpingjia started 2016/12/06

    public function param_check($param)
    {

        $param = addslashes($param); // 进行magic_quotes_gpc没有打开的情况对提交数据的过滤

        // $param = str_replace("_", "\_", $param); // 把 '_'过滤掉
        // $param = str_replace("%", "\%", $param); // 把' % '过滤掉
        $param = nl2br($param); // 回车转换
        $param = htmlspecialchars($param); // html标记转换
        return $param;
    }


    public function updatekeyword()
    {
        global  $MEMCACHE_SERVER, $MEMCACHE_PORT;
        $cache = new Memcache();
        $cache->connect($MEMCACHE_SERVER, $MEMCACHE_PORT);

        $sql = "SELECT key_word,type FROM ly_keyword WHERE type=1 or type='2' ";
        $rs = $this->t1->query($sql);
        $type1 = array();
        $type2 = array();
        foreach ($rs as $tmp) {
            if ($tmp["type"] == 1) {
                $type1[] = str_replace(array(".", "/", "*", "+"), array("\.", "\/", "\*", "\+"), $tmp["key_word"]);
            } else {
                $type2[] = '((?=.*' . str_replace("+", ")(?=.*", str_replace(array(".", "/", "*"), array("\.", "\/", "\*"), $tmp["key_word"])) . '))';
            }
        }
        $cache->set('key_word_type1', $type1, MEMCACHE_COMPRESSED, 86400);
        $cache->set('key_word_type2', $type2, MEMCACHE_COMPRESSED, 86400);
        $cache->close();

        echo '关键字缓存成功！';
        exit;
    }







    public function tijiao_pingjia($params)
    {
        $DDid = $params['DDid'];
        $ManYi = $params['ManYi'];
        $Description = $params['Description'];
        $Service = $params['Service'];
        $FaHuoSpeed = $params['FaHuoSpeed'];
        $Quality = $params['Quality'];
        $Purches = $params['Purches'];
        $Remark = $params['pingjia_remark'];

        $content = $this->param_check(trim($Remark));


        global  $MEMCACHE_SERVER, $MEMCACHE_PORT;
        $cache = new Memcache();
        $cache->connect($MEMCACHE_SERVER, $MEMCACHE_PORT);
        $type1 = $cache->get('key_word_type1');
        $type2 = $cache->get('key_word_type2');
        if (!$type1) //若缓存无数据请求数据库将数据存缓存
        {

            $sql = "SELECT key_word,type FROM ly_keyword WHERE type=1 or type=2 ";
            $rs = $this->t1->query($sql);
            $type1 = array();
            $type2 = array();
            foreach ($rs as $tmp) {
                if ($tmp["type"] == 1) {
                    $type1[] = str_replace(array(".", "/", "*", "+"), array("\.", "\/", "\*", "\+"), $tmp["key_word"]);
                } else {
                    $type2[] = '((?=.*' . str_replace("+", ")(?=.*", str_replace(array(".", "/", "*"), array("\.", "\/", "\*"), $tmp["key_word"])) . '))';
                }
            }

            $cache->set('key_word_type1', $type1, MEMCACHE_COMPRESSED, 86400);
            $cache->set('key_word_type2', $type2, MEMCACHE_COMPRESSED, 86400);
        }
        $cache->close();
        $type1array = array_chunk($type1, 500);
        if (is_array($type1array) && !empty($type1array)) {
            foreach ($type1array as $banned_words) {
                $zhengze = implode('|', $banned_words);
                $zhengze = '/(' . $zhengze . ')/i';
                $str = str_replace("\r\n", "<br />", $content);
                $str = htmlspecialchars_decode($str);
                if (preg_match_all($zhengze, $str, $matches)) {
                    alert("评价内容不符合规范,请重新输入！");
                    goback();
                    exit;
                }
            }
        }

        $type2array = array_chunk($type2, 500);
        if (is_array($type2array) && !empty($type2array)) {
            foreach ($type2array as $banned_words) {
                $zhengze = implode('|', $banned_words);
                $zhengze = '/(' . $zhengze . ')/i';
                $str = str_replace("\r\n", "<br />", $content);
                $str = htmlspecialchars_decode($str);
                if (preg_match_all($zhengze, $str, $matches)) {
                    alert("评价内容不符合规范,请重新输入！");
                    goback();
                    exit;
                }
            }
        }

        $comp_ddpj = $this->_dao->get_sys_company($_SESSION['SYS_COMPANYID']);

        $sqlsearch = "select * from sm_contract_transaction where BID = '" . $DDid . "' "; //查询
        $result = $this->_dao->getRow($sqlsearch);
        if ($comp_ddpj['ID'] == $result['Mid_Shipper']) {
            $isSeller = 1;
            $comid = $result['Mid_Consignee'];
        }
        if ($comp_ddpj['ID'] == $result['Mid_Consignee']) {
            $isSeller = 0;
            $comid = $result['Mid_Shipper'];
        }

        $pingjia_exits = $this->_dao->getRow("SELECT * FROM PingJia WHERE Ddid='" . $DDid . "' and CompanyId='" . $_SESSION['SYS_COMPANYID'] . "'");
        if (!empty($pingjia_exits)) {

            $this->_dao->execute("update PingJia set isSeller = '" . $isSeller . "',ComName = '" . $comp_ddpj['ComName'] . "',ComShort = '" . $comp_ddpj['ComNameShort'] . "',ManYi = '" . $ManYi . "',Description = '" . $Description . "',Service = '" . $Service . "',FaHuoSpeed = '" . $FaHuoSpeed . "',Quality = '" . $Quality . "',Purches = '" . $Purches . "',Remark = '" . $Remark . "',Date=now() where CompanyId='" . $comp_ddpj['ID'] . "'and Ddid = '" . $DDid . "'");

            if ($isSeller == 0) {
                $PingJiaZongFen2 = $ManYi * 0.2 + $Description * 0.2 + $Service * 0.2 + $FaHuoSpeed * 0.2 + $Quality * 0.2;
            } else {
                $PingJiaZongFen2 = $ManYi * 0.5 + 0.5 * $Purches;
            } //更改之后的评价分数
            if ($isSeller == 0) {
                $PingJiaZongFen1 = $pingjia_exits['ManYi'] * 0.2 + $pingjia_exits['Description'] * 0.2 + $pingjia_exits['Service'] * 0.2 + $pingjia_exits['FaHuoSpeed'] * 0.2 + $pingjia_exits['Quality'] * 0.2;
            } else {
                $PingJiaZongFen1 = $pingjia_exits['ManYi'] * 0.5 + 0.5 * $pingjia_exits['Purches'];
            } //更改之前的评价分数
            if ($PingJiaZongFen2 >= '5' && $PingJiaZongFen2 <= '0') {
                alert("评价总分有误！请重新评价。");
                exit;
            }
            if ($isSeller == 0) {
                $this->_dao->execute("update sys_company set PingJiaZongFen1 = PingJiaZongFen1+" . $PingJiaZongFen2 . "-" . $PingJiaZongFen1 . " where ID='" . $comid . "' ");
            } else {
                $this->_dao->execute("update sys_company set PingJiaZongFen2 = PingJiaZongFen2+" . $PingJiaZongFen2 . "-" . $PingJiaZongFen1 . " where ID='" . $comid . "' ");
            }
            //	alert("更新评价订单成功!");
        } else {
            $this->_dao->execute("insert into PingJia set CompanyId='" . $comp_ddpj['ID'] . "',Ddid = '" . $DDid . "',isSeller = '" . $isSeller . "',ComName = '" . $comp_ddpj['ComName'] . "',ComShort = '" . $comp_ddpj['ComNameShort'] . "',ManYi = '" . $ManYi . "',Description = '" . $Description . "',Service = '" . $Service . "',FaHuoSpeed = '" . $FaHuoSpeed . "',Quality = '" . $Quality . "',Purches = '" . $Purches . "',Remark = '" . $Remark . "',Date=now() ");

            //	alert("评价订单成功!");

            if ($isSeller == 0) {
                $PingJiaZongFen = $ManYi * 0.2 + $Description * 0.2 + $Service * 0.2 + $FaHuoSpeed * 0.2 + $Quality * 0.2; //评价总分数
            } else {
                $PingJiaZongFen = $ManYi * 0.5 + 0.5 * $Purches; //评价总分数
            }
            if ($PingJiaZongFen >= '5' && $PingJiaZongFen <= '0') {
                alert("评价总分有误！请重新评价。");
                exit;
            }
            if ($isSeller == 0) {
                $this->_dao->execute(
                    "update sys_company set PingJiaZongFen1 = PingJiaZongFen1+" . $PingJiaZongFen . ", PingJiaNum1 = PingJiaNum1+1 where ID='" . $comid . "' "
                );
            } else {
                $this->_dao->execute(
                    "update sys_company set PingJiaZongFen2 = PingJiaZongFen2+" . $PingJiaZongFen . ", PingJiaNum2 = PingJiaNum2+1 where ID='" . $comid . "' "
                );
            }
        }

        alert("订单评价成功！");
        goURL("bizorder.php?view=orderdetail&id=" . $DDid . "&pingjia=1");
    }

    public function ddpjuploadpic($params)
    {
        $DDid = $params['DDid'];
        $this->assign("DDid", $DDid);
    }

    public function ddpjuploadpicture($params)
    {
        $DDid = $params['DDid'];

        if (empty($_FILES["file"]["name"])) {
            alert("请选择！");
            goback();
            exit;
        }
        //检查上传文件是否是图片
        if (strpos($_FILES["file"]["type"], 'image/') === false) {
            alert("只能上传图片文件，请重新选择！");
            goback();
            exit;
        }
        $sql_pic = "select count(ID) from sm_exc_dd_pics where CompanyId='" . $_SESSION['SYS_COMPANYID'] . "' and  Ddid = '" . $DDid . "' "; //查询
        $pingjia_picnum = $this->_dao->getOne($sql_pic);
        //alert($pingjia_picnum);
        if ($pingjia_picnum >= 5) {
            alert("上传图片最多为五张！");
            goback();
            exit;
        }
        if (!$this->is_safe($_FILES["file"]["tmp_name"])) {
            alert("不要恶意伤害我！");
            goback();
            exit;
        }
        include("miniature.php");
        $min = new CreatMiniature();
        $picturename = $_FILES["file"]["name"];
        @$houzhui = end(explode(".", $picturename));
        $sql1 = "select uuid() ";   //取出一个uuid
        $uuid = $this->_dao->getOne($sql1);
        $nametime = date('YmdHis', time());
        $filename = $nametime . "." . $houzhui;
        $filename2 = $nametime . "_small" . "." . $houzhui;
        $path1 = $_SERVER["DOCUMENT_ROOT"] . "/uploadfile/picture/" . $uuid . "/" . $filename;
        $f_dir = $_SERVER["DOCUMENT_ROOT"] . "/uploadfile/picture/" . $uuid . "/";
        if (is_dir($f_dir) || @mkdir($f_dir)) {
        }
        move_uploaded_file($_FILES["file"]["tmp_name"], $f_dir . $filename);
        //生成缩略图
        $path2 = $_SERVER["DOCUMENT_ROOT"] . "/uploadfile/picture/" . $uuid . "/" . $filename2;
        $min->SetVar($path1, "file");
        $image_info = getimagesize($path1);
        $width = $image_info['0'];
        $height = $image_info['1'];
        if ($width > $height) {
            $w = $width;
            $h = $height;
        } else {
            $h = $width;
            $w = $height;
        }
        $small = $min->Prorate($path2, 200, $h * 200 / $w);
        $path11 = "/uploadfile/picture/" . $uuid . "/" . $filename;
        $path21 = "/uploadfile/picture/" . $uuid . "/" . $filename2;
        $sql_ddpjpic = "insert into sm_exc_dd_pics set CompanyId='" . $_SESSION['SYS_COMPANYID'] . "',Ddid = '" . $DDid . "',Path = '" . $path11 . "',ThumbnailURL = '" . $path21 . "',Date = '" . date('Y-m-d H:i:s', time()) . "' ";   //上传图片
        $this->_dao->execute($sql_ddpjpic);


        //alert( "新增成功！" );		
        echo "<script type='text/javascript' src='js/jquery.min.js'></script>";
        echo "<script>
            var url='bizorder.php?view=reloadpicture&DDid=$DDid';
            $.get(url, function(result){
            window.opener.document.getElementById('ddpj_pictu').innerHTML=result;
            });
            </script>";
        echo '<script>
            setTimeout(alert("新增成功！"), 1000 )
            </script>';
        echo '<script>window.close();</script>';
    }

    public function deleteddpjpicture($params)
    {

        $PicId = $params['picid'];
        $sql = "delete from sm_exc_dd_pics where ID in(" . $PicId . ") ";   //删除图片
        $this->_dao->execute($sql);
    }

    public function reloadpicture($params)
    {

        $DDid = $params['DDid'];
        $sql_pic = "select ID,Path,ThumbnailURL,Date from sm_exc_dd_pics where CompanyId='" . $_SESSION['SYS_COMPANYID'] . "' and  Ddid = '" . $DDid . "' "; //查询
        $pingjia_pic = $this->_dao->query($sql_pic);
        $this->assign("pingjia_pic", $pingjia_pic);
    }

    //added by shizg for dingdangpingjia ended 2016/12/06

    public function unlocktables($params)
    {
        $this->_dao->execute("unlock tables");
    }
}
