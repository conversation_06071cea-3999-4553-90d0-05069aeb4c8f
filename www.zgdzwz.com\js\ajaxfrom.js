
//$(document).ready(function() {$("input[name=SalesType]:eq(0)").attr("checked",'checked'); });
//$(document).ready(function() {$("input[name=TradeType]:eq(0)").attr("checked",'checked'); });
//$(document).ready(function() {$("input[name=PickUpType]:eq(0)").attr("checked",'checked'); });

//$(document).ready(function() {$("input[name=SalesUnit]:eq(0)").attr("checked",'checked'); });

//$(document).ready(function() {$("input[name=Delivery]:eq(0)").attr("checked",'checked'); });

//$(document).ready(function() {$("input[name=xyjgtypes]:eq(0)").attr("checked",'checked'); });


Date.prototype.format = function (format) {

	var o = {
		"M+" : this.getMonth() + 1, //month
		"d+" : this.getDate(), //day
		"h+" : this.getHours(), //hour
		"m+" : this.getMinutes(), //minute
		"s+" : this.getSeconds(), //second
		"q+" : Math.floor((this.getMonth() + 3) / 3), //quarter
		"S" : this.getMilliseconds() //millisecond
	}

	if (/(y+)/.test(format)) {
		format = format.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
	}

	for (var k in o) {
		if (new RegExp("(" + k + ")").test(format)) {
			format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
		}
	}
	return format;
}

function postData() {
	/**
	 * formObj 需要提交的form表单对象 --必要属性
	 * method 何种方式提交(post或get) 当用get方法提交时将不会对form表单里面的数据进行封装--默认为post
	 * type   返回值是何种类型 text 或 xml --默认为text
	 * myNeaten 自定义封装数据方法(没有可用 null)
	 * myProcess 自定义显示数据方法(有此myProcess方法那么conname可以为空) 没有可用 null
	 * conname=document.getElementById("show") 容器id,用于显示返回信息(如:<div id="show"></div>) 没有可用 null
	 */
	//submitRequest(formObj,method,type,myProcess,conname);


	//submitRequest(document.form1,"post","text",null,document.getElementById( "show" ));//使用原有myProcess

	if (document.jgform.IsTalk.checked == false) {
		document.jgform.IsTalk.value = 0;
	}

	if(document.jgform.PickUpCity.value == ""){
		alert("交割城市不能为空");
		return false;
	}
	var salety = document.zyform.salety.value;
	var date1 = new Date();
	date1.setMonth(date1.getMonth() + 1);
	if (salety == "16") {
		//alert(date1.format("yyyy-MM-dd"));
		if (document.jgform.PickUpDate.value < date1.format("yyyy-MM-dd")) {
			alert("交货日期必须超过1个月");
			return false;
		}

	}

	submitRequest(document.jgform, "post", "text", outWrite, null); //使用自定义myProcess
}

//AJAX出现结果，处理变化
function outWrite2(count) {
	document.getElementById("content").innerHTML = "";
	var div = document.getElementById("show");
	div.innerHTML = "aaa" + count;
}

function outWrite(count) {
	//alert(count);
	var tmp = count.split("|-|");

	document.getElementById("jgtype").innerHTML = tmp[0];
	document.getElementById("jgdate").innerHTML = tmp[1];
	document.getElementById("jgcity").innerHTML = tmp[2];
	document.getElementById("jgadress").innerHTML = tmp[3];
	document.getElementById("jgfkfs").innerHTML = tmp[5];
	document.getElementById("jgDelivery").innerHTML = tmp[6];
	document.getElementById("jgjhck").innerHTML = tmp[7];
	document.getElementById("jgStoreCity").innerHTML = tmp[8];
	document.getElementById("LyMoney").innerHTML = tmp[9] + "%";
	if (tmp[10] == 1) {
		document.getElementById("IsTalk").innerHTML = '<input type="checkbox" value="1" name="IsTalk" disabled checked >付款及交割可洽谈';
	} else {
		document.getElementById("IsTalk").innerHTML = '<input type="checkbox" value="1" name="IsTalk" disabled >付款及交割可洽谈';
	}

	document.forms['jgform'].flag.value = tmp[4];
	document.getElementById("zyflag").value = tmp[4];
	// document.getElementById("zyid").value=tmp[4];

	document.getElementById("jgdiv").style.display = "none"; //隐藏
	// var div = document.getElementById( "show" );
	document.getElementById("show").style.display = "block";
	//  div.innerHTML =ht;


	//显示联系人相关信息
	// document.getElementById("showfh").style.display="none";
	// document.getElementById("fhdiv").style.display="block";

	////显示附件相关信息
	//document.getElementById("fjdiv").style.display="block";
	//document.getElementById("showfj").style.display="none";

}

function editjg() {
	var ss = document.getElementById("fkxstype").value;

	if (ss == "1") {
		document.getElementById("xjhz").style.display = '';
	} else if (ss == "2") {
		document.getElementById("yhcd").style.display = '';
	} else if (ss == "3") {
		document.getElementById("other").style.display = '';
	}
	setcdhp(ss);

	//document.getElementById("fkclick").click();
	var dd = document.getElementById("fkxstype2").value;
	if (dd == "1" || dd == "2" || dd == "3") {}
	if (dd == "4") {
		chengui(dd);
	}
	if (dd == "7" || dd == "8") {
		var obj = document.getElementsByName("fkxs");
		for (i = 0; i < obj.length; i++) {

			if (obj[i].value == "0") {
				obj[i].checked = true;
			}
		}
		danbao(0);
	}

	if (dd == "6" || dd == "9") {
		danbao(dd);
	}

	var rlist = document.getElementsByName("PickUpType");
	var aa = true;

	for (var j = 0; j < rlist.length; j++) {
		if (rlist[j].checked) {
			aa = false
		}
	}

	if (aa)

		$(document).ready(function () {
			$("input[name=PickUpType]:eq(0)").attr("checked", 'checked');
		});

	document.getElementById("show").style.display = "none"; //隐藏
	document.getElementById("jgdiv").style.display = "block"; //隐藏

}
function closejg() {
	document.getElementById("show").style.display = "block";
	document.getElementById("jgdiv").style.display = "none";
}

function editzy() {

	document.getElementById("showzy").style.display = "none"; //隐藏
	document.getElementById("zydiv").style.display = "block"; //隐藏
	my_on_init();

	setother(document.getElementById('secparentid'));

}

function closezy() {
	document.getElementById("showzy").style.display = "block";
	document.getElementById("zydiv").style.display = "none";
}
function closemgzy() {
    document.getElementById("showzy").style.display = "block";
    document.getElementById("zydiv").style.display = "none";
    document.getElementById("showzy_notmatch").style.display = "block";
}
//Added by quanjw for meiijao start 2014/12/31
function meitanpostData() {

	var SCDate = document.getElementById("MfgDate2").value;

	SCDate = SCDate + " 00:00:00";

	var FBDate = document.getElementById("CreateDate1").innerHTML;

	var SCDate = new Date(SCDate.replace("-", "/").replace("-", "/"));
	var FBDate = new Date(FBDate.replace("-", "/").replace("-", "/"));
	if (SCDate > FBDate) {
		alert("生产日期不能大于发布日期,请重新填写！");
		return fales;
	}

	//anyAjaxObj.setRequestHeader("If-Modified-Since","0");
	var xszytype = document.getElementsByName("SalesType");

	for (var m = 0; m < xszytype.length; m++) {
		if (xszytype[m].checked) {
			zytype = xszytype[m].value;
		}
	}
	//alert(typedif);
	//alert(zytype);
	var zytype = document.zyform.salety.value;
	var typedif = document.zyform.typedif.value;

	var myDate = new Date();
	var sysdate = myDate.format('yyyy-MM-dd hh:mm:ss');

	strDate1 = document.zyform.SalesEndDate.value;
	strDate2 = document.zyform.SalesStartDate.value;
	endDate = Date.parse(strDate1.replace(/\-/g, "/"));
	startDate = Date.parse(strDate2.replace(/\-/g, "/"));
	var sjc = (endDate - startDate) / (60 * 60 * 1000);

	var flag;
	if (typedif != "") {
		flag = "1";
	} //新增
	if (zytype != "") {
		flag = "2";
	} //修改
	if (document.zyform.VarietyName.value == "") {
		alert("品名不能为空");
		return false;
	}

	if (isNaN(document.zyform.QuantitySales.value)) {

		alert("数量不能为非数字");
		return false;
	}
		var chk;
		var chkObjs = document.zyform.TradeType; //取出报价方式的radio,
		//alert(chkObjs);

		for (var i = 0; i < chkObjs.length; i++) { //循环取出选择项
			if (chkObjs[i].checked) {
				chk = i;

				break;
			}
		}
if(document.zyform.SalesMinPrice.value=="")//判断价格填写框有没有值
{	
		if (chk == "0") //判断是否是锁定价格方式
		{
			var chk2;
			var chkObjs2 = document.zyform.Yijia;
			for (var i = 0; i < chkObjs2.length; i++) {
				if (chkObjs2[i].checked) {
					chk2 = i;
					//alert(chk2);
					break;

				}
			}
			if (chk2 == "1") {
				alert("您没有填写资源价格，不能选择锁定价格或不可议价。");
				return false;
			}
		}
	}

	if (zytype != "16" && typedif != "5" && typedif != "6") {

		if (zytype != "16" && typedif != "15" && typedif != "16") {

			if (document.zyform.SpecCode.value == "") {
				alert("灰分不能为空");
				return false;
			}
			if (document.zyform.MaterialCode.value == "") {
				alert("挥发分不能为空");
				return false;
			}
			if (document.zyform.cd.value == "") {
				alert("全硫分不能为空");
				return false;
			}

		}

	}
	/*
	if(document.zyform.StoreCityCode.value == "" && document.zyform.StoreCity.value == "" ){
	alert("所在城市不能为空");
	return false;
	}*/

/*if(document.zyform.OriginCode.value == "" && chk!='1'  && document.zyform.SalesType.value=='5'){
alert("产地(厂家)不能为空");
return false;
}*/
if( chk=='1'  && (document.zyform.SalesType.value=='2' || document.zyform.SalesType.value=='8')){
}else{
	if(document.zyform.OriginCode.value == "" ){
	alert("厂家不能为空");
	return false;
	}
}
/*
if(zytype =="1"){
	if(document.zyform.jhck.value == ""){
	alert("交货仓库不能为空");
	return false;
	}
	}*/
	if (document.zyform.QuantitySales.value == "") {

		if (zytype == "2" || zytype == "6" || typedif == "3" || typedif == "4") {
			alert("购买数量不能为空");
		} else {
			alert("数量不能为空");
		}
		return false;
	}

	var dj = "";
	//if(document.zyform.IsStaMoney){
	//var rlistdj = document.getElementsByName("IsStaMoney");
	//
	//for(var m=0;m<rlistdj.length;m++)
	//{
	//if(rlistdj[m].checked){dj =  rlistdj[m].value;}
	//}
	//
	//}

	if ((dj == "1" && (zytype == "1" || zytype == "2")) || zytype == "5" || zytype == "6") {
		if (document.zyform.SalesMinPrice.value == "" || document.zyform.SalesMinPrice.value == "0" || document.zyform.SalesMinPrice.value == "0.00") {

			//alert("价格/起始价不能为空或0");
			//return false;
		}
	}

	if (document.zyform.xyjgtypes) {

		var rlist2 = document.getElementsByName("xyjgtypes");
		var type2;
		for (var j = 0; j < rlist2.length; j++) {
			if (rlist2[j].checked) {
				type2 = rlist2[j].value;
			}
		}

		if (type2 > 0) {}
		else {
			alert("价格类型不能为空");
			return false;
		}
	}

	if (document.zyform.SalesStartDate.value == "") {
		alert("交易开始日期不能为空");
		return false;
	}
	if (document.zyform.SalesEndDate.value == "") {
		alert("交易结束日期不能为空");
		return false;
	}
	if (document.zyform.SalesEndDate.value < sysdate) {
		alert("交易结束日期已过期，请修改销售日期");
		return false;
	}
	if (document.zyform.SalesEndDate.value < document.zyform.SalesStartDate.value) {
		alert("交易结束日期不能早于开始日期");
		return false;
	}
	if (sjc < 2) {
		alert("交易时间必须大于2小时，请修改销售日期");
		return false;
	}
	if (sjc > 720) {
		alert("资源有效时间应在30天以内");
		return false;
	}

	//定向采购检查
	if (document.zyform.Isdxcg) {
		if (document.zyform.Isdxcg.value == 1 && document.zyform.dxcggc.value == 0) {
			alert("定向采购必须选择定向采购厂家");
			document.zyform.dxcggc.focus();
			return false;
		}
	}

	//if(document.zyform.AssureGuarpayMoney.value == ""){
	//alert("交易保证金不能为空");
	//return false;
	//}
	//if(document.zyform.AssureGuarpayMoney.value != "" && document.zyform.IsNoFinance.value != "1" ){
	//if(document.zyform.AssureGuarpayMoney.value < 1000 ){
	//alert("请填写正确的交易保证金金额");
	//return false;
	//}
	//
	//}
	/*if((document.zyform.LyMoney.value == "" || document.zyform.LyMoney.value < 1000)&& document.zyform.IsNoFinance.value != "1" ){
	alert("请填写正确的履约保证金（保证金最低不能小于1000）");
	return false;
	}*/
	//if( eval(document.zyform.LyMoney.value) < eval(document.zyform.AssureGuarpayMoney.value) ){
	//alert("履约保证金金额必须大于或等于交易保证金金额");
	//return false;
	//}


	submitRequest(document.zyform, "post", "text", outWritezy, null); //使用自定义myProcess
}
//Added by quanjw for meijiao end 2014/12/31


//Added by quanjw for meijiao start 2015/2/2
function jiaotanpostData() {

	var SCDate = document.getElementById("MfgDate2").value;

	SCDate = SCDate + " 00:00:00";

	var FBDate = document.getElementById("CreateDate").innerHTML;

	var SCDate = new Date(SCDate.replace("-", "/").replace("-", "/"));
	var FBDate = new Date(FBDate.replace("-", "/").replace("-", "/"));
	if (SCDate > FBDate) {
		alert("生产日期不能大于发布日期,请重新填写！");
		return fales;
	}

	//anyAjaxObj.setRequestHeader("If-Modified-Since","0");
	var xszytype = document.getElementsByName("SalesType");

	for (var m = 0; m < xszytype.length; m++) {
		if (xszytype[m].checked) {
			zytype = xszytype[m].value;
		}
	}
	//alert(typedif);
	//alert(zytype);
	var zytype = document.zyform.salety.value;
	var typedif = document.zyform.typedif.value;

	var myDate = new Date();
	var sysdate = myDate.format('yyyy-MM-dd hh:mm:ss');

	strDate1 = document.zyform.SalesEndDate.value;
	strDate2 = document.zyform.SalesStartDate.value;
	endDate = Date.parse(strDate1.replace(/\-/g, "/"));
	startDate = Date.parse(strDate2.replace(/\-/g, "/"));
	var sjc = (endDate - startDate) / (60 * 60 * 1000);

	var flag;
	if (typedif != "") {
		flag = "1";
	} //新增
	if (zytype != "") {
		flag = "2";
	} //修改
	if (document.zyform.VarietyName.value == "") {
		alert("品名不能为空");
		return false;
	}
	if (isNaN(document.zyform.QuantitySales.value)) {

		alert("数量不能为非数字");
		return false;
	}
		var chk;
		var chkObjs = document.zyform.TradeType; //取出报价方式的radio,
		//alert(chkObjs);

		for (var i = 0; i < chkObjs.length; i++) { //循环取出选择项
			if (chkObjs[i].checked) {
				chk = i;

				break;
			}
		}
if(document.zyform.SalesMinPrice.value=="")//判断价格填写框有没有值
{	
		if (chk == "0") //判断是否是锁定价格方式
		{
			var chk2;
			var chkObjs2 = document.zyform.Yijia;
			for (var i = 0; i < chkObjs2.length; i++) {
				if (chkObjs2[i].checked) {
					chk2 = i;
					//alert(chk2);
					break;

				}
			}
			if (chk2 == "1") {
				alert("您没有填写资源价格，不能选择锁定价格或不可议价。");
				return false;
			}
		}
	}

	if (zytype != "16" && typedif != "5" && typedif != "6") {

		if (zytype != "16" && typedif != "15" && typedif != "16") {

			if (document.zyform.SpecCode.value == "") {
				alert("灰分不能为空");
				return false;
			}
			if (document.zyform.MaterialCode.value == "") { //&& document.zyform.hd.value == "" && document.zyform.kd.value == ""&& document.zyform.cd.value == ""
				alert("全硫分不能为空");
				return false;
			}

			if (document.zyform.cd.value == "") {
				alert("CSR不能为空");
				return false;
			}

		}

	}
	/*
	if(document.zyform.StoreCityCode.value == "" && document.zyform.StoreCity.value == "" ){
	alert("所在城市不能为空");
	return false;
	}*/
if( chk=='1'  && (document.zyform.SalesType.value=='2' || document.zyform.SalesType.value=='8')){
}else{
	if(document.zyform.OriginCode.value == "" ){
	alert("厂家不能为空");
	return false;
	}
}
/*if(document.zyform.OriginCode.value == "" && chk!='1'  && document.zyform.SalesType.value!='5'){
alert("产地(厂家)不能为空");
return false;
}*/
	/*
	if(zytype =="1"){
	if(document.zyform.jhck.value == ""){
	alert("交货仓库不能为空");
	return false;
	}
	}*/
	if (document.zyform.QuantitySales.value == "") {

		if (zytype == "2" || zytype == "6" || typedif == "3" || typedif == "4") {
			alert("购买数量不能为空");
		} else {
			alert("数量不能为空");
		}
		return false;
	}

	var dj = "";
	//if(document.zyform.IsStaMoney){
	//var rlistdj = document.getElementsByName("IsStaMoney");
	//
	//for(var m=0;m<rlistdj.length;m++)
	//{
	//if(rlistdj[m].checked){dj =  rlistdj[m].value;}
	//}
	//
	//}

	if ((dj == "1" && (zytype == "1" || zytype == "2")) || zytype == "5" || zytype == "6") {
		if (document.zyform.SalesMinPrice.value == "" || document.zyform.SalesMinPrice.value == "0" || document.zyform.SalesMinPrice.value == "0.00") {

			//alert("价格/起始价不能为空或0");
			//return false;
		}
	}

	if (document.zyform.xyjgtypes) {

		var rlist2 = document.getElementsByName("xyjgtypes");
		var type2;
		for (var j = 0; j < rlist2.length; j++) {
			if (rlist2[j].checked) {
				type2 = rlist2[j].value;
			}
		}

		if (type2 > 0) {}
		else {
			alert("价格类型不能为空");
			return false;
		}
	}

	if (document.zyform.SalesStartDate.value == "") {
		alert("交易开始日期不能为空");
		return false;
	}
	if (document.zyform.SalesEndDate.value == "") {
		alert("交易结束日期不能为空");
		return false;
	}
	if (document.zyform.SalesEndDate.value < sysdate) {
		alert("交易结束日期已过期，请修改销售日期");
		return false;
	}
	if (document.zyform.SalesEndDate.value < document.zyform.SalesStartDate.value) {
		alert("交易结束日期不能早于开始日期");
		return false;
	}
	if (sjc < 2) {
		alert("交易时间必须大于2小时，请修改销售日期");
		return false;
	}
	if (sjc > 720) {
		alert("资源有效时间应在30天以内");
		return false;
	}

	//定向采购检查
	if (document.zyform.Isdxcg) {
		if (document.zyform.Isdxcg.value == 1 && document.zyform.dxcggc.value == 0) {
			alert("定向采购必须选择定向采购厂家");
			document.zyform.dxcggc.focus();
			return false;
		}
	}

	//if(document.zyform.AssureGuarpayMoney.value == ""){
	//alert("交易保证金不能为空");
	//return false;
	//}
	//if(document.zyform.AssureGuarpayMoney.value != "" && document.zyform.IsNoFinance.value != "1" ){
	//if(document.zyform.AssureGuarpayMoney.value < 1000 ){
	//alert("请填写正确的交易保证金金额");
	//return false;
	//}
	//
	//}
	/*if((document.zyform.LyMoney.value == "" || document.zyform.LyMoney.value < 1000)&& document.zyform.IsNoFinance.value != "1" ){
	alert("请填写正确的履约保证金（保证金最低不能小于1000）");
	return false;
	}*/
	//if( eval(document.zyform.LyMoney.value) < eval(document.zyform.AssureGuarpayMoney.value) ){
	//alert("履约保证金金额必须大于或等于交易保证金金额");
	//return false;
	//}


	submitRequest(document.zyform, "post", "text", outWritejiaotanzy, null); //使用自定义myProcess
}
//Added by quanjw for meijiao end 2015/2/2

//Added by quanjw for shuini start 2015/2/6
function shuinipostData() {

	var SCDate = document.getElementById("MfgDate2").value;

	SCDate = SCDate + " 00:00:00";

	var FBDate = document.getElementById("CreateDate").innerHTML;

	var SCDate = new Date(SCDate.replace("-", "/").replace("-", "/"));
	var FBDate = new Date(FBDate.replace("-", "/").replace("-", "/"));
	if (SCDate > FBDate) {
		alert("生产日期不能大于发布日期,请重新填写！");
		return fales;
	}

	//anyAjaxObj.setRequestHeader("If-Modified-Since","0");
	var xszytype = document.getElementsByName("SalesType");

	for (var m = 0; m < xszytype.length; m++) {
		if (xszytype[m].checked) {
			zytype = xszytype[m].value;
		}
	}
	//alert(typedif);
	//alert(zytype);
	var zytype = document.zyform.salety.value;
	var typedif = document.zyform.typedif.value;

	var myDate = new Date();
	var sysdate = myDate.format('yyyy-MM-dd hh:mm:ss');

	strDate1 = document.zyform.SalesEndDate.value;
	strDate2 = document.zyform.SalesStartDate.value;
	endDate = Date.parse(strDate1.replace(/\-/g, "/"));
	startDate = Date.parse(strDate2.replace(/\-/g, "/"));
	var sjc = (endDate - startDate) / (60 * 60 * 1000);

	var flag;
	if (typedif != "") {
		flag = "1";
	} //新增
	if (zytype != "") {
		flag = "2";
	} //修改
	if (document.zyform.VarietyName.value == "") {
		alert("品名不能为空");
		return false;
	}
	if (isNaN(document.zyform.QuantitySales.value)) {

		alert("数量不能为非数字");
		return false;
	}
		var chk;
		var chkObjs = document.zyform.TradeType; //取出报价方式的radio,
		//alert(chkObjs);

		for (var i = 0; i < chkObjs.length; i++) { //循环取出选择项
			if (chkObjs[i].checked) {
				chk = i;

				break;
			}
		}
if(document.zyform.SalesMinPrice.value=="")//判断价格填写框有没有值
{	
		if (chk == "0") //判断是否是锁定价格方式
		{
			var chk2;
			var chkObjs2 = document.zyform.Yijia;
			for (var i = 0; i < chkObjs2.length; i++) {
				if (chkObjs2[i].checked) {
					chk2 = i;
					//alert(chk2);
					break;

				}
			}
			if (chk2 == "1") {
				alert("您没有填写资源价格，不能选择锁定价格或不可议价。");
				return false;
			}
		}
	}

	if (zytype != "16" && typedif != "5" && typedif != "6") {

		if (zytype != "16" && typedif != "15" && typedif != "16") {

			if (document.zyform.SpecCode.value == "") { //&& document.zyform.hd.value == "" && document.zyform.kd.value == ""&& document.zyform.cd.value == ""
				alert("规格不能为空");
				return false;
			}

		}

	}
	/*
	if(document.zyform.StoreCityCode.value == "" && document.zyform.StoreCity.value == "" ){
	alert("所在城市不能为空");
	return false;
	}*/

if( chk=='1'  && (document.zyform.SalesType.value=='2' || document.zyform.SalesType.value=='8')){
}else{
	if(document.zyform.OriginCode.value == "" ){
	alert("厂家不能为空");
	return false;
	}
}
/*if(document.zyform.OriginCode.value == "" && chk!='1'  && document.zyform.SalesType.value!='5'){
alert("厂家不能为空");
return false;
}*/
	/*
	if(zytype =="1"){
	if(document.zyform.jhck.value == ""){
	alert("交货仓库不能为空");
	return false;
	}
	}*/
	if (document.zyform.QuantitySales.value == "") {

		if (zytype == "2" || zytype == "6" || typedif == "3" || typedif == "4") {
			alert("购买数量不能为空");
		} else {
			alert("数量不能为空");
		}
		return false;
	}

	var dj = "";
	//if(document.zyform.IsStaMoney){
	//var rlistdj = document.getElementsByName("IsStaMoney");
	//
	//for(var m=0;m<rlistdj.length;m++)
	//{
	//if(rlistdj[m].checked){dj =  rlistdj[m].value;}
	//}
	//
	//}

	if ((dj == "1" && (zytype == "1" || zytype == "2")) || zytype == "5" || zytype == "6") {
		if (document.zyform.SalesMinPrice.value == "" || document.zyform.SalesMinPrice.value == "0" || document.zyform.SalesMinPrice.value == "0.00") {

			//alert("价格/起始价不能为空或0");
			//return false;
		}
	}

	if (document.zyform.xyjgtypes) {

		var rlist2 = document.getElementsByName("xyjgtypes");
		var type2;
		for (var j = 0; j < rlist2.length; j++) {
			if (rlist2[j].checked) {
				type2 = rlist2[j].value;
			}
		}

		if (type2 > 0) {}
		else {
			alert("价格类型不能为空");
			return false;
		}
	}

	if (document.zyform.SalesStartDate.value == "") {
		alert("交易开始日期不能为空");
		return false;
	}
	if (document.zyform.SalesEndDate.value == "") {
		alert("交易结束日期不能为空");
		return false;
	}
	if (document.zyform.SalesEndDate.value < sysdate) {
		alert("交易结束日期已过期，请修改销售日期");
		return false;
	}
	if (document.zyform.SalesEndDate.value < document.zyform.SalesStartDate.value) {
		alert("交易结束日期不能早于开始日期");
		return false;
	}
	if (sjc < 2) {
		alert("交易时间必须大于2小时，请修改销售日期");
		return false;
	}
	if (sjc > 720) {
		alert("资源有效时间应在30天以内");
		return false;
	}

	//定向采购检查
	if (document.zyform.Isdxcg) {
		if (document.zyform.Isdxcg.value == 1 && document.zyform.dxcggc.value == 0) {
			alert("定向采购必须选择定向采购厂家");
			document.zyform.dxcggc.focus();
			return false;
		}
	}

	//if(document.zyform.AssureGuarpayMoney.value == ""){
	//alert("交易保证金不能为空");
	//return false;
	//}
	//if(document.zyform.AssureGuarpayMoney.value != "" && document.zyform.IsNoFinance.value != "1" ){
	//if(document.zyform.AssureGuarpayMoney.value < 1000 ){
	//alert("请填写正确的交易保证金金额");
	//return false;
	//}
	//
	//}
	/*if((document.zyform.LyMoney.value == "" || document.zyform.LyMoney.value < 1000)&& document.zyform.IsNoFinance.value != "1" ){
	alert("请填写正确的履约保证金（保证金最低不能小于1000）");
	return false;
	}*/
	//if( eval(document.zyform.LyMoney.value) < eval(document.zyform.AssureGuarpayMoney.value) ){
	//alert("履约保证金金额必须大于或等于交易保证金金额");
	//return false;
	//}


	submitRequest(document.zyform, "post", "text", outWriteshuinizy, null); //使用自定义myProcess
}
//Added by quanjw for shuini end 2015/2/6
function zypostData(){	

		var SCDate=document.getElementById("MfgDate2").value;

		SCDate=SCDate+" 00:00:00";
				
		var FBDate=document.getElementById("CreateDate").innerHTML;

		var SCDate = new Date(SCDate.replace("-", "/").replace("-", "/"));
		var FBDate = new Date(FBDate.replace("-", "/").replace("-", "/"));
		if(SCDate>FBDate){
		alert("生产日期不能大于发布日期,请重新填写！");
		return false;
		}
		


//anyAjaxObj.setRequestHeader("If-Modified-Since","0"); 
var xszytype = document.getElementsByName("SalesType");

	for (var m = 0; m < xszytype.length; m++) {
		if (xszytype[m].checked) {
			zytype = xszytype[m].value;
		}
	}
	var zytype = document.zyform.salety.value;
	var typedif = document.zyform.typedif.value;

	var myDate = new Date();
	var sysdate = myDate.format('yyyy-MM-dd hh:mm:ss');

	strDate1 = document.zyform.SalesEndDate.value;
	strDate2 = document.zyform.SalesStartDate.value;
	endDate = Date.parse(strDate1.replace(/\-/g, "/"));
	startDate = Date.parse(strDate2.replace(/\-/g, "/"));
	var sjc = (endDate - startDate) / (60 * 60 * 1000);

	var flag;
	if (typedif != "") {
		flag = "1";
	} //新增
	if (zytype != "") {
		flag = "2";
	} //修改
	if (document.zyform.VarietyName.value == "") {
		alert("品名不能为空");
		return false;
	}

	if (isNaN(document.zyform.QuantitySales.value)) {

		alert("数量不能为非数字");
		return false;
	}
		var chk;
		var chkObjs = document.zyform.TradeType; //取出报价方式的radio,
		//alert(chkObjs);

		for (var i = 0; i < chkObjs.length; i++) { //循环取出选择项
			if (chkObjs[i].checked) {
				chk = i;

				break;
			}
		}
if(document.zyform.SalesMinPrice.value=="")//判断价格填写框有没有值
{	
		if (chk == "0") //判断是否是锁定价格方式
		{
			var chk2;
			var chkObjs2 = document.zyform.Yijia;
			for (var i = 0; i < chkObjs2.length; i++) {
				if (chkObjs2[i].checked) {
					chk2 = i;
					//alert(chk2);
					break;

				}
			}
			if (chk2 == "1") {
				alert("您没有填写资源价格，不能选择锁定价格或不可议价。");
				return false;
			}
		}
	}

	if (zytype != "16" && typedif != "5" && typedif != "6") {

		if (zytype != "16" && typedif != "15" && typedif != "16") {

			if (document.zyform.tks_fe.value == "") {
				alert("Fe不能为空");
				return false;
			}
            
            if ( ( document.zyform.tks_fe.value.indexOf("%") > -1) || 
                 ( document.zyform.tks_si.value.indexOf("%") > -1) || 
                 ( document.zyform.tks_al.value.indexOf("%") > -1) || 
                 ( document.zyform.attr_b.value.indexOf("%") > -1) || 
                 ( document.zyform.attr_c.value.indexOf("%") > -1) || 
                 ( document.zyform.attr_d.value.indexOf("%") > -1) 
                 ) {
                alert("Fe,SiO2,AL2O3,P,S,H2O,不能含%");
                return false;
            }
			
			if( document.zyform.tks_si.value == "" || document.zyform.tks_al.value == "" || document.zyform.attr_b.value == "" || document.zyform.attr_c.value == "" ){
				
				if( ! window.confirm('SiO2、AL2O3、P、S有空值，您确定要继续吗？')){
					return false;
				}
			}

		}

	}
if( chk=='1'  && (document.zyform.SalesType.value=='2' || document.zyform.SalesType.value=='8')){
}else{
	if(document.zyform.OriginCode.value == "" ){
	alert("厂家不能为空");
	return false;
	}
}
	/*if (document.zyform.OriginCode.value == "" && chk!='1'  && document.zyform.SalesType.value!='5') {
		alert("产地不能为空");
		return false;
	}*/
	
	if (document.zyform.QuantitySales.value == "") {

		if (zytype == "2" || zytype == "6" || typedif == "3" || typedif == "4") {
			alert("购买数量不能为空");
		} else {
			alert("数量不能为空");
		}
		return false;
	}

	var dj = "";
	
	if (document.zyform.xyjgtypes) {

		var rlist2 = document.getElementsByName("xyjgtypes");
		var type2;
		for (var j = 0; j < rlist2.length; j++) {
			if (rlist2[j].checked) {
				type2 = rlist2[j].value;
			}
		}

		if (type2 > 0) {}
		else {
			alert("价格类型不能为空");
			return false;
		}
	}

	if (document.zyform.SalesStartDate.value == "") {
		alert("交易开始日期不能为空");
		return false;
	}
	if (document.zyform.SalesEndDate.value == "") {
		alert("交易结束日期不能为空");
		return false;
	}
	if (document.zyform.SalesEndDate.value < sysdate) {
		alert("交易结束日期已过期，请修改销售日期");
		return false;
	}
	if (document.zyform.SalesEndDate.value < document.zyform.SalesStartDate.value) {
		alert("交易结束日期不能早于开始日期");
		return false;
	}
	if (sjc < 2) {
		alert("交易时间必须大于2小时，请修改销售日期");
		return false;
	}
	if (sjc > 720) {
		alert("资源有效时间应在30天以内");
		return false;
	}

	//定向采购检查
	if (document.zyform.Isdxcg) {
		if (document.zyform.Isdxcg.value == 1 && document.zyform.dxcggc.value == 0) {
			alert("定向采购必须选择定向采购厂家");
			document.zyform.dxcggc.focus();
			return false;
		}
	}


	submitRequest(document.zyform, "post", "text", outWritezy, null); //使用自定义myProcess
}



function zypostData() {

	var SCDate = document.getElementById("MfgDate2").value;

	SCDate = SCDate + " 00:00:00";

	var FBDate = document.getElementById("CreateDate").innerHTML;

	var SCDate = new Date(SCDate.replace("-", "/").replace("-", "/"));
	var FBDate = new Date(FBDate.replace("-", "/").replace("-", "/"));
	if (SCDate > FBDate) {
		alert("生产日期不能大于发布日期,请重新填写！");
		return fales;
	}

	//anyAjaxObj.setRequestHeader("If-Modified-Since","0");
	var xszytype = document.getElementsByName("SalesType");

	for (var m = 0; m < xszytype.length; m++) {
		if (xszytype[m].checked) {
			zytype = xszytype[m].value;
		}
	}
	//alert(typedif);
	//alert(zytype);
	var zytype = document.zyform.salety.value;
	var typedif = document.zyform.typedif.value;

	var myDate = new Date();
	var sysdate = myDate.format('yyyy-MM-dd hh:mm:ss');

	strDate1 = document.zyform.SalesEndDate.value;
	strDate2 = document.zyform.SalesStartDate.value;
	endDate = Date.parse(strDate1.replace(/\-/g, "/"));
	startDate = Date.parse(strDate2.replace(/\-/g, "/"));
	var sjc = (endDate - startDate) / (60 * 60 * 1000);

	var flag;
	if (typedif != "") {
		flag = "1";
	} //新增
	if (zytype != "") {
		flag = "2";
	} //修改
	if (document.zyform.VarietyName.value == "") {
		alert("品名不能为空");
		return false;
	}
	if (isNaN(document.zyform.QuantitySales.value)) {

		alert("数量不能为非数字");
		return false;
	}
	
		var chk;
		var chkObjs = document.zyform.TradeType; //取出报价方式的radio,
		//alert(chkObjs);

		for (var i = 0; i < chkObjs.length; i++) { //循环取出选择项
			if (chkObjs[i].checked) {
				chk = i;

				break;
			}
		}
	if (document.zyform.SalesMinPrice.value == "") //判断价格填写框有没有值
	{
		if (chk == "0") //判断是否是锁定价格方式
		{
			var chk2;
			var chkObjs2 = document.zyform.Yijia;
			for (var i = 0; i < chkObjs2.length; i++) {
				if (chkObjs2[i].checked) {
					chk2 = i;
					//alert(chk2);
					break;

				}
			}
			if (chk2 == "1") {
				alert("您没有填写资源价格，不能选择锁定价格或不可议价。");
				return false;
			}
		}
	}

	if (zytype != "16" && typedif != "5" && typedif != "6") {

		if (zytype != "16" && typedif != "15" && typedif != "16") {

			if (document.zyform.MaterialCode.value == "") {
				alert("材质不能为空");
				return false;
			}
			if (document.zyform.SpecCode.value == "") { //&& document.zyform.hd.value == "" && document.zyform.kd.value == ""&& document.zyform.cd.value == ""
				alert("规格不能为空");
				return false;
			}

		}

	}
	/*
	if(document.zyform.StoreCityCode.value == "" && document.zyform.StoreCity.value == "" ){
	alert("所在城市不能为空");
	return false;
	}*/
if( chk=='1'  && (document.zyform.SalesType.value=='2' || document.zyform.SalesType.value=='8')){
}else{
	if(document.zyform.OriginCode.value == "" ){
	alert("厂家不能为空");
	return false;
	}
}
	/*if (document.zyform.OriginCode.value == "" && chk!='1'  && document.zyform.SalesType.value!='5') {
		alert("钢厂不能为空");
		return false;
	}*/
	/*
	if(zytype =="1"){
	if(document.zyform.jhck.value == ""){
	alert("交货仓库不能为空");
	return false;
	}
	}*/
	if (document.zyform.QuantitySales.value == "") {

		if (zytype == "2" || zytype == "6" || typedif == "3" || typedif == "4") {
			alert("购买数量不能为空");
		} else {
			alert("数量不能为空");
		}
		return false;
	}

	var dj = "";
	//if(document.zyform.IsStaMoney){
	//var rlistdj = document.getElementsByName("IsStaMoney");
	//
	//for(var m=0;m<rlistdj.length;m++)
	//{
	//if(rlistdj[m].checked){dj =  rlistdj[m].value;}
	//}
	//
	//}

	if ((dj == "1" && (zytype == "1" || zytype == "2")) || zytype == "5" || zytype == "6") {
		if (document.zyform.SalesMinPrice.value == "" || document.zyform.SalesMinPrice.value == "0" || document.zyform.SalesMinPrice.value == "0.00") {

			//alert("价格/起始价不能为空或0");
			//return false;
		}
	}

	if (document.zyform.xyjgtypes) {

		var rlist2 = document.getElementsByName("xyjgtypes");
		var type2;
		for (var j = 0; j < rlist2.length; j++) {
			if (rlist2[j].checked) {
				type2 = rlist2[j].value;
			}
		}

		if (type2 > 0) {}
		else {
			alert("价格类型不能为空");
			return false;
		}
	}

	if (document.zyform.SalesStartDate.value == "") {
		alert("交易开始日期不能为空");
		return false;
	}
	if (document.zyform.SalesEndDate.value == "") {
		alert("交易结束日期不能为空");
		return false;
	}
	if (document.zyform.SalesEndDate.value < sysdate) {
		alert("交易结束日期已过期，请修改销售日期");
		return false;
	}
	if (document.zyform.SalesEndDate.value < document.zyform.SalesStartDate.value) {
		alert("交易结束日期不能早于开始日期");
		return false;
	}
	if (sjc < 2) {
		alert("交易时间必须大于2小时，请修改销售日期");
		return false;
	}
	if (sjc > 720) {
		alert("资源有效时间应在30天以内");
		return false;
	}

	//定向采购检查
	if (document.zyform.Isdxcg) {
		if (document.zyform.Isdxcg.value == 1 && document.zyform.dxcggc.value == 0) {
			alert("定向采购必须选择定向采购厂家");
			document.zyform.dxcggc.focus();
			return false;
		}
	}

	//if(document.zyform.AssureGuarpayMoney.value == ""){
	//alert("交易保证金不能为空");
	//return false;
	//}
	//if(document.zyform.AssureGuarpayMoney.value != "" && document.zyform.IsNoFinance.value != "1" ){
	//if(document.zyform.AssureGuarpayMoney.value < 1000 ){
	//alert("请填写正确的交易保证金金额");
	//return false;
	//}
	//
	//}
	/*if((document.zyform.LyMoney.value == "" || document.zyform.LyMoney.value < 1000)&& document.zyform.IsNoFinance.value != "1" ){
	alert("请填写正确的履约保证金（保证金最低不能小于1000）");
	return false;
	}*/
	//if( eval(document.zyform.LyMoney.value) < eval(document.zyform.AssureGuarpayMoney.value) ){
	//alert("履约保证金金额必须大于或等于交易保证金金额");
	//return false;
	//}


	submitRequest(document.zyform, "post", "text", outWritezy, null); //使用自定义myProcess
}
//Added by quanjw for jinshuzhipin start 2015/2/15
// 金属资源 逐条发布 保存按钮 的onclick事件
// 检查各字段合法性并提交表单
function jinshupostData(type) {

	var SCDate = document.getElementById("MfgDate2").value;

	SCDate = SCDate + " 00:00:00";

	var FBDate = document.getElementById("CreateDate").innerHTML;

	var SCDate = new Date(SCDate.replace("-", "/").replace("-", "/"));
	var FBDate = new Date(FBDate.replace("-", "/").replace("-", "/"));
	if (SCDate > FBDate) {
		alert("生产日期不能大于发布日期,请重新填写！");
		return fales;
	}

	//anyAjaxObj.setRequestHeader("If-Modified-Since","0");
	var xszytype = document.getElementsByName("SalesType");

	for (var m = 0; m < xszytype.length; m++) {
		if (xszytype[m].checked) {
			zytype = xszytype[m].value;
		}
	}
	//alert(typedif);
	//alert(zytype);
	var zytype = document.zyform.salety.value;
	var typedif = document.zyform.typedif.value;

	var myDate = new Date();
	var sysdate = myDate.format('yyyy-MM-dd hh:mm:ss');

	strDate1 = document.zyform.SalesEndDate.value;
	strDate2 = document.zyform.SalesStartDate.value;
	endDate = Date.parse(strDate1.replace(/\-/g, "/"));
	startDate = Date.parse(strDate2.replace(/\-/g, "/"));
	var sjc = (endDate - startDate) / (60 * 60 * 1000);

	var flag;
	if (typedif != "") {
		flag = "1";
	} //新增
	if (zytype != "") {
		flag = "2";
	} //修改
	if (document.zyform.VarietyName.value == "") {
		alert("品名不能为空");
		return false;
	}
	if (isNaN(document.zyform.QuantitySales.value)) {

		alert("数量不能为非数字");
		return false;
	}
		var chk;
		var chkObjs = document.zyform.TradeType; //取出报价方式的radio,
		//alert(chkObjs);

		for (var i = 0; i < chkObjs.length; i++) { //循环取出选择项
			if (chkObjs[i].checked) {
				chk = i;

				break;
			}
		}
if(document.zyform.SalesMinPrice.value=="")//判断价格填写框有没有值
{	
		if (chk == "0") //判断是否是锁定价格方式
		{
			var chk2;
			var chkObjs2 = document.zyform.Yijia;
			for (var i = 0; i < chkObjs2.length; i++) {
				if (chkObjs2[i].checked) {
					chk2 = i;
					//alert(chk2);
					break;

				}
			}
			if (chk2 == "1") {
				alert("您没有填写资源价格，不能选择锁定价格或不可议价。");
				return false;
			}
		}
	}

	if (zytype != "16" && typedif != "5" && typedif != "6") {

		if (zytype != "16" && typedif != "15" && typedif != "16") {

			if (document.zyform.MaterialCode.value == "") {
				alert("材质不能为空");
				return false;
			}
			if (document.zyform.SpecCode.value == "") { //&& document.zyform.hd.value == "" && document.zyform.kd.value == ""&& document.zyform.cd.value == ""
				alert("规格不能为空");
				return false;
			}

		}

	}
	/*
	if(document.zyform.StoreCityCode.value == "" && document.zyform.StoreCity.value == "" ){
	alert("所在城市不能为空");
	return false;
	}*/
    //update by zfy started 2019/09/26 增加焊网
    if ("undefined" == typeof type) {
        type = 'jinshu';
    }
    if (type=='hw'){
        if (document.zyform.yongtu.value == "") {
            alert("抗拉不能为空");
            return false;
        }

        if (document.zyform.strength.value == "") {
            alert("屈服不能为空");
            return false;
        }
    }else {
        if (document.zyform.yongtu.value == "") {
            alert("用途不能为空");
            return false;
        }

        if (document.zyform.strength.value == "") {
            alert("强度不能为空");
            return false;
        }
    }
    //update by zfy ended 2019/09/26 增加焊网


if( chk=='1'  && (document.zyform.SalesType.value=='2' || document.zyform.SalesType.value=='8')){
}else{
	if(document.zyform.OriginCode.value == "" ){
	alert("厂家不能为空");
	return false;
	}
}
/*if(document.zyform.OriginCode.value == "" && chk!='1'  && document.zyform.SalesType.value!='5'){
alert("产地不能为空");
return false;
}*/
	/*
	if(zytype =="1"){
	if(document.zyform.jhck.value == ""){
	alert("交货仓库不能为空");
	return false;
	}
	}*/
	if (document.zyform.QuantitySales.value == "") {

		if (zytype == "2" || zytype == "6" || typedif == "3" || typedif == "4") {
			alert("购买数量不能为空");
		} else {
			alert("数量不能为空");
		}
		return false;
	}

	var dj = "";
	//if(document.zyform.IsStaMoney){
	//var rlistdj = document.getElementsByName("IsStaMoney");
	//
	//for(var m=0;m<rlistdj.length;m++)
	//{
	//if(rlistdj[m].checked){dj =  rlistdj[m].value;}
	//}
	//
	//}

	if ((dj == "1" && (zytype == "1" || zytype == "2")) || zytype == "5" || zytype == "6") {
		if (document.zyform.SalesMinPrice.value == "" || document.zyform.SalesMinPrice.value == "0" || document.zyform.SalesMinPrice.value == "0.00") {

			//alert("价格/起始价不能为空或0");
			//return false;
		}
	}

	if (document.zyform.xyjgtypes) {

		var rlist2 = document.getElementsByName("xyjgtypes");
		var type2;
		for (var j = 0; j < rlist2.length; j++) {
			if (rlist2[j].checked) {
				type2 = rlist2[j].value;
			}
		}

		if (type2 > 0) {}
		else {
			alert("价格类型不能为空");
			return false;
		}
	}

	if (document.zyform.SalesStartDate.value == "") {
		alert("交易开始日期不能为空");
		return false;
	}
	if (document.zyform.SalesEndDate.value == "") {
		alert("交易结束日期不能为空");
		return false;
	}
	if (document.zyform.SalesEndDate.value < sysdate) {
		alert("交易结束日期已过期，请修改销售日期");
		return false;
	}
	if (document.zyform.SalesEndDate.value < document.zyform.SalesStartDate.value) {
		alert("交易结束日期不能早于开始日期");
		return false;
	}
	if (sjc < 2) {
		alert("交易时间必须大于2小时，请修改销售日期");
		return false;
	}
	if (sjc > 720) {
		alert("资源有效时间应在30天以内");
		return false;
	}

	//定向采购检查
	if (document.zyform.Isdxcg) {
		if (document.zyform.Isdxcg.value == 1 && document.zyform.dxcggc.value == 0) {
			alert("定向采购必须选择定向采购厂家");
			document.zyform.dxcggc.focus();
			return false;
		}
	}

	//if(document.zyform.AssureGuarpayMoney.value == ""){
	//alert("交易保证金不能为空");
	//return false;
	//}
	//if(document.zyform.AssureGuarpayMoney.value != "" && document.zyform.IsNoFinance.value != "1" ){
	//if(document.zyform.AssureGuarpayMoney.value < 1000 ){
	//alert("请填写正确的交易保证金金额");
	//return false;
	//}
	//
	//}
	/*if((document.zyform.LyMoney.value == "" || document.zyform.LyMoney.value < 1000)&& document.zyform.IsNoFinance.value != "1" ){
	alert("请填写正确的履约保证金（保证金最低不能小于1000）");
	return false;
	}*/
	//if( eval(document.zyform.LyMoney.value) < eval(document.zyform.AssureGuarpayMoney.value) ){
	//alert("履约保证金金额必须大于或等于交易保证金金额");
	//return false;
	//}


	submitRequest(document.zyform, "post", "text", outWritezy, null); //使用自定义myProcess
}
//Added by quanjw for jinshuzhipin end 2015/2/15
//Added by quanjw for meijiao  start 2015/1/4
//煤炭资源
function outWritemeitanzy(count) {
	var tmp = count.split("|-|");
	location.href = "member.php?view=jymanage&id=" + tmp[0] + "&jg=1" + "&optionid=2593";
}
//Added by quanjw for meijiao end 2015/1/4

//Added by quanjw for meijiao start 2015/2/2
//焦炭资源
function outWritejiaotanzy(count) {
	var tmp = count.split("|-|");
	location.href = "member.php?view=jymanage&id=" + tmp[0] + "&jg=1" + "&optionid=2592";
}
//Added by quanjw for meijiao end 2015/2/2

//Added by quanjw for shuini start 2015/2/6
function outWriteshuinizy(count) {
	var tmp = count.split("|-|");
	location.href = "member.php?view=jymanage&id=" + tmp[0] + "&jg=1" + "&optionid=3414";
}
//ADded by quanjw for shuini end 2015/2/6
function outWritezy(count) {
	//alert(count);
	var tmp = count.split("|-|");
	//alert(tmp[19]);
	//var SaType=document.getElementById("SaType").value;
	location.href = "member.php?view=jymanage&id=" + tmp[0] + "&jg=1";
	//document.getElementById("SaType").innerHTML=temp[1];


	//if(document.getElementById("StoreCityCode")) document.getElementById("StoreCityCode").innerHTML=tmp[0];

	//if(document.getElementById("zyName")) document.getElementById("zyName").innerHTML=tmp[2];

	//if(document.getElementById("pz")) document.getElementById("pz").innerHTML=tmp[3];

	//if(document.getElementById("cz")) document.getElementById("cz").innerHTML=tmp[5];

	//if(document.getElementById("gg")) document.getElementById("gg").innerHTML=tmp[6];

	//if(document.getElementById("gc")) document.getElementById("gc").innerHTML=tmp[7];

	//if(document.getElementById("SalesUnit"))document.getElementById("SalesUnit").innerHTML=tmp[8];

	//if(document.getElementById("SalesMinPrice"))document.getElementById("SalesMinPrice").innerHTML=tmp[9];

	//if(document.getElementById("QuantitySales")) document.getElementById("QuantitySales").innerHTML=tmp[10];

	//if(document.getElementById("WeightPerOne")){document.getElementById("WeightPerOne").innerHTML=tmp[11];}

	//if(document.getElementById("MfgDate")) document.getElementById("MfgDate").innerHTML=tmp[12];

	//if(document.getElementById("QuantityMin")){document.getElementById("QuantityMin").innerHTML=tmp[13];}

	//if(document.getElementById("SalesStartDate")){document.getElementById("SalesStartDate").innerHTML=tmp[15];}

	//if(document.getElementById("SalesEndDate")){document.getElementById("SalesEndDate").innerHTML=tmp[16];}

	//if(document.getElementById("LadderPrice")) document.getElementById("LadderPrice").innerHTML=tmp[17];
	//if(document.getElementById("Delivery")) document.getElementById("Delivery").innerHTML=tmp[18];

	//document.getElementById("zyflag").value=tmp[19];
	//document.getElementById("flag").value=tmp[19];
	//document.getElementById("zyid").value=tmp[19];

	//document.getElementById("ziyuanname").value = tmp[2];


	//document.getElementById("showzy").style.display="block";
	//document.getElementById("zydiv").style.display="none";

	////显示交割信息模块
	//document.getElementById("jgdiv").style.display="block";
	//document.getElementById("show").style.display="none";

}

function fhpostData() {
	var f = document.fhform;
	if (f.ShipperMan.value == '') {
		alert("请填写发货联系人姓名");
		return false;
	}
	if (f.ShipperPhone.value == '') {
		alert("请填写发货联系人电话");
		return false;
	}
	if (f.ShipperAddress.value == '') {
		alert("请填写发货联系人地址");
		return false;
	}

	submitRequest(document.fhform, "post", "text", outWritefh, null); //使用自定义myProcess
}

function outWritefh(count) {
	var tmp = count.split("|-|");

	document.getElementById("ShipperMan").innerHTML = tmp[0];
	document.getElementById("ShipperPhone").innerHTML = tmp[1];
	document.getElementById("ShipperMobile").innerHTML = tmp[2];
	document.getElementById("ShipperFax").innerHTML = tmp[3];
	document.getElementById("ShipperEmail").innerHTML = tmp[4];
	document.getElementById("ShipperAddress").innerHTML = tmp[5];
	document.getElementById("ShipperPostCode").innerHTML = tmp[6];

	document.getElementById("htid").value = tmp[7];

	document.getElementById("showfh").style.display = "block";
	document.getElementById("fhdiv").style.display = "none";

}
function editfh() {
	document.getElementById("showfh").style.display = "none"; //隐藏
	document.getElementById("fhdiv").style.display = "block"; //隐藏
}

function closefh() {
	document.getElementById("showfh").style.display = "block";
	document.getElementById("fhdiv").style.display = "none";
}

function editjgxx() {
	document.getElementById("showjgxx").style.display = "none"; //隐藏
	document.getElementById("jgxxdiv").style.display = "block"; //隐藏
}

function closejgxx() {
	document.getElementById("showjgxx").style.display = "block";
	document.getElementById("jgxxdiv").style.display = "none";
}

function qrht(s) {

	if (s == "1") {
		var f = document.shform;

		if (f.ConsigneeMan.value == '') {
			alert("请填写收货联系人姓名");
			return false;
		}
		if (f.ConsigneePhone.value == '') {
			alert("请填写收货联系人电话");
			return false;
		}
		if (f.ConsigneeAddress.value == '') {
			alert("请填写收货联系人地址");
			return false;
		}

		if (document.getElementById("ConsigneeMan").innerHTML != f.ConsigneeMan.value) {
			alert("请先保存收货联系人信息");
			return false;
		}

	}

	if (s == "2") {
		var f = document.fhform;
		if (f.ShipperMan.value == '') {
			alert("请填写发货联系人姓名");
			return false;
		}
		if (f.ShipperPhone.value == '') {
			alert("请填写发货联系人电话");
			return false;
		}
		if (f.ShipperAddress.value == '') {
			alert("请填写发货联系人地址");
			return false;
		}
		if (document.getElementById("ShipperMan").innerHTML != f.ShipperMan.value) {
			alert("请先保存发货联系人信息");
			return false;
		}
	}

	var id = document.getElementById("htid").value;
	var param = "action=qrhtsales&id=" + id;
	var ajax = new Ajax("member.php", setHt, param);
}

//增加联系地址后的确认合同
function qrht2(obj, s) {
	//alert(checkradio("Delivery"));
	//alert(s);
	var temp = checkprice();
	if (!temp) {
		if (!confirm("供需双方价格不同,是否继续提交？")) {
			return false;
		}
	}

	/*
	if(s == "1"){
	var f = document.sh22;
	var dh=document.getElementById("shfs").innerHTML;
	if( document.getElementById("ConsigneeMan2")==''){
	alert( "收货联系人不能为空" );
	return false;
	}
	if( f.ConsigneeDhck.value == '' ){
	alert( "请填写"+dh +"信息" );
	return false;
	}
	if( checkradio("ConsigneeYsfs")==''){
	alert( "请选择运输方式" );
	return false;
	}

	if(document.getElementById("ConsigneeDhck2").innerHTML != f.ConsigneeDhck.value  ){
	alert( "请先保存收货联系人信息" );
	return false;
	}

	//	if((document.getElementById("ConsigneeMan2").innerHTML != f.ConsigneeMan.value)
	//	||(document.getElementById("ConsigneePhone2").innerHTML != f.ConsigneePhone.value)
	//	||(document.getElementById("ConsigneeMobile2").innerHTML != f.ConsigneeMobile.value)  ){
	//	alert( "请先保存收货联系人信息" );
	//    return false;
	//	}



	}



	if(s == "2"){
	var f = document.fh2;
	if( document.getElementById("ShipperMan2")==''){
	alert( "发货联系人不能为空" );
	return false;
	}
	if( f.ShipperFhck.value == '' ){
	alert( "请填写发货仓库" );
	return false;
	}
	if( checkradio("ShipperYsfs")==''){
	alert( "请选择运输方式" );
	return false;
	}

	if(document.getElementById("ShipperFhck2").innerHTML != f.ShipperFhck.value ){
	alert( "请先保存发货联系人信息" );
	return false;
	}

	//	if((document.getElementById("ShipperMan2").innerHTML != f.ShipperMan.value)
	//		||(document.getElementById("ShipperPhone2").innerHTML != f.ShipperPhone.value)
	//		||(document.getElementById("ShipperMobile2").innerHTML != f.ShipperMobile.value)  ){
	//		alert( "请先保存发货联系人信息" );
	//	    return false;
	//	}
	//	if((document.getElementById("ShipperAddress2").innerHTML != f2.ShipperAddress.value)
	//		|| (document.getElementById("ShipperAddressState2").innerHTML != f2.ShipperAddressState.value)
	//		|| (document.getElementById("ShipperAddressCity2").innerHTML != f2.ShipperAddressCity.value)
	//		|| (document.getElementById("ShipperPostCode2").innerHTML != f2.ShipperPostCode.value)
	//
	//	){
	//		alert( "请先保存发货地址信息" );
	//	    return false;
	//	}
	}
	 */

	if (checkradio("PickUpType") == "") {
		alert("请选择交割类型");
		return false;
	}

	if (checkradio("Delivery") == "") {
		alert("请选择提货方式");
		return false;
	}

	if (checkradio("ysfy") == "") {
		alert("请选择运费承担方");
		return false;
	}

	if (checkradio("fkxs") == "1" && checkradio("fkxss") == "1" || checkradio("fkxs") == "2" && checkradio("fkxss") == "1") {}
	else {
		if (checkradio("txcd") == "") {
			alert("付款产生的贴息承担方不能为空！");
			return false;
		}

	}

	if (checkradio("fkxss") == "2" || checkradio("fkxss") == "3") {
		if(checkradio("fkxs")=="5" || checkradio("fkxs")=="10" ){
			var cdhp_v = document.payform.cdhp.value;
			if(cdhp_v=="" || cdhp_v=="0" ){
				alert("银行承兑汇票期限不能为空或者0");
				return false;
			}
		}
		
		if(checkradio("fkxs")=="5" || checkradio("fkxs") == '0'){
			var dwname_v = document.payform.dwname.value;
			if(dwname==""){
				alert("单位名称不能为空");
				return false;
			}
		}
	}

	if (document.getElementById("fkxstype2").value == "0" || document.getElementById("fkxstype2").value == "") {
		alert("付款形式不能为空");
		return false;
	}

	var type2 = document.payform.paytype2.value;

	if (type2 == "") {
		alert("请选择货款支付方式");
		return false;
	}

	if ($("input[type=radio][name=PayType]:checked").val() != type2) {
		alert("请先保存货款支付方式");
		return false;
	}

	var f3 = document.tkform;

	if (f3.qdtime.value == "") {
		alert("签订日期不能为空");
		return false;
	}
	if (f3.qdaddress.value == "") {
		alert("签订地点不能为空");
		return false;
	}

	if (f3.zlbz.value == "") {
		alert("质量标准不能为空");
		return false;
	}
	if (f3.bzbz.value == "") {
		alert("包装标准不能为空");
		return false;
	}

	if (checkradio("hwsl") == "") {
		alert("确认货物数量方式不能为空");
		return false;
	}
	if (f3.kjfp.value == "") {
		alert("开具发票不能为空");
		return false;
	}

	//if (f3.lybzj.value == ""){alert("履约保证金不能为空");return false;}

	if (f3.yqwyj.value == "") {
		alert("交易保证金不能为空");
		return false;
	}

	if (f3.ycfkwyj.value == "") {
		alert("履约保证金不能为空");
		return false;
	}

	if (f3.jhqx.value == "") {
		alert("交货期限不能为空");
		return false;
	}

	if (f3.yqqx.value == "") {
		alert("逾期期限不能为空");
		return false;
	}

	if (f3.jhbd.value == "") {
		alert("交货变动提前通知不能为空");
		return false;
	}

	if (f3.xgzs.value == "") {
		alert("相关证书不能为空");
		return false;
	}
   //add by xiakang for gcwlht ended 2015/10/28
	//add by xiakang for pdf started 2015/08/31
	/*var A = document.getElementById('DisputeSettlementA').checked;
	var B = document.getElementById('DisputeSettlementB').checked;
	if(A ==false && B== false){
		  alert("合同争议的解决方式不能为空");
		   //add for contract by wangjian started 2015/09/17
		  location.href = "#showhttk";
		  //add for contract by wangjian ended 2015/09/17
		  return false;
	}*/
	
	if(DisputeSettlement=="" || DisputeSettlement=="0"){
		alert("合同争议的解决方式不能为空");
		   //add for contract by wangjian started 2015/09/17
		  location.href = "#showhttk";
		  //add for contract by wangjian ended 2015/09/17
		  return false;
	}
	//add by xiakang for pdf ended 2015/08/31
  //add by xiakang for yunqian started 2015/11/05
    var shenpilc_ht=document.getElementById("shenpilc_ht").value;
    var shenpilc=document.getElementsByName("shenpilc");
    var isshenpilc="";
    if(shenpilc_ht=="show"){
	   for(var i=0;i<shenpilc.length;i++){
              if(shenpilc[i].checked){
				  isshenpilc=shenpilc[i].value;
			  }
	   }
       if(isshenpilc==""){
	       alert("您没有选择审批流程组号");
           return false;
	   }
	}
  //add by xiakang for yunqian started 2015/11/05 

	var DDid = document.getElementById("DDid").value;
	var id = document.getElementById("htid").value;
	var mima2 = document.getElementById("mima2").value;
	var mima = document.getElementById("mima").value;
	if (mima == "") {
		mima = mima2;
	}

	if (mima == "") {
		alert("交易密码不能为空");
		return false;
	}
	qrht_DDid=DDid;
    qrht_id=id;
    qrht_mima=mima;
    qrht_shenpilc=isshenpilc;
	
	// obj.style.display="none"; 
	//document.getElementById("wait").style.display = "block";
	obj.onclick = function () {};
	obj.src = "images/btn_qrdd_gray.png";
	obj.onclick = "";
	//alert(DDid);
	document.getElementById("loading").style.display = "";
	// var param = "action=qrhtsales&id="+id+"&DDid="+DDid+"&mima="+mima;
	//var ajax = new Ajax("bizorder.php", setHt, param );
	var param1 = "action=GetUserState";
	var ajax = new Ajax("member.php", GetState, param1);
}

var qrht_DDid = '';
var qrht_id = '';
var qrht_mima = '';
 var qrht_shenpilc='';
 function GetState(returnstr){
         var par=returnstr.split(";");
         var pos= par[0].indexOf("1");
         if((parseInt(par[1])==1)||pos>=0){
	   //update by xiakang for yunqian started 2015/11/05
		  //var param = "action=qrhtsales&id="+qrht_id+"&DDid="+qrht_DDid+"&mima="+qrht_mima;
		  var param = "action=qrhtsales&id="+qrht_id+"&DDid="+qrht_DDid+"&mima="+qrht_mima+"&shenpilc="+qrht_shenpilc;
	   //update by xiakang for yunqian ended 2015/11/05
		  var ajax = new Ajax("bizorder.php", setHt, param );
		 }
		 else{
		 alert("对不起，您没有操作权限");
		 document.getElementById("loading").style.display="none";
		 window.location.reload();
		 }
	        
	 }
//Added for quanxian sheding  by hxy ended  2015/1/30
 function setHt(returnstr){
	String.prototype.trim=function() {

		return this.replace(/(^\s*)|(\s*$)/g, '');
	}
	document.getElementById("loading").style.display="none";
	var str1 = returnstr.trim();
	var str2=str1.split("#");
	var str=str2[0];
	//alert("www"+str);
	if(str == "99"){
	alert("保证金冻结失败");
	location.reload() ;
	}else if(str=="4"){
	alert("订单确认失败，请返回重试！");
	location.reload() ;
	}else if(str=="7"){
	alert("订单购买量超过库存量，请重新填写数量进行提交！");
	location.reload() ;
	}else if(str=="9"){
	alert("交易密码输入错误！");
	location.reload() ;
	}else if(str=="55"){
	alert("没有对应的实体订单！");
	location.reload() ;
	//added by hezp for ksdj started 2015/09/07
	}else if(str=="P0001"){
	alert("不满足集团要求！"+ str2[1] +"资源无网价！");
	location.reload() ;
	}else if(str=="P0002"){
	alert("不满足集团要求！合同金额不符合！");
	location.reload() ;
	}else if(str=="P0003"){
	alert("该客户集团评级为C级，所签合同付款方式只能为先货后款！");
	location.reload() ;
	}else if(str=="P0004"){
	alert("该客户的集团评级为D或者E级，不能确认订单！");
	location.reload() ;
	//added by hezp for ksdj ended 2015/09/07
	}else{
	alert("订单确认成功");
	location.reload() ;
	}
 }

//收货联系人

function shpostData() {

	var f = document.shform;
	if (f.ConsigneeMan.value == '') {
		alert("请填写收货联系人姓名");
		return false;
	}
	if (f.ConsigneePhone.value == '') {
		alert("请填写收货联系人电话");
		return false;
	}
	if (f.ConsigneeAddress.value == '') {
		alert("请填写收货联系人地址");
		return false;
	}
	submitRequest(document.shform, "post", "text", outWritesh, null); //使用自定义myProcess
}

function outWritesh(count) {

	var tmp = count.split("|-|");

	document.getElementById("ConsigneeMan").innerHTML = tmp[0];
	document.getElementById("ConsigneePhone").innerHTML = tmp[1];
	document.getElementById("ConsigneeMobile").innerHTML = tmp[2];
	document.getElementById("ConsigneeFax").innerHTML = tmp[3];
	document.getElementById("ConsigneeEmail").innerHTML = tmp[4];
	document.getElementById("ConsigneeAddress").innerHTML = tmp[5];
	document.getElementById("ConsigneePostCode").innerHTML = tmp[6];

	document.getElementById("showsh").style.display = "block";
	document.getElementById("shdiv").style.display = "none";

}
function editsh() {
	document.getElementById("showsh").style.display = "none"; //隐藏
	document.getElementById("shdiv").style.display = "block"; //隐藏
}

function closesh() {
	document.getElementById("showsh").style.display = "block";
	document.getElementById("shdiv").style.display = "none";
}

//确认发货

function qrfh(obj) {
	//运输公司检查
	if (!fhxxPaytest(document.getElementById("tempoid").value)) {
		return false;
	}

	if (checkfh() == "2") {

		if (confirm("您的发货明细没有填写件数！")) {
			return false;
		} else
			return false;
	}
	if (checkfh() == "5") {
		if (confirm("您的发货明细没有填写件数！")) {
			return false;
		} else
			return false;
	}
	if (checkfh() == "1") {
		alert("请填写数量");
		return false;
	}
	/*
	if(checkfh() == "3"){
	alert("请选择运输公司");
	return false;
	}*/

	//return false;
	document.getElementById("loading").style.display = "";
	document.getElementById("qrdiv0").style.display = "none";
	document.getElementById("qrdiv").style.display = "none";
	document.getElementById("qrdiv2").style.display = "";
	document.getElementById("qrdiv3").style.display = "";

	var id = document.getElementById("htid").value;
	qrfh_id = id;
	//obj.style.display="none";
	//	 document.getElementById("wait").style.display = "block";
	obj.onclick = function () {};
	var param1 = "action=GetUserState";
	var ajax = new Ajax("member.php", GetState4, param1);

}
var qrfh_id = '';
function GetState4(returnstr) {

	var par = returnstr.split(";");
	var pos = par[0].indexOf("5");
	//alert(pos);
	if ((parseInt(par[1]) == 1) || pos >= 0) {
		    // updated by quanjw for qrsfh start 2015/11/3
			//var param = "action=qrfh&id="+qrfh_id;
	        //var ajax = new Ajax("member.php", setFh, param );
			var param = "action=qrfhsl&id="+qrfh_id;
			var ajax2 = new Ajax("member.php", qrFhsl, param );
			// updated by quanjw for qrsfh start 2015/11/3
	} else {
		alert("对不起，您没有操作权限");
		document.getElementById("loading").style.display = "";
		window.location.reload();
	}

}
//Added for quanxian sheding  by hxy ended  2015/1/13
// added by quanjw for qrsfh start 2015/11/3
function qrFhsl(returnstr){
	if(returnstr=="11" || returnstr=="22"){
		document.getElementById("loading").style.display="";
		var cfm=confirm("发货资源总量超过或低于订单的量的20%，是否继续？");
		 
		if (cfm==true){
			var param = "action=qrfh&id="+qrfh_id;
			var ajax = new Ajax("member.php", setFh, param );
			return true;
		}else{
			document.getElementById("loading").style.display="none";
			window.location.reload(); 
			return false;
		}
	}else{
		var param = "action=qrfh&id="+qrfh_id;
		var ajax = new Ajax("member.php", setFh, param );
		return true;
	}
}
// added by quanjw for qrsfh end 2015/11/3
function setFh(returnstr) {
	//alert(returnstr);
	document.getElementById("loading").style.display = "none";

// updated by quanjw for qrsfh start 2015/11/3
/*if(returnstr=="11"){
		//update by libing for fahuo start 2015/04/23
		//alert("发货资源总量不能超过订单的量的5%");
		alert("发货资源总量不能超过订单的量的20%");
		//update by libing for fahuo end 2015/04/23
		document.getElementById("loading").style.display = "none";
		window.location.reload();
		return false;
	}
	if (returnstr == "22") {
		//alert("发货资源总量不能超过订单的量的5%");
		//alert("发货资源总量不能低于订单的量的95%");
		alert("发货资源总量不能低于订单的量的80%");
		//update by libing for fahuo end 2015/04/23
		document.getElementById("loading").style.display = "none";
		window.location.reload();
		return false;
}*/
// updated by quanjw for qrsfh start 2015/11/3

	var id = document.getElementById("ddid").value;
	var str = returnstr;
	if (str == "1") {
		alert("已确认发货单");

		//document.getElementById("zhtai").innerHTML="状态：已发货";
		//document.getElementById("qrdiv").style.display="none";
	}

	location.href = "bizorder.php?view=orderdetail&id=" + id;
	//location.reload() ;

}

//确认收货

function qrsh(obj) {

	var id = document.getElementById("htid").value;
	//obj.style.display="none";
	//	 document.getElementById("wait").style.display = "block";
	obj.onclick = function () {};

	document.getElementById("loading").style.display = "";
	document.getElementById("qrdiv0").style.display = "none";
	document.getElementById("qrdiv1").style.display = "none";
	document.getElementById("qrdiv2").style.display = "";
	document.getElementById("qrdiv3").style.display = "";

	//	     var param = "action=qrsh&id="+id;
	//	     var ajax = new Ajax("member.php", setSh, param );
	qrsh_id = id;
	var param1 = "action=GetUserState";
	var ajax = new Ajax("member.php", GetState5, param1);
}
var qrsh_id = '';
function GetState5(returnstr) {

	var par = returnstr.split(";");
	var pos = par[0].indexOf("5");
	//alert(pos);
	if ((parseInt(par[1]) == 1) || pos >= 0) {
		var param = "action=qrsh&id=" + qrsh_id;
		var ajax = new Ajax("member.php", setSh, param);
	} else {
		alert("对不起，您没有操作权限");
		document.getElementById("loading").style.display = "none";
		window.location.reload();
	}

}
//Added for quanxian sheding  by hxy ended  2015/1/30
function setSh(returnstr) {
	document.getElementById("loading").style.display = "none";
	var id = document.getElementById("ddid").value;
	var str = returnstr;
	if (str == "1") {
		alert("已确认收货单");
		//document.getElementById("zhtai").innerHTML="状态：已收货";
		//document.getElementById("qrdiv0").style.display="none";
	}

	location.href = "bizorder.php?view=orderdetail&id=" + id;
	//location.reload() ;

}

function qrys(obj) {

	var id = document.getElementById("htid").value;
	//obj.style.display="none";
	//	 document.getElementById("wait").style.display = "block";
	obj.onclick = function () {};
	obj.src = "images/btn_qrys_gray.png";
	obj.onclick = "";
	document.getElementById("loading").style.display = "";
	var param = "action=qrys&id=" + id;
	var ajax = new Ajax("bizorder.php", setYs, param);
}
function setYs(returnstr) {
	//alert(returnstr);
	var id = document.getElementById("ddid").value;
	var str = returnstr;
	if (str == "1") {
		document.getElementById("loading").style.display = "none";
		alert("已验收");
		//document.getElementById("zhtai").innerHTML="状态：已收货";
		//document.getElementById("qrdiv").style.display="none";
	}
	location.href = "bizorder.php?view=orderdetail&id=" + id;
	//location.reload() ;

}

function gettctypediv() {
	var salestype; //= document.getElementById("SalesType").value;
	var rlist = document.getElementsByName("bigzytype");
	for (var j = 0; j < rlist.length; j++) {
		if (rlist[j].checked) {
			salestype = rlist[j].value;
		}
	}

	//Added by quanjw for jinshuzhipin start 2015/3/12
	var Vid = document.getElementsByName("Vid");
	for (var i = 0; i < Vid.length; i++) {
		if (Vid[i].checked) {
			Vid = Vid[i].value;
		}

	}
	//Added by quanjw for jinshuzhipin end 2015/3/12

	//修改新增资源时 新增信息
	var tydif;
	//Updated by quanjw for jinshuzhipin start 2015/3/12
	/*if(salestype == "1"){tydif=2;
	location.href="member.php?view=tczy&xs=1&type="+tydif;
	exit;
	}
	if(salestype == "2"  ){tydif=4;
	location.href="member.php?view=tczy&cg=1&type="+tydif;
	exit;
	}

	if( salestype == "3" ){tydif=8;
	location.href="member.php?view=tczy&cg=1&type="+tydif;
	exit;
	}*/

	if (salestype == "1") {
		tydif = 2;
	}

	if (salestype == "2") {
		tydif = 4;
	}

	if (salestype == "3") {
		tydif = 8;
	}
	if (tydif == 8 && Vid == TypeZP_VID) {
		alert("金属制品无工程物流采购!");
		return;
	}
    if (tydif == 8 && Vid == hw_vid) {
        alert("焊网无工程物流采购!");
        return;
    }
	location.href = "member.php?view=tczy&cg=1&type=" + tydif + "&Vid=" + Vid;
	//Updated by quanjw for jinshuzhipin end 2015/3/12
}
function getpltypediv() {
	var salestype; //= document.getElementById("SalesType").value;
	var rlist = document.getElementsByName("bigzytype");
	for (var j = 0; j < rlist.length; j++) {
		if (rlist[j].checked) {
			salestype = rlist[j].value;
		}
	}

	//Added by quanjw for jinshuzhipin start 2015/3/12
	var Vid = document.getElementsByName("Vid");
	for (var i = 0; i < Vid.length; i++) {
		if (Vid[i].checked) {
			Vid = Vid[i].value;
		}

	}
	//Added by quanjw for jinshuzhipin end 2015/3/12

	//修改新增资源时 新增信息
	var tydif;

	//Updated by quanjw for jinshuzhipin start 2015/3/12

	/*if(salestype == "1"){tydif=2;
	location.href="member.php?view=plzy&type="+tydif;
	exit;
	}
	if(salestype == "2" ){tydif=4;
	location.href="member.php?view=plzy&type="+tydif;
	exit;
	}

	if( salestype == "3"){tydif=8;
	location.href="member.php?view=plzy&type="+tydif;
	exit;
	}*/
	if (salestype == "1") {
		tydif = 2;
	}

	if (salestype == "2") {
		tydif = 4;
	}
	if (salestype == "3") {
		tydif = 8;
	}

	if (tydif == 8 && Vid == TypeZP_VID) {
		alert("金属制品无工程物流采购!");
		return;
	}
    if (tydif == 8 && Vid == hw_vid) {
        alert("焊网无工程物流采购!");
        return;
    }
	location.href = "member.php?view=plzy&type=" + tydif + "&Vid=" + Vid;
	//Updated by quanjw for jinshuzhipin start 2015/3/12


}
function gettypediv() {

	var id = document.getElementById("typeid").value;
	var salestype; //= document.getElementById("SalesType").value;
	var rlist = document.getElementsByName("bigzytype");

	for (var j = 0; j < rlist.length; j++) {
		if (rlist[j].checked) {
			salestype = rlist[j].value;
		}
	}

	//Added by quanjw for meijiao start 2015/6/24
	var Vid = document.getElementsByName("Vid");
	for (var i = 0; i < Vid.length; i++) {
		if (Vid[i].checked) {
			Vid = Vid[i].value;
		}

	}
	//Added by quanjw for meijiao end 2015/6/24

	var STORETYPE;
	var typelist = document.getElementsByName("STORETYPE");
	for (var j = 0; j < typelist.length; j++) {
		if (typelist[j].checked) {
			STORETYPE = typelist[j].value;
		}
	}

	//修改新增资源时 新增信息
	var tydif;
	if (salestype == "1") {
		tydif = 2;
	}

	if (salestype == "2") {
		tydif = 4;
	}
	if (salestype == "3") {
		tydif = 8;
	}

	//Updated by quanjw for meijiao start 2015/6/24
	if (tydif == 8 && Vid == TypeZP_VID) {
		alert("金属制品无工程物流采购!");
		return;
	}
	if (tydif == 8 && Vid == hw_vid) {
        alert("焊网无工程物流采购!");
        return;
    }
	//location.href="member.php?view=jymanage&type="+tydif;
	location.href = "member.php?view=jymanage&type=" + tydif + "&Vid=" + Vid;
	//Updated by quanjw for meijiao end 2015/6/24


}
function setType(returnstr) {

	var str = returnstr;
	location.href = "member.php?view=jymanage&id=" + str + "&f=1";
}

function editjb() {
	document.getElementById("showjb").style.display = "none"; //隐藏
	document.getElementById("jbdiv").style.display = "block"; //隐藏
}

function closejb() {
	document.getElementById("showjb").style.display = "block";
	document.getElementById("jbdiv").style.display = "none";
}

function editfj() {
	document.getElementById("showfj").style.display = "none"; //隐藏
	document.getElementById("fjdiv").style.display = "block"; //隐藏
}

function closefj() {
	document.getElementById("showfj").style.display = "block";
	document.getElementById("fjdiv").style.display = "none";
}

//现在销售方式 一口价

function showsaletype() {
	var id = document.getElementById("typeid").value;
	var salestype; //= document.getElementById("SalesType").value;
	var rlist = document.getElementsByName("SalesType");
	for (var j = 0; j < rlist.length; j++) {
		if (rlist[j].checked) {
			salestype = rlist[j].value;
		}
	}

	if (salestype == "1") {
		document.getElementById("saletype").style.display = "block";
		document.getElementById("buytype").style.display = "none";
		document.getElementById("xytype").style.display = "none";
		document.getElementById("gctype").style.display = "none";
	}
	if (salestype == "2") {
		document.getElementById("saletype").style.display = "none";
		document.getElementById("buytype").style.display = "block";
		document.getElementById("xytype").style.display = "none";
		document.getElementById("gctype").style.display = "none";
	}
	if (salestype == "5") {
		document.getElementById("saletype").style.display = "none";
		document.getElementById("buytype").style.display = "none";
		document.getElementById("xytype").style.display = "none";
		document.getElementById("gctype").style.display = "block";
	}
	if (salestype == "6") {
		document.getElementById("saletype").style.display = "none";
		document.getElementById("buytype").style.display = "none";
		document.getElementById("xytype").style.display = "block";
		document.getElementById("gctype").style.display = "none";
	}
}

//确认交割
function qrjg(obj) {
	if (jscheck() == "1") {
		alert("请填写结算价格");
		return false;
	} else if (jscheck() == "2") {
		alert("请填写其它费用单价");
		return false;
	}

	var id = document.getElementById("htid").value;
	// obj.style.display="none";
	var mima = document.getElementById("mima").value;
	var mima2 = document.getElementById("mima2").value;
	qrjg_id = id;
	qrjg_mima = mima;
	if (mima == "") {
		mima = mima2;
	qrjg_mima=mima2;
	}

	if (mima == "") {
		alert("交易密码不能为空");
		return false;
	}

	//	document.getElementById("wait").style.display = "block";
	obj.onclick = function () {};
	obj.src = "images/btn_qrjs_gray.png";
	obj.onclick = "";
	document.getElementById("loading").style.display = "";
	//var param = "action=qrjg&id="+id+"&mima="+mima;
	//var ajax = new Ajax("member.php", setJg, param );
	var param1 = "action=GetUserState";
	var ajax = new Ajax("member.php", GetState6, param1);
}
var qrjg_id = '';
var qrjg_mima = '';
function GetState6(returnstr) {
	var par = returnstr.split(";");
	var pos = par[0].indexOf("2");
	if ((parseInt(par[1]) == 1) || pos >= 0) {
		var param = "action=qrjg&id=" + qrjg_id + "&mima=" + qrjg_mima;
		var ajax = new Ajax("member.php", setJg, param);
	} else {
		alert("对不起，您没有操作权限");
		document.getElementById("loading").style.display = "none";
		window.location.reload();
	}

}
//Added for quanxian sheding  by hxy ended  2015/1/30
function setJg(returnstr) {
	//alert(returnstr);

	document.getElementById("loading").style.display = "none";
	var str = returnstr;
	var id = document.getElementById("ddid").value;

	if (str == "9") {
		alert("交易密码错误");
		location.reload();

	} else if (str == "4") {
		alert("结算失败,请返回重试");
		location.reload();
	} else if (str == "1") {

		alert("确认结算成功");
		//document.getElementById("qrdiv").style.display="none";
	}else{
		console.log(str)
		alert("有点问题哦");return;
	}
	location.href = "bizorder.php?view=orderdetail&id=" + id;
}

function editmyjinjia() {
	document.getElementById("showmyjinjia").style.display = "none";
	document.getElementById("myjinjiadiv").style.display = "block";
}
function closemyjinjia() {
	document.getElementById("showmyjinjia").style.display = "block";
	document.getElementById("myjinjiadiv").style.display = "none";
}

function myjinjiapostData() {

	submitRequest(document.myjinjiaform, "post", "text", myjinjiaoutWrite, null); //使用自定义myProcess
}

function myjinjiaoutWrite(count) {

	var count2 = count.split("|A|");
	
	var tmp = count2[0].split("|-|");
	var tmp2 = count2[1].split("|-|");


	if(document.getElementById("PriceContention")){document.getElementById("PriceContention").innerHTML=tmp[0];}
	if(document.getElementById("BuyQuantity")){document.getElementById("BuyQuantity").innerHTML=tmp[1];}   
	if(document.getElementById("Factory_1")){document.getElementById("Factory_1").value=tmp[2];}   
	if(document.getElementById("caizhi")){document.getElementById("caizhi").innerHTML=tmp[3];} 
	if(document.getElementById("guige")){document.getElementById("guige").innerHTML=tmp[4];} 

	//added by hezp started 2016/11/09
	if(document.getElementById("fkxstype")){document.getElementById("fkxstype").value=tmp2[5];} 
	if(document.getElementById("fkxstype2")){document.getElementById("fkxstype2").value=tmp2[0];} 
	setfkxs();
	
	if(document.getElementById("ReMark_1")){document.getElementById("ReMark_1").value=tmp2[4];} 
	$("input[type=radio][name=txcd_1][value="+tmp2[3]+"]").attr("checked","checked");
	$("input[type=radio][name=KaiPiaoFangShi_1][value="+tmp2[1]+"]").attr("checked","checked");
	$("input[type=radio][name=JiLiangFangShi_1][value="+tmp2[2]+"]").attr("checked","checked");
	$("input[type=radio][name=fkxss_1][value="+tmp2[5]+"]").attr("checked","checked");
	$("input[type=radio][name=fkxs_1][value="+tmp2[0]+"]").attr("checked","checked");
	$("input[type=radio][name=fkxs2_1][value="+tmp2[6]+"]").attr("checked","checked");
	//added by hezp ended 2016/11/09

	document.getElementById("showmyjinjia").style.display = "block";
	document.getElementById("myjinjiadiv").style.display = "none";

}

//图片
function edittp() {
	document.getElementById("showtp").style.display = "none"; //隐藏
	document.getElementById("tpdiv").style.display = "block"; //隐藏
}

function closetp() {
	document.getElementById("showtp").style.display = "block";
	document.getElementById("tpdiv").style.display = "none";
}

// Added for quanxian sheding(leftmenu)  by hxy started  2015/1/13
function dele(id) {
	// var id = document.getElementById("zyid").value;
	var param1 = "action=GetUserState";
	del_id = id;
	var ajax = new Ajax("member.php", Del_GetState, param1);
}
var del_id = '';
function Del_GetState(returnstr) {
	var par = returnstr.split(";");
	var pos = par[0].indexOf("1");
	if ((parseInt(par[1]) == 1) || pos >= 0) {
		if (window.confirm("是否确认删除此条信息?")) {
			var param = "action=delzy&id=" + del_id;
			var ajax = new Ajax("member.php", setdel, param);
		}
	} else {
		alert("对不起，您没有操作权限");
		// document.getElementById("loading").style.display="none";
		window.location.reload();
	}
}
// Added for quanxian sheding(leftmenu)  by hxy ended  2015/1/30

function setdel(returnstr) {
	var str = returnstr;
	if (str == "1") {
		alert("删除成功");
		//location.href="member.php?view=newzylist";
	} else {
		alert("删除失败"); //location.href="member.php?view=newzylist";
	}
	location.reload();
}

function setUser() {

	var userid;
	var rlist = document.getElementsByName("user");
	for (var j = 0; j < rlist.length; j++) {
		if (rlist[j].checked) {
			userid = rlist[j].value;
		}
	}

	var param = "action=setUser&id=" + userid;
	var ajax = new Ajax("member.php", setUserMessage, param);
}

function setUserMessage(returnstr) {
	var tmp = returnstr.split("|X|");

	document.getElementById("ContactMan").value = tmp[0];
	document.getElementById("ContactPhone").value = tmp[1];
	document.getElementById("ContactMobile").value = tmp[2];
	document.getElementById("ContactFax").value = tmp[3];
	document.getElementById("ContactEmail").value = tmp[4];
}

function adduser() {
	if (document.userform22.ContactMan.value == "") {
		alert("联系人不能为空");
		return false;
	}
	if (document.userform22.ContactMobile.value == "" && document.userform22.ContactPhone.value == "") {
		alert("固定电话或者手机其中一项不能为空");
		return false;
	}
	submitRequest(document.userform22, "post", "text", adduseroutWrite, null); //使用自定义myProcess
}

function adduseroutWrite(count) {

	var tmp = count.split("|-|");

	document.getElementById("ContactMan2").innerHTML = tmp[0];
	document.getElementById("ContactPhone2").innerHTML = tmp[1];
	document.getElementById("ContactMobile2").innerHTML = tmp[2];
	document.getElementById("ContactFax2").innerHTML = tmp[3];
	document.getElementById("ContactEmail2").innerHTML = tmp[4];
	document.getElementById("QQNum2").innerHTML = tmp[5];

	document.getElementById("showfh").style.display = "block";
	document.getElementById("fhdiv").style.display = "none";

	//显示附件相关信息
	document.getElementById("fjdiv").style.display = "block";
	document.getElementById("showfj").style.display = "none";

}

//发货联系人信息 demo
function fhpostData2() {

	var f = document.fh2;
	if (f.ShipperFhck.value == '') {
		alert("请填写发货仓库");
		return false;
	}
	if (checkradio("ShipperYsfs") == '') {
		alert("请选择运输方式");
		return false;
	}

	submitRequest(document.fh2, "post", "text", outWritefh2, null); //使用自定义myProcess
}

function outWritefh2(count) {

	var tmp = count.split("|-|");

	document.getElementById("ShipperMan2").innerHTML = tmp[0];
	document.getElementById("ShipperPhone2").innerHTML = tmp[1];
	document.getElementById("ShipperMobile2").innerHTML = tmp[2];
	document.getElementById("ShipperFax2").innerHTML = tmp[3];
	document.getElementById("ShipperEmail2").innerHTML = tmp[4];
	document.getElementById("ShipperPostCode2").innerHTML = tmp[5];
	document.getElementById("ShipperQQNum2").innerHTML = tmp[6];
	document.getElementById("ShipperAddressState2").innerHTML = tmp[7];
	document.getElementById("ShipperAddressCity2").innerHTML = tmp[8];
	document.getElementById("ShipperAddress2").innerHTML = tmp[9];
	document.getElementById("ShipperFhck2").innerHTML = tmp[10];
	document.getElementById("ShipperYsfs2").innerHTML = tmp[11];
	document.getElementById("ConsigneeYsfs22").innerHTML = tmp[11];
	document.getElementById("ConsigneeYsfs222").innerHTML = tmp[11];
	//,"ShipperAddressState","ShipperAddressCity","ShipperAddress","ShipperFhck","ShipperYsfs"


	document.getElementById("showfh").style.display = "block";
	document.getElementById("fhdiv").style.display = "none";

	// document.getElementById("showfhdz").style.display="none";
	// document.getElementById("fhdzdiv").style.display="block";


}

//收货联系人信息 demo
function shpostData2() {

	var f = document.sh22;
	var dh = document.getElementById("shfs").innerHTML;
	if (f.ConsigneeDhck.value == '') {
		alert("请填写" + dh + "信息");
		return false;
	}
	if (checkradio("ConsigneeYsfs") == '') {
		alert("请选择运输方式");
		return false;
	}
	//if( f.ConsigneePhone.value == '' ){
	//alert( "请填写收货联系人电话" );
	//return false;
	//}

	submitRequest(document.sh22, "post", "text", outWritesh2, null); //使用自定义myProcess
}

function outWritesh2(count) {

	var tmp = count.split("|-|");

	document.getElementById("ConsigneeMan2").innerHTML = tmp[0];
	document.getElementById("ConsigneePhone2").innerHTML = tmp[1];
	document.getElementById("ConsigneeMobile2").innerHTML = tmp[2];
	document.getElementById("ConsigneeFax2").innerHTML = tmp[3];
	document.getElementById("ConsigneeEmail2").innerHTML = tmp[4];

	document.getElementById("ConsigneePostCode2").innerHTML = tmp[5];
	document.getElementById("ConsigneeQQNum2").innerHTML = tmp[6];

	document.getElementById("ConsigneeAddressState2").innerHTML = tmp[7];
	document.getElementById("ConsigneeAddressCity2").innerHTML = tmp[8];
	document.getElementById("ConsigneeAddress2").innerHTML = tmp[9];
	document.getElementById("ConsigneeDhck2").innerHTML = tmp[10];
	document.getElementById("ConsigneeYsfs2").innerHTML = tmp[11];
	document.getElementById("ConsigneeYsfs22").innerHTML = tmp[11];
	document.getElementById("ConsigneeYsfs222").innerHTML = tmp[11];

	document.getElementById("showsh").style.display = "block";
	document.getElementById("shdiv").style.display = "none";

	//  document.getElementById("showshdz").style.display="none";
	// document.getElementById("shdzdiv").style.display="block";

}

//质保书div

function editzhibao() {
	document.getElementById("showzhibao").style.display = "none"; //隐藏
	document.getElementById("zhibaodiv").style.display = "block"; //隐藏
}

function closezhibao() {
	document.getElementById("showzhibao").style.display = "block";
	document.getElementById("zhibaodiv").style.display = "none";
}

function editfhdz() {
	document.getElementById("showfhdz").style.display = "none";
	document.getElementById("fhdzdiv").style.display = "block";
}
function closefhdz() {
	document.getElementById("showfhdz").style.display = "block";
	document.getElementById("fhdzdiv").style.display = "none";
}

function editshdz() {
	document.getElementById("showshdz").style.display = "none";
	document.getElementById("shdzdiv").style.display = "block";
}
function closeshdz() {
	document.getElementById("showshdz").style.display = "block";
	document.getElementById("shdzdiv").style.display = "none";
}

//发货联系人地址
function fhpostdzData() {

	var f = document.fhdzform;

	if (f.ShipperAddressState.value == '') {
		alert("请填写省份");
		return false;
	}
	if (f.ShipperAddressCity.value == '') {
		alert("请填写城市");
		return false;
	}
	if (f.ShipperPostCode.value == '') {
		alert("请填写邮编");
		return false;
	}
	if (f.ShipperAddress.value == '') {
		alert("请填写地址");
		return false;
	}
	f.action = "bizorder.php?action=fhlxraddress";
	submitRequest(document.fhdzform, "post", "text", outWritefh3, null); //使用自定义myProcess
}

function outWritefh3(count) {

	var tmp = count.split("|-|");

	document.getElementById("ShipperAddressState2").innerHTML = tmp[0];
	document.getElementById("ShipperAddressCity2").innerHTML = tmp[1];
	document.getElementById("ShipperPostCode2").innerHTML = tmp[2];
	document.getElementById("ShipperAddress2").innerHTML = tmp[3];

	// document.getElementById("showfhdz").style.display="block";
	// document.getElementById("fhdzdiv").style.display="none";

}

//收货联系人地址
function shpostdzData() {

	var f = document.shdzform;

	if (f.ConsigneeAddressState.value == '') {
		alert("请填写省份");
		return false;
	}
	if (f.ConsigneeAddressCity.value == '') {
		alert("请填写城市");
		return false;
	}
	if (f.ConsigneePostCode.value == '') {
		alert("请填写邮编");
		return false;
	}
	if (f.ConsigneeAddress.value == '') {
		alert("请填写地址");
		return false;
	}
	f.action = "bizorder.php?action=shlxraddress";
	submitRequest(document.shdzform, "post", "text", outWritesh3, null); //使用自定义myProcess
}

function outWritesh3(count) {

	var tmp = count.split("|-|");

	document.getElementById("ConsigneeAddressState2").innerHTML = tmp[0];
	document.getElementById("ConsigneeAddressCity2").innerHTML = tmp[1];
	document.getElementById("ConsigneePostCode2").innerHTML = tmp[2];
	document.getElementById("ConsigneeAddress2").innerHTML = tmp[3];

	// document.getElementById("showshdz").style.display="block";
	// document.getElementById("shdzdiv").style.display="none";

}

function choosedz(id) {
	var param = "action=choosedz&id=" + id;
	var ajax = new Ajax("member.php", setDz, param);
}

function setDz(returnstr) {
	var f = document.fhdzform;

	var tmp = returnstr.split("|-|");

	f.ShipperAddressState.value = tmp[0];
	f.ShipperAddressCity.value = tmp[1];
	f.ShipperPostCode.value = tmp[2];
	f.ShipperAddress.value = tmp[3];
}

function choosedz2(id) {
	var param = "action=choosedz&id=" + id;
	var ajax = new Ajax("member.php", setDz2, param);
}

function setDz2(returnstr) {
	var f = document.shdzform;

	var tmp = returnstr.split("|-|");

	f.ConsigneeAddressState.value = tmp[0];
	f.ConsigneeAddressCity.value = tmp[1];
	f.ConsigneePostCode.value = tmp[2];
	f.ConsigneeAddress.value = tmp[3];
}

//添加常用地址
function addaddress() {

	var f = document.fhdzform;
	f.action = "member.php?action=addaddress";

	submitRequest(document.fhdzform, "post", "text", outWritefh4, null); //使用自定义myProcess
}

function outWritefh4(count) {
	if (count == "1") {
		alert("已存在相同的联系地址");
	} else {
		$("#contacttab3 tbody").append(count);
	}
	//document.getElementById("contacttab2").append(count);

	// var tmp = count.split("|-|");

}

//添加常用地址
function addaddress2() {

	var f = document.shdzform;
	f.action = "member.php?action=addaddress2";

	submitRequest(document.shdzform, "post", "text", outWritesh4, null); //使用自定义myProcess
}

function outWritesh4(count) {
	if (count == "1") {
		alert("已存在相同的联系地址");
	} else {
		$("#contacttab22 tbody").append(count);
	}
	//document.getElementById("contacttab2").append(count);

	// var tmp = count.split("|-|");

}

function delconaddress(id) {
	if (confirm('请确认是否删除！')) {
		var param = "action=deldz&id=" + id;
		var ajax = new Ajax("member.php", setdelDz, param);
		$("#address" + id).remove();
	}
}
function setdelDz(returnstr) {
	if (returnstr == "1") {
		alert("删除成功");

	}
}
function delconaddress2(id) {
	if (confirm('请确认是否删除！')) {
		var param = "action=deldz&id=" + id;
		var ajax = new Ajax("member.php", setdelDz2, param);
		$("#address2" + id).remove();
	}
}
function setdelDz2(returnstr) {
	if (returnstr == "1") {
		alert("删除成功");

	}
}

//支付方式
function postPay() {
	var f = document.payform;
	var aa = "";
	aa = checkradio("PayType");
	if (aa == "") {
		alert("请选择支付方式");
		return false;
	}
	//alert(checkradio("fkxs"));

	//llh add 20140930
	if (checkradio("PayType") == '4' && f.lybzj.value > 0) {
		alert("您选择了先货后款,预付款比例不能大于0");
		return false;
	}
	//Added by quanjw for bug426 start 2015/7/23
	//在线支付 预付款比例为0 支付出现问题
	if ( checkradio("PayType") == '1' && checkradio("fkxs") == '1' && f.lybzj.value <=0) {
		alert("先款后货,在线支付预付款比例不能低于0");
		return false;
	}
	//Added by quanjw for bug426 start 2015/7/23
	if (checkradio("fkxss") == "1" && checkradio("fkxs") != '1' && checkradio("fkxs") != '2' && checkradio("fkxs") != '3') {
		alert("付款形式不能为空");
		return false;
	}
	if (checkradio("fkxss") == "2" && checkradio("fkxs") != '4' && checkradio("fkxs") != '5') {
		alert("付款形式不能为空");
		return false;
	}
	if (checkradio("fkxss") == "2") {
   //Update by xiakng for fkfs started 2015/09/08
		/*if (f.cdhp.value == "") {
			alert("银行承兑期限不能为空");
			return false;
		}*/
		if (checkradio("fkxs")=='5' && f.cdhp.value == "") {
			alert("银行承兑期限不能为空");
			return false;
		}

		/*if (checkradio("fkxs") == '4') {
			if (f.dwname.value == '') {
				alert("单位名称不能为空");
				return false;
			}
			if (f.kpbank.value == '') {
				alert("开票银行不能为空");
				return false;
			}
			if (f.kctime.value == '') {
				alert("开出时间不能为空");
				return false;
			}
			if (f.txl.value == '') {
				alert("贴息率不能为空");
				return false;
			}
			if (checkradio("danwei") == '') {
				alert("选择贴息率日月");
				return false;
			}
		}*/
    //Update by xiakng for fkfs ended 2015/09/08
		if (checkradio("fkxs") == '5' && f.dwname.value == '') {
			alert("单位名称不能为空");
			return false;
		}

	}
	if (checkradio("fkxss") == "3" && checkradio("fkxs") == '0' && checkradio("fkxs2") != '7' && checkradio("fkxs2") != '8' && checkradio("fkxs2") != '10' ) {
		alert("付款形式不能为空");
		return false;
	}

	if (checkradio("fkxss") == "3" && checkradio("fkxs") == '0' 　 && f.dwname2.value == '') {
		alert("单位名称不能为空");
		return false;
	}

	if (checkradio("fkxss") == "3" && checkradio("fkxs")=='10' && (f.cdhp.value == "" || f.cdhp.value == 0) ) {
			alert("银行承兑期限不能为空或者0");
			return false;
	}

	if (f.fkqx.value == "") {
		alert("付款期限不能为空");
		return false;
	}

	if (checkradio("fkxs") == "" && checkradio("fkxs2") == "") {
		alert("付款形式不能为空");
		return false;
	}
	if (checkradio("fkxs") == "0" && checkradio("fkxs2") == "") {
		alert("付款形式不能为空");
		return false;
	}
	if (checkradio("fkxs") == "9" && f.dwname3.value == "") {
		alert("自定义付款形式不能为空");
		return false;
	}
	if (checkradio("fkxs") == "3" && checkradio("txcd") == "") {
		alert("付款产生的贴息承担方不能为空");
		return false;
	}
	//if(checkradio("fkxs") == "2" && checkradio("txcd")==""){alert("付款产生的贴息承担方不能为空");return false;}
	if (checkradio("fkxs") != "1" && checkradio("fkxss") != "1" || checkradio("fkxs") !== "2" && checkradio("fkxss") != "1") {

		if (checkradio("txcd") == "") {
			alert("付款产生的贴息承担方不能为空");
			return false;
		}

	} else if (checkradio("fkxs") == "1" && checkradio("fkxss") == "1" || checkradio("fkxs") == "2" && checkradio("fkxss") == "1") {
		//alert("11111");
		//document.getElementById("txcdXK").value = "";
		document.getElementById("fktxQT").style.display = "none"; //将付款贴息信息选项隐藏
		//alert(document.getElementById("txcdXK").value);

	}

	submitRequest(document.payform, "post", "text", outWritepay, null); //使用自定义myProcess
}

function outWritepay(count) {
	var tempyfkje3 = document.getElementById("yfkje3").innerHTML;
	var tmp = count.split("|-|");
	tmp[16] = tempyfkje3;
	document.getElementById("zffs").innerHTML = tmp[0];
	document.getElementById("fkqx").innerHTML = tmp[1];
	document.getElementById("cdhp").innerHTML = tmp[2];
	document.getElementById("txcd").innerHTML = tmp[3];
	//document.getElementById( "fkxs" ).innerHTML = tmp[4];
	document.getElementById("kctime").innerHTML = tmp[5];
	document.getElementById("txl").innerHTML = tmp[6];
	document.getElementById("dwname").innerHTML = tmp[7];
	document.getElementById("danwei").innerHTML = tmp[8];
	document.getElementById("kpbank").innerHTML = tmp[9];
	document.getElementById("lybzj").innerHTML = tmp[10];
	document.getElementById("yfkje").innerHTML = tmp[16];
	document.payform.paytype2.value = tmp[11];
	document.payform.fkxstype2.value = tmp[12];
	document.payform.fkxstype.value = tmp[13];
	if (tmp[12] == "9") {
		document.getElementById("fkxs").innerHTML = tmp[15];
	} else {
		document.getElementById("fkxs").innerHTML = tmp[14];
	}
	if (tmp[12] != "1" && tmp[12] != "2" && tmp[12] != "3" && tmp[12] != "6" && tmp[12] != "9" && tmp[12] != "10") {
		document.getElementById("other2").style.display = "";
	} else {
		document.getElementById("other2").style.display = "none";
	}
	if (tmp[12] == "4") {
		document.getElementById("yhcd2").style.display = "";
		document.getElementById("yhcd5").style.display = '';
	} else {
		document.getElementById("yhcd2").style.display = "none";
		document.getElementById("yhcd5").style.display = 'none';
	}

	document.getElementById("show").style.display = "block";
	document.getElementById("jgdiv").style.display = "none";
	//window.location.reload();
}

function edithttk() {
	document.getElementById("showhttk").style.display = "none";
	document.getElementById("httkdiv").style.display = "block";
}
function closehttk() {
	document.getElementById("showhttk").style.display = "block";
	document.getElementById("httkdiv").style.display = "none";
}

//合同条款

//支付方式
function postHttk() {
	var f = document.tkform;
	if (f.qdtime.value == "") {
		alert("签订日期不能为空");
		return false;
	}
	if (f.qdaddress.value == "") {
		alert("签订地点不能为空");
		return false;
	}

	if (f.zlbz.value == "") {
		alert("质量标准不能为空");
		return false;
	}
	if (f.bzbz.value == "") {
		alert("包装标准不能为空");
		return false;
	}

	if (checkradio("hwsl") == "") {
		alert("确认货物数量方式不能为空");
		return false;
	}
	if (f.kjfp.value == "") {
		alert("开具发票不能为空");
		return false;
	}

	//if (f.lybzj.value == ""){alert("履约保证金不能为空");return false;}

	if (f.yqwyj.value == "") {
		alert("交易保证金不能为空");
		return false;
	}

	if (f.ycfkwyj.value == "") {
		alert("履约保证金不能为空");
		return false;
	}

	if (f.jhqx.value == "") {
		alert("交货期限不能为空");
		return false;
	}

	if (f.yqqx.value == "") {
		alert("逾期期限不能为空");
		return false;
	}

	if (f.jhbd.value == "") {
		alert("交货变动提前通知不能为空");
		return false;
	}

	if (f.xgzs.value == "") {
		alert("相关证书不能为空");
		return false;
	}
 //add by xiakang for isshow started 2015/09/11 
    if(f.IsShow.checked){f.IsShow.value='1';}else{f.IsShow.value='0';}
 //add by xiakang for isshow started 2015/09/11

	//ADD by xiakang for pdf Started 2015/08/29 
	var A = document.getElementById('DisputeSettlementA').checked;
	var B = document.getElementById('DisputeSettlementB').checked;
	if(A ==false && B== false){
		  alert("合同争议的解决方式不能为空");return false;
		  }
	if(A ==true)
	  { 
         String.prototype.trim=function() {
			return this.replace(/(^\s*)|(\s*$)/g,'');
		 }
	     var DisputeSettlement_city =f.DisputeSettlement_city.value.trim();
         
		 if(DisputeSettlement_city == ""){
		 alert("相关城市地区不能为空");return false;
		}
      }  
   //ADD by xiakang for pdf ended 2015/08/29 
	document.getElementById("showhttk").style.display = "block";
	submitRequest(document.tkform, "post", "text", outWritetk, null); //使用自定义myProcess
}
//ADD by xiakang for pdf started 2015/08/29
function htzyfs(){
	var f = document.tkform;
	var B = document.getElementById('DisputeSettlementB').checked;
	if (B==true)
	{
       f.DisputeSettlement_city.value="";
	}
}
//ADD by xiakang for pdf ended 2015/08/29
function outWritetk(count) {
    
	var tmp = count.split("|-|");
	//"qdtime","qdaddress","zlbz","bzbz","xgzs","hwsl","kjfp","jhqx","lybzj","yqwyj","yqqx","fjtk","ycfkwyj","jhbd"
	document.getElementById("qdtime").innerHTML = tmp[0];
	document.getElementById("qdaddress").innerHTML = tmp[1];
	document.getElementById("zlbz").innerHTML = tmp[2];
	document.getElementById("bzbz").innerHTML = tmp[3];
	// document.getElementById( "ysfy" ).innerHTML = tmp[4];
	document.getElementById("xgzs").innerHTML = tmp[4];
	document.getElementById("hwsl").innerHTML = tmp[5];
	document.getElementById("kjfp").innerHTML = tmp[6];
	//document.getElementById( "fkqx" ).innerHTML = tmp[8];
	document.getElementById("jhqx").innerHTML = tmp[7];
	//document.getElementById( "fkxs" ).innerHTML = tmp[10];
	//document.getElementById( "cdhp" ).innerHTML = tmp[11];
	//document.getElementById( "txcd" ).innerHTML = tmp[12];

	//document.getElementById( "lybzj" ).innerHTML = tmp[8];
	document.getElementById("yqwyj").innerHTML = tmp[8];
	document.getElementById("yqqx").innerHTML = tmp[9];
	document.getElementById("fjtk").innerHTML = tmp[10];

	document.getElementById("ycfkwyj").innerHTML = tmp[11];
	document.getElementById("jhbd").innerHTML = tmp[12];
	document.getElementById("gcwlht").innerHTML = tmp[13];
	//ADD by xiakang for pdf started 2015/08/29
	 if(tmp[14]=="1")
	 {
	 document.getElementById( "DisputeSettlement" ).innerHTML = '提交'+tmp[15]+'仲裁委员会仲裁。';
     }
	 if(tmp[14]=="2"){
	 document.getElementById( "DisputeSettlement" ).innerHTML = "向合同签订地人民法院诉讼。";
	 } 
	 //ADD by xiakang for pdf ended 2015/08/29

	 //ADD by xiakang for pdf started 2015/09/11
	 if(tmp[16]=="1")
	 {
	 document.getElementById( "IsShow" ).innerHTML = '显示';
     }
	 if(tmp[16]=="0"){
	 document.getElementById( "IsShow" ).innerHTML = "不显示";
	 }
	 document.getElementById( "summary" ).innerHTML = tmp[17];
	 document.getElementById( "ChengbanName" ).innerHTML = tmp[18];
	 document.getElementById( "yzf_Shipper" ).innerHTML = tmp[19];
	 document.getElementById( "yzf_Consignee" ).innerHTML = tmp[20];
	 //ADD by xiakang for pdf ended 2015/08/29
     document.getElementById("showhttk").style.display="block";
     document.getElementById("httkdiv").style.display="none";
	
}

//确认生成合同

function qrscht2(obj, s) {

	if ($("input[type=radio][name=PayType]:checked").val() > 0) {}
	else {
		alert("请选择货款支付方式");
		return false;
	}

	var type2 = document.payform.paytype2.value;
	if ($("input[type=radio][name=PayType]:checked").val() != type2) {
		alert("请先保存货款支付方式");
		return false;
	}

	if (s == "1") {
		var f = document.sh22;
		var f2 = document.shdzform;

		var f3 = document.tkform;
		if (f3.qdtime.value == "") {
			alert("签订日期不能为空");
			return false;
		}
		if (f3.qdaddress.value == "") {
			alert("签订地点不能为空");
			return false;
		}

		if (f3.zlbz.value == "") {
			alert("质量标准不能为空");
			return false;
		}
		if (f3.bzbz.value == "") {
			alert("包装标准不能为空");
			return false;
		}

		if (checkradio("ysfy") == "") {
			alert("运输相关费用承担方不能为空");
			return false;
		}
		if (f3.xgzs.value == "") {
			alert("相关证书份数不能为空");
			return false;
		}
		if (checkradio("hwsl") == "") {
			alert("确认货物数量方式不能为空");
			return false;
		}
		if (f3.kjfp.value == "") {
			alert("开具发票不能为空");
			return false;
		}
		if (f3.fkqx.value == "") {
			alert("付款期限不能为空");
			return false;
		}
		if (f3.jhqx.value == "") {
			alert("交货期限不能为空");
			return false;
		}
		if (checkradio("fkxs") == "") {
			alert("付款形式不能为空");
			return false;
		}
		if (checkradio("fkxs") == "2") {
			if (f3.cdhp.value == "") {
				alert("银行承兑期限不能为空");
				return false;
			}
		}

		if (checkradio("txcd") == "") {
			alert("付款产生的贴息承担方不能为空");
			return false;
		}

		//	if (f3.lybzj.value == ""){alert("履约保证金不能为空");return false;}

		if (f3.yqwyj.value == "") {
			alert("交易保证金不能为空");
			return false;
		}
		if (f3.yqqx.value == "") {
			alert("逾期期限不能为空");
			return false;
		}

		if (f.ConsigneeMan.value == '') {
			alert("请填写收货联系人姓名");
			return false;
		}
		if (f.ConsigneePhone.value == '' && f.ConsigneeMobile.value == '') {
			alert("请填写收货联系人手机或电话");
			return false;
		}
		if (f2.ConsigneeAddressState.value == '') {
			alert("请选择省份");
			return false;
		}
		if (f2.ConsigneeAddressCity.value == '') {
			alert("请填写城市");
			return false;
		}
		if (f2.ConsigneeAddress.value == '') {
			alert("请填写收货联系人地址");
			return false;
		}

		if ((document.getElementById("ConsigneeMan2").innerHTML != f.ConsigneeMan.value)
			 || (document.getElementById("ConsigneePhone2").innerHTML != f.ConsigneePhone.value)
			 || (document.getElementById("ConsigneeMobile2").innerHTML != f.ConsigneeMobile.value)) {
			alert("请先保存收货联系人信息");
			return false;
		}
		if ((document.getElementById("ConsigneeAddress2").innerHTML != f2.ConsigneeAddress.value)
			 || (document.getElementById("ConsigneeAddressState2").innerHTML != f2.ConsigneeAddressState.value)
			 || (document.getElementById("ConsigneeAddressCity2").innerHTML != f2.ConsigneeAddressCity.value)
			 || (document.getElementById("ConsigneePostCode2").innerHTML != f2.ConsigneePostCode.value)) {
			alert("请先保存收货地址信息");
			return false;
		}

	}

	if (s == "2") {
		var f = document.fh2;
		var f2 = document.fhdzform;

		var f3 = document.tkform;
		if (f3.qdtime.value == "") {
			alert("签订日期不能为空");
			return false;
		}
		if (f3.qdaddress.value == "") {
			alert("签订地点不能为空");
			return false;
		}

		if (f3.zlbz.value == "") {
			alert("质量标准不能为空");
			return false;
		}
		if (f3.bzbz.value == "") {
			alert("包装标准不能为空");
			return false;
		}

		if (checkradio("ysfy") == "") {
			alert("运输相关费用承担方不能为空");
			return false;
		}
		if (f3.xgzs.value == "") {
			alert("相关证书份数不能为空");
			return false;
		}
		if (checkradio("hwsl") == "") {
			alert("确认货物数量方式不能为空");
			return false;
		}
		if (f3.kjfp.value == "") {
			alert("开具发票不能为空");
			return false;
		}
		if (f3.fkqx.value == "") {
			alert("付款期限不能为空");
			return false;
		}
		if (f3.jhqx.value == "") {
			alert("交货期限不能为空");
			return false;
		}
		if (checkradio("fkxs") == "") {
			alert("付款形式不能为空");
			return false;
		}
		if (checkradio("fkxs") == "2") {
			if (f3.cdhp.value == "") {
				alert("银行承兑期限不能为空");
				return false;
			}
		}

		if (checkradio("txcd") == "") {
			alert("付款产生的贴息承担方不能为空");
			return false;
		}

		//if (f3.lybzj.value == ""){alert("履约保证金不能为空");return false;}

		if (f3.yqwyj.value == "") {
			alert("交易保证金不能为空");
			return false;
		}
		if (f3.yqqx.value == "") {
			alert("逾期期限不能为空");
			return false;
		}

		if (f.ShipperMan.value == '') {
			alert("请填写发货联系人姓名");
			return false;
		}
		if (f.ShipperPhone.value == '' && 　f.ShipperMobile.value == '') {
			alert("请填写发货联系人手机或电话");
			return false;
		}
		if (f2.ShipperAddressState.value == '') {
			alert("请选择省份");
			return false;
		}
		if (f2.ShipperAddressCity.value == '') {
			alert("请填写城市");
			return false;
		}
		if (f2.ShipperAddress.value == '') {
			alert("请填写发货联系人地址");
			return false;
		}

		if ((document.getElementById("ShipperMan2").innerHTML != f.ShipperMan.value)
			 || (document.getElementById("ShipperPhone2").innerHTML != f.ShipperPhone.value)
			 || (document.getElementById("ShipperMobile2").innerHTML != f.ShipperMobile.value)) {
			alert("请先保存发货联系人信息");
			return false;
		}
		if ((document.getElementById("ShipperAddress2").innerHTML != f2.ShipperAddress.value)
			 || (document.getElementById("ShipperAddressState2").innerHTML != f2.ShipperAddressState.value)
			 || (document.getElementById("ShipperAddressCity2").innerHTML != f2.ShipperAddressCity.value)
			 || (document.getElementById("ShipperPostCode2").innerHTML != f2.ShipperPostCode.value)) {
			alert("请先保存发货地址信息");
			return false;
		}
	}
	//obj.style.display = "none";
	//document.getElementById("wait").style.display = "block";
	obj.onclick = function () {};
	var DDid = document.getElementById("DDid").value;
	var id = document.getElementById("htid").value;
	var param = "action=qrschtsales&id=" + id + "&DDid=" + DDid;
	var ajax = new Ajax("bizorder.php", setHtsc, param);
}

function setHtsc(returnstr) {
	var str = returnstr;
	if (str == "1") {
		//document.getElementById("qrdiv").style.display="none";
		//document.getElementById("lxr").style.display="none";
		//document.getElementById("zhtai").innerHTML="状态：合同已确认等待买方确认";
		alert("订单已生成");
		location.reload();
	}
	if (str == "2") {
		alert("订单已生成");
		//document.getElementById("qrdiv").style.display="none";
		//document.getElementById("slxr").style.display="none";
		//document.getElementById("zhtai").innerHTML="状态：合同已确认等待卖方确认";
		location.reload();
	}
	if (str == "3") {
		alert("订单已生成");
		location.reload();
	}

}

function qrsk(obj) {

	var id = document.getElementById("htid").value;

	var mima = document.getElementById("mima").value;
	var mima2 = document.getElementById("mima2").value;
	if (mima == "") {
		mima = mima2;
	}
	var pytd = document.getElementById("pytd").value;

	if (mima == "") {
		if (pytd == "0") {
			alert("支付密码不能为空");
		} else {
			//Update by xiakang for zhifu started 2015/04/27
			//alert("交易密码不能为空");
			alert("支付密码不能为空");
			//Update by xiakang for zhifu ended 2015/04/27
		}
		return false;
	}
	qrsk_id = id;
	qrsk_mima = mima;
	//obj.style.display = "none";
	//	document.getElementById("wait").style.display = "block";
	obj.onclick = function () {};
	obj.src = "images/btn_qrsk_gray.png";
	obj.onclick = "";
	document.getElementById("loading").style.display = "";
	//var param = "action=qrsk&id="+id+"&mima="+mima;
	//var ajax = new Ajax("member.php", setHtsk, param );
	var param1 = "action=GetUserState";
	var ajax = new Ajax("member.php", GetState_sk, param1);
}
var qrsk_id = '';
var qrsk_mima = '';
function GetState_sk(returnstr) {
	var par = returnstr.split(";");
	if ((parseInt(par[1]) == 1) || (parseInt(par[2]) == 1)) {
		var param = "action=qrsk&id=" + qrsk_id + "&mima=" + qrsk_mima;
		var ajax = new Ajax("member.php", setHtsk, param);
	} else {
		alert("对不起，您没有操作权限");
		document.getElementById("loading").style.display = "none";
		window.location.reload();
	}

}
//Added for quanxian sheding  by hxy ended  2015/1/30
function setHtsk(returnstr) {
	document.getElementById("loading").style.display = "none";
	var id = document.getElementById("ddid").value;
	var pytd = document.getElementById("pytd").value;
	var str = returnstr;

	if (str == "9") {
		if (pytd == "0") {
			alert("支付密码输入错误");
		}
		if (pytd == "1") {
			//Update by xiakang for zhifu started 2015/04/27
			//alert("交易密码输入错误");
			alert("支付密码输入错误");
			//Update by xiakang for zhifu ended 2015/04/27
		}

		location.reload();
	} else { //if(str == "1")
		alert("确认成功");
		location.href = "bizorder.php?view=orderdetail&id=" + id;
	}

}

function zfyk(obj) {

	var id = document.getElementById("htid").value;

	var mima = document.getElementById("mima").value;
	var mima2 = document.getElementById("mima2").value;
	zfyk_id = id;
	zfyk_mima = mima;
	if (mima == "") {
		mima = mima2;
		zfyk_mima = mima2;
	}
	var pytd = document.getElementById("pytd").value;

	if (mima == "") {
		if (pytd == "0") {
			alert("支付密码不能为空");
		}else{
			alert("交易密码不能为空");
		}
		return false;
	 }
   //add by xiakang for pingan started 2016/01/13
	 var ApprovalStatus1 = document.getElementById("ApprovalStatus1").value;
	 var ApprovalStatus2 = document.getElementById("ApprovalStatus2").value;
	 var ApprovalMode = document.getElementById("ApprovalMode").value;
	 if(ApprovalStatus2=="30" && ApprovalMode=="1"){
	     var shly = document.getElementById('shly').value;
	     
         var A = document.getElementById('agree').checked;
	     var B = document.getElementById('unagree').checked;
	     var isagree = "";
	     if(A ==false && B== false){
		     alert("请选择是否通过");return false;
	     }
		 if(B==true && shly==""){
	         alert("请填写备注原因");return false;
	     }
	 }
	 if(ApprovalMode!=""){
		if(ApprovalStatus1=="-1"){
		  alert("初次审核未完成，耐心等待");
		  return false;
		}else if((ApprovalStatus2!="30" && ApprovalStatus2!="2" && ApprovalStatus2!="0") && (ApprovalMode=="1" || ApprovalMode=="2")){
          alert("融资审核未完成，请您等待审批结果");
		  return false;
        }else if(ApprovalStatus2=="2" && ApprovalMode=="1"){
          alert("您已支付，请您耐心等待平安银行出账");
		  return false;
        }
	 }
   //add by xiakang for pingan ended 2016/01/13

	 //obj.style.display = "none";
//	 document.getElementById("wait").style.display = "block";
	 obj.onclick=function(){};
			obj.src="images/btn_zfyfk_gray.png";
			obj.onclick="";
		 document.getElementById("loading").style.display="";
		 var param1 = "action=GetUserState";
         var ajax = new Ajax("member.php",GetState3,param1);
	    
 }
 var zfyk_id='';
 var zfyk_mima='';
function GetState3(returnstr){
  var ApprovalStatus1 = document.getElementById("ApprovalStatus1").value;
  var ApprovalMode = document.getElementById("ApprovalMode").value;
     if(ApprovalMode!=""){
		 var shly = document.getElementById("shly").value;
         var A = document.getElementById('agree').checked;
         var B = document.getElementById('unagree').checked;
	     var isagree = "";
	     if(A ==true){
		     var isagree="0";
	     }
		 if(B ==true){
		     var isagree="1";
	     }
	 }
         var par=returnstr.split(";");
         if((parseInt(par[1])==1)||(parseInt(par[2])==1)){
		   if(ApprovalMode!=""){
			 var param = "action=zfyk&id="+zfyk_id+"&mima="+zfyk_mima+"&isagree="+isagree+"&ApprovalRemark2="+shly;
	         var ajax = new Ajax("member.php", setHtyk, param );
		   }else{
		     var param = "action=zfyk&id="+zfyk_id+"&mima="+zfyk_mima;
	         var ajax = new Ajax("member.php", setHtyk, param );
		   }
		 }
		 else{
		 alert("对不起，您没有操作权限");
		 document.getElementById("loading").style.display="none";
		 window.location.reload();
		 }
	        
	 }
//Added for quanxian sheding  by xk ended  2015/1/30
 function setHtyk(returnstr){
	 document.getElementById("loading").style.display="none";
 var id = document.getElementById("ddid").value;
 var pytd = document.getElementById("pytd").value;
 var str = returnstr;
 //add by xiakang for pingan started 2016/1/20
 var ApprovalMode = document.getElementById("ApprovalMode").value;
 if(ApprovalMode=="1"){
     var str=str.split("|");
	 if(str[0]=="1"){
	      alert(str[1]+"请继续点击支付。");
	 }
	 if(str[0]=="2"){
	      alert("支付成功，请等待平安出账");
	 }
	 if(str[0]=="3"){
	      //alert("选择拒绝，您已取消平安融资支付方式,请等待平安返回信息");
	      //alert("返回代码"+str[1]+"（"+str[2]+"）");
	      alert(str[2]);
	 }
     if(str[0]=="4"){
	      //alert("选择拒绝，发送信息给平安失败，请继续选择");
		  //alert("返回代码"+str[1]+"（"+str[2]+"）");
		  alert(str[2]);
	 }
	
	 location.reload() ;
 }
 //add by xiakang for pingan ended 2016/1/20

	if (str == "9") {
		if (pytd == "0") {
			alert("支付密码输入错误");
		}
		if (pytd == "1") {
			//Update by xiakang for zhifu started 2015/04/27
			//alert("交易密码输入错误");
			alert("支付密码输入错误");
			//Update by xiakang for zhifu ended 2015/04/27
		}
		location.reload();
	} else {
		if (str == "1") {
			alert("支付成功");
		}
		location.href = "bizorder.php?view=orderdetail&id=" + id;
	}
}

// 页面编辑数据
var inputsData;
var textareasData;
var selectsData;
// 记录下表单中的原始值
function initFileds() {
	var inputs = document.getElementsByTagName("input");
	var textareas = document.getElementsByTagName("textarea");
	var selects = document.getElementsByTagName("select");
	inputsData = new Array(inputs.length);
	for (var i = 0; i < inputs.length; i++) {
		inputsData[i] = inputs[i].value;
		if (inputs[i].type == "radio") {
			inputsData[i] = inputs[i].checked;
		}
	}
	textareasData = new Array(textareas.length);
	for (var i = 0; i < textareas.length; i++) {
		textareasData[i] = textareas[i].value;
	}
	selectsData = new Array(selects.length);
	for (var i = 0; i < selects.length; i++) {
		selectsData[i] = selects[i].value;
	}
}
/*
 * 判断表单中值是否被修改了
 * submitCommand 表单有改动时,执行的javascript代码
 */
function checkModification(submitCommand) {
	var inputs = document.getElementsByTagName("input");
	var textareas = document.getElementsByTagName("textarea");
	var selects = document.getElementsByTagName("select");
	var hasBeenChanged = false;
	for (var i = 0; i < inputs.length; i++) {
		if (inputs[i].type == "radio" && (inputs[i].checked != inputsData[i])) {
			hasBeenChanged = true;
			inputsData[i] = inputs[i].checked;
		}
		if (inputs[i].type != "radio" && inputsData[i] != inputs[i].value) {
			if (inputs[i].name != "actionType") {
				hasBeenChanged = true;
			}
			inputsData[i] = inputs[i].value;
		}
	}
	for (var i = 0; i < textareas.length; i++) {
		if (textareasData[i] != textareas[i].value) {
			hasBeenChanged = true;
			textareasData[i] = textareas[i].value;
		}
	}
	for (var i = 0; i < selects.length; i++) {
		if (selectsData[i] != selects[i].value) {
			hasBeenChanged = true;
			selectsData[i] = selects[i].value;
		}
	}
	if (hasBeenChanged && confirm("数据已经改变,是否保存？")) {
		eval(submitCommand);
	}
}

function checkradio(radio) {
	var rlist = document.getElementsByName(radio);
	var aa = "";
	for (var j = 0; j < rlist.length; j++) {
		if (rlist[j].checked) {
			aa = rlist[j].value;
		}
	}
	return aa;
}

function editdiv(div) {
	document.getElementById("show" + div).style.display = "none";
	document.getElementById(div + "div").style.display = "block";
}

function editmgdiv(div) {
	document.getElementById("show" + div).style.display = "none";
	document.getElementById(div + "div").style.display = "block";
	document.getElementById("showzy_notmatch").style.display = "none";
}
function closediv(div) {
	document.getElementById("show" + div).style.display = "block";
	document.getElementById(div + "div").style.display = "none";
}

function plzypostData() {

	if (document.zyform2.VarietyName.value == "") {
		alert("资源名称不能为空");
		return false;
	}

//if(document.zyform2.VarietyCode.value == ""){
//alert("品种不能为空");
//return false;
//}
//
//
//if(document.zyform2.MaterialCode.value == ""){
//alert("材质不能为空");
//return false;
//}
//if(document.zyform2.SpecCode.value == ""){
//alert("规格不能为空");
//return false;
//}
if(document.zyform2.SpecCode.value == ""){// && document.zyform2.hd.value == "" && document.zyform2.kd.value == ""&& document.zyform2.cd.value == ""
alert("规格不能为空");
return false;
}
var trade = $("input[name=TradeType]:checked").val();
var zytype = document.getElementById("SalesType2").value;
if((zytype=='2'|| zytype=='8') && trade=='2'){
}else{
if(document.zyform2.OriginCode.value == "" ){
//if(document.zyform2.OriginCode.value == ""){
alert("钢厂不能为空");
return false;
}
}
if(document.zyform2.QuantitySales.value == ""){

		alert("数量不能为空");
		return false;
	}

	var dj = "";
	//if(document.zyform2.IsStaMoney){
	//var rlistdj = document.getElementsByName("IsStaMoney");
	//
	//for(var m=0;m<rlistdj.length;m++)
	//{
	//if(rlistdj[m].checked){dj =  rlistdj[m].value;}
	//}
	//
	//}

	submitRequest(document.zyform2, "post", "text", outplzy, null); //使用自定义myProcess
}

//Added by quanjw for jinshuzhipin start 2015/2/15
function jinshu_plzypostData(type) {

	if (document.zyform2.VarietyName.value == "") {
		alert("资源名称不能为空");
		return false;
	}

	//if(document.zyform2.VarietyCode.value == ""){
	//alert("品种不能为空");
	//return false;
	//}
	//
	//

	if (document.zyform2.MaterialCode.value == "") {
		alert("材质不能为空");
		return false;
	}

	if (document.zyform2.SpecCode.value == "") { // && document.zyform2.hd.value == "" && document.zyform2.kd.value == ""&& document.zyform2.cd.value == ""
		alert("规格不能为空");
		return false;
	}
var trade = $("input[name=TradeType]:checked").val();
var zytype = document.getElementById("SalesType2").value;
if((zytype=='2'|| zytype=='8') && trade=='2'){
}else{
if(document.zyform2.OriginCode.value == "" ){
//if(document.zyform2.OriginCode.value == ""){
		alert("产地不能为空");
		return false;
}
	}
	if (document.zyform2.QuantitySales.value == "") {
		alert("数量不能为空");
		return false;
	}

    //update by zfy started 2019/09/26 增加焊网
    if ("undefined" == typeof type) {
        type = 'jinshu';
    }
    if (type=='hw'){
        if (document.zyform2.yongtu.value == "") {
            alert("抗拉不能为空");
            return false;
        }

        if (document.zyform2.strength.value == "") {
            alert("屈服不能为空");
            return false;
        }
    }else {
        if (document.zyform2.yongtu.value == "") {
            alert("用途不能为空");
            return false;
        }

        if (document.zyform2.strength.value == "") {
            alert("强度不能为空");
            return false;
        }
    }
    //update by zfy ended 2019/09/26 增加焊网

	var dj = "";
	//if(document.zyform2.IsStaMoney){
	//var rlistdj = document.getElementsByName("IsStaMoney");
	//
	//for(var m=0;m<rlistdj.length;m++)
	//{
	//if(rlistdj[m].checked){dj =  rlistdj[m].value;}
	//}
	//
	//}

	submitRequest(document.zyform2, "post", "text", outplzy, null); //使用自定义myProcess
}
//Added by quanjw for jinshuzhipin end 2015/2/15

//Added by quanjw for meijiao start 2015/1/12
function meitan_plzypostData() {

	if (document.zyform2.VarietyName.value == "") {
		alert("资源名称不能为空");
		return false;
	}

	if (document.zyform2.SpecCode.value == "") {
		alert("灰分不能为空");
		return false;
	}
	if (document.zyform2.MaterialCode.value == "") {
		alert("挥发分不能为空");
		return false;
	}
	if (document.zyform2.cd.value == "") {
		alert("全硫分不能为空");
		return false;
	}
	if (document.zyform2.QuantitySales.value == "") {
		alert("数量不能为空");
		return false;
	}
var trade = $("input[name=TradeType]:checked").val();
var zytype = document.getElementById("SalesType2").value;
if((zytype=='2'|| zytype=='8') && trade=='2'){
}else{
if(document.zyform2.OriginCode.value == "" ){
//if(document.zyform2.OriginCode.value == ""){
alert("厂家不能为空");
return false;
}
}

	var dj = "";

	submitRequest(document.zyform2, "post", "text", outplzy, null); //使用自定义myProcess
}
//Added by quanjw for meijiao end 2015/1/12

//Added by quanjw for meijaio start 2015/2/5
function jiaotan_plzypostData() {

	if (document.zyform2.VarietyName.value == "") {
		alert("资源名称不能为空");
		return false;
	}

	if (document.zyform2.SpecCode.value == "") {
		alert("灰分不能为空");
		return false;
	}
	if (document.zyform2.MaterialCode.value == "") {
		alert("全硫分不能为空");
		return false;
	}
	if (document.zyform2.cd.value == "") {
		alert("CSR不能为空");
		return false;
	}
	if (document.zyform2.QuantitySales.value == "") {
		alert("数量不能为空");
		return false;
	}
var trade = $("input[name=TradeType]:checked").val();
var zytype = document.getElementById("SalesType2").value;
if((zytype=='2'|| zytype=='8') && trade=='2'){
}else{
if(document.zyform2.OriginCode.value == "" ){
//if(document.zyform2.OriginCode.value == ""){
		alert("厂家不能为空");
		return false;
	}
	}

	var dj = "";

	submitRequest(document.zyform2, "post", "text", outplzy, null); //使用自定义myProcess
}
//Added by quanjw for meijiao end 2015/2/5


function tks_plzypostData() {

	if (document.zyform2.VarietyName.value == "") {
		alert("资源名称不能为空");
		return false;
	}

	if (document.zyform2.tks_fe.value == "") {
		alert("Fe不能为空");
		return false;
	}
    
    if ( ( document.zyform2.tks_fe.value.indexOf("%") > -1) || 
         ( document.zyform2.tks_si.value.indexOf("%") > -1) || 
         ( document.zyform2.tks_al.value.indexOf("%") > -1) || 
         ( document.zyform2.attr_b.value.indexOf("%") > -1) || 
         ( document.zyform2.attr_c.value.indexOf("%") > -1) || 
         ( document.zyform2.attr_d.value.indexOf("%") > -1) 
         ) {
		alert("Fe,SiO2,AL2O3,P,S,H2O,不能含%");
		return false;
	}
var trade = $("input[name=TradeType]:checked").val();
var zytype = document.getElementById("SalesType2").value;
if((zytype=='2'|| zytype=='8') && trade=='2'){
}else{
if(document.zyform2.OriginCode.value == ""){
	//if (document.zyform2.OriginCode.value == "") {
		alert("产地不能为空");
		return false;
	}
	}
	if (document.zyform2.QuantitySales.value == "") {
		alert("数量不能为空");
		return false;
	}

	if( document.zyform2.tks_si.value == "" || document.zyform2.tks_al.value == "" || document.zyform2.attr_b.value == "" || document.zyform2.attr_c.value == "" ){
				
		if( ! window.confirm('SiO2、AL2O3、P、S有空值，您确定要继续吗？')){
			return false;
		}
	}
	var dj = "";

	submitRequest(document.zyform2, "post", "text", outplzy, null); //使用自定义myProcess
}

function outplzy(count) {
	if ($("#aedit").val() != "") {
		$("#dt" + $("#aedit").val()).html(count);
		//$('#aedit').val(data.id);
		alert("修改成功");
	} else {
		$("#detailtablist tbody").append(count);
	}
	closediv("zy");
	//var tmp = count.split("|-|");
	//location.href="member.php?view=jymanage&id="+tmp[31]+"&jg=1";
}

//add by xiakang for pingan started 2015/12/10
function rzsh(){  
   var financetype=document.getElementById("financetype").value;
   var Appamount=document.getElementById("Appamount").value;
   var firstmoney=document.getElementById("firstmoney").value;
   var applyday=document.getElementById("applyday").value;
   var Notes=document.getElementById("Notes").value;
   if(financetype==""){
      alert("请选择融资类型");
	  return false;
   }
   if(Appamount==""){
      alert("请填写融资申请金额");
	  return false;
   }
   if(financetype=="ECDG"){
     if(firstmoney==""){
        alert("请填写保证金");
	    return false;
	 }
   }
   if(applyday==""){
      alert("请填写融资期限");
	  return false;
   }
   var param = "action=GetUserState";
   var ajax = new Ajax("member.php",rzshGetState,param);
 }
function rzshGetState(returnstr){
        var par=returnstr.split(";");
		var financetype=document.getElementById("financetype").value;
		var Appamount=document.getElementById("Appamount").value;
		var firstmoney=document.getElementById("firstmoney").value;
		var applyday=document.getElementById("applyday").value;
		var Notes=document.getElementById("Notes").value;
		var ddid = document.getElementById("DDid").value;
		var ApprovalMode = document.getElementById("ApprovalMode").value;
		var kymoney=document.getElementById("kyed").innerHTML;
	
         if((parseInt(par[1])==1)||(parseInt(par[2])==1)){
		   window.location.href="bizorder.php?action=dorzsh&id="+ddid+
			                    "&ApprovalMode="+ApprovalMode+
		                        "&financetype="+financetype+
			                    "&kymoney="+kymoney+"&Appamount="+Appamount+
			                    "&firstmoney="+firstmoney+"&applyday="+applyday+
			                    "&Notes="+Notes;
	   }
	   else{
		alert("对不起，您没有操作权限");
		 document.getElementById("loading").style.display="none";
		 window.location.reload();
	  }
} 

function tjrzsh(obj){
    
	var shly = document.getElementById('shly').value;
    var A = document.getElementById('agree').checked;
	var B = document.getElementById('unagree').checked;
	var isagree = "";
	if(A ==false && B== false){
		  alert("请选择是否同意");return false;
	}else{
	   if(A ==true){
	      var isagree="0";
	   }else{
	      var isagree="1";
	   }
	}
	var ddid = document.getElementById("DDid").value;

    obj.onclick=function(){};
		obj.src="images/btn_rzsh_gray.png";
		obj.onclick="";
	document.getElementById("loading").style.display="";
    document.getElementById("agree").disabled=true;
    document.getElementById("unagree").disabled=true;
	document.getElementById("shly").disabled=true;
    
    var param = "action=tjrzsh&id="+ddid+"&ApprovalStatus1="+isagree+"&ApprovalRemark1="+shly;
    var ajax = new Ajax("bizorder.php",tjrzshGetState,param);

}

function tjrzshGetState(returnstr){
	var ddid = document.getElementById("DDid").value;
	var returnstr=returnstr.split("|");
	document.getElementById("loading").style.display="none";
	if(returnstr[0]=="0"){
        alert("平安融资初审完成");
		location.href="bizorder.php?view=orderdetail&id="+ddid;
	}
	if(returnstr[0]=="1"){
	    alert("平安融资初审完成");
		location.href="bizorder.php?view=orderdetail&id="+ddid;
	}
	if(returnstr[0]=="-1"){
	    alert("融资审核失败，出错码："+returnstr[1]+"，请重新点击审核");
		location.href="bizorder.php?view=orderdetail2&id="+ddid;
	}
	if(returnstr[0]=="9"){
        alert("您已经审核过了，不能重复提交");
		location.href="bizorder.php?view=orderdetail&id="+ddid;
	}
}
//add by xiakang for pingan ended 2015/12/10

function tjmx(){
	editdiv("zy");
	//	$("select[name=VarietyCode2]").get(0).selectedIndex = 0;
	//	$("select[name=VarietyCode]").empty();
	var $textarry = $('#zydiv input[type="text"]');
	$textarry.each(function (index) {
		$textarry.eq(index).val("");
	});
	$("input[name='hidd_attrid']").val("");
	$("#div_Result").html("");
	$("input[name='hidd_attrid2']").val("");
	$("#div_Result2").html("");
	$("input[name='hidd_attrid3']").val("");
	$("#div_Result3").html("");

	$("input[name='aedit']").val("");

	//$("input[name=AssureGuarpayType]:eq(0)").attr("checked",'checked');
	//$("input[name=bzjflag]:eq(0)").attr("checked",'checked');
	//$("input[name=Delivery]:eq(0)").attr("checked",'checked');

	//$("input[name=IsStaMoney]:eq(0)").attr("checked",'checked');

	//setdj(1);

	//$("[name='IsMaxMoney2']").removeAttr("checked");

}

function editplzy(id) {
	jQuery.ajax({
		url : 'member.php?action=ajaxplzyedit',
		data : "id=" + id + "&time=" + new Date().getTime(), // 从表单中获取数据
		type : 'POST', // 设置请求类型为 ‘POST’，默认为 ‘GET’
		dataType : "json",
		success : function (data) {

			$("input[name='aedit']").val(id);
			$("input[name='hidd_attrid']").val("");
			$("input[name='hidd_attrid2']").val("");
			$("input[name='hidd_attrid3']").val("");


			
editdiv("zy");
				
				
				$("input[name='SalesType']").val(data.SalesType);
				$("input[name='StoreCityCode']").val(data.StoreCityCode);
				$("input[name='VarietyName']").val(data.VarietyName);
		//		$("input[name='VarietyCode2']").val(data.biglei);
		//		$("input[name='VarietyCode']").val(data.VarietyCode);
				//Added
				$("input[name='cd']").val(data.cd);
				//Added

			$("input[name='MaterialCode']").val(data.MaterialCode);
			$("input[name='SpecCode']").val(data.SpecCode);
			$("input[name='OriginCode']").val(data.OriginCode);

			$("select[name=SalesUnit]").val(data.SalesUnit);
			//		getpz2(data.biglei,data.VarietyCode);

			$("input[name=SalesMinPrice]").val(data.SalesMinPrice);
			$("input[name='QuantitySales']").val(data.QuantitySales);
			$("input[name='WeightPerOne']").val(data.WeightPerOne);
			//$("input[name='PerNumber']").val(data.PerNumber);
			$("input[name='MfgDate']").val(data.MfgDate);

			$("input[name='QuantityMin']").val(data.QuantityMin);
			$("input[name='QuantitySalesed']").val(data.QuantitySalesed);
			//	$("input[name='SalesStartDate']").val(data.SalesStartDate);

			//	$("input[name='SalesEndDate']").val(data.SalesEndDate);
			//	$("input[name='LadderPrice']").val(data.LadderPrice);
			//$("select[name='bzjflag']").val(data.bzjflag);

			//$("select[name='Delivery']").val(data.Delivery);


			$("input[name='jhck']").val(data.jhck);
			$("input[name='bmzk']").val(data.bmzk);

			$("input[name='hd']").val(data.hd);
			$("input[name='kd']").val(data.kd);
			$("input[name='cd']").val(data.cd);
			$("input[name='ResourceNum']").val(data.ResourceNum);
			$("input[name='cangku']").val(data.cangku);
			$("input[name='kuweihao']").val(data.kuweihao);

			$("input[name='cxname']").val(data.cxname);

			$("input[name='bzqk']").val(data.bzqk);
			$("input[name='rclzk']").val(data.rclzk);
			$("input[name='bz']").val(data.bz);
			$("input[name='Standard']").val(data.Standard);
			$("input[name='PickUpDate']").val(data.PickUpDate);
			$("input[name='xyjgtypes']").val(data.xyjgtypes);
			//Added by quanjw for meijiao start 2015/1/13
			$("input[name='attr_a']").val(data.attr_a);
			$("input[name='attr_b']").val(data.attr_b);
			$("input[name='attr_c']").val(data.attr_c);
			//Added by quanjw for meijiao end 2015/1/13
			//Added by quanjw for meijiao start 2015/2/2
			$("input[name='attr_d']").val(data.attr_d);
			$("input[name='attr_e']").val(data.attr_e);
			$("input[name='attr_f']").val(data.attr_f);
			$("input[name='validDate']").val(data.validDate);
			//Added by quanjw for meijiao end 2015/2/2

			//Added by quanjw for jinshuzhipin start 2015/2/15
			$("input[name='yongtu']").val(data.yongtu);
			$("input[name='strength']").val(data.strength);
			$("input[name='xincengWeight']").val(data.xincengWeight);
			//Added by quanjw for jinshuzhipin end 2015/2/15
			
			//Added by quanjw for tks start 2016/08/16
			$("input[name='tks_fe']").val(data.tks_fe);
			$("input[name='tks_si']").val(data.tks_si);
			$("input[name='tks_al']").val(data.tks_al);
			//Added by quanjw for tks end 2016/08/16

			//	$("input[name='AssureGuarpayMoney']").val(data.AssureGuarpayMoney);

			//	$("input[type=radio][name=AssureGuarpayType][value="+data.AssureGuarpayType+"]").attr("checked","checked");
			$("input[type=radio][name=bzjflag][value=" + data.bzjflag + "]").attr("checked", "checked");

			$("input[type=radio][name=Delivery][value=" + data.Delivery + "]").attr("checked", "checked");

			//		$("input[type=radio][name=IsStaMoney][value="+data.IsStaMoney+"]").attr("checked","checked");

			$("input[type=radio][name=SalesType][value=" + data.SalesType + "]").attr("checked", "checked");
			$("input[type=radio][name=Yijia][value=" + data.Yijia + "]").attr("checked", "checked");
			$("input[type=radio][name=TradeType][value=" + data.TradeType + "]").attr("checked", "checked");
			$("input[name='LyMoney']").val(data.LyMoney);
			//	$("input[name='IsStaMoney']").val(data.IsStaMoney);
			if (data.IsMaxMoney == "1") {
				//	 $("[name='IsMaxMoney2']").attr("checked",'true');
			}

			//	$("input[name='IsMaxMoney']").val(data.IsMaxMoney);

			//$("#div_Result").html(data.attrstr1);
			//$("#div_Result2").html(data.attrstr2);
			//$("#div_Result3").html(data.attrstr3);


		}
	});
}
//add by zfy started 马钢资源库存数量发布
//修改
function editmgkcsl(id,type) {
	jQuery.ajax({
		url : 'mg_resource.php?action=ajaxkcsledit',
		data : "id=" + id +"&type="+type+"&time=" + new Date().getTime(), // 从表单中获取数据
		type : 'POST', // 设置请求类型为 ‘POST’，默认为 ‘GET’
		dataType : "json",
		success : function (data) {
			if (data.isstart==0) {

                $("input[name='aedit']").val(id);
                $("input[name='hidd_attrid']").val("");
                $("input[name='hidd_attrid2']").val("");
                $("input[name='hidd_attrid3']").val("");

                editmgdiv("zy");
                $("input[name='wuliaoNo']").val(data.wuliaoNo);
                $("input[name='varietyName']").val(data.varietyName);
                $("input[name='Material']").val(data.Material);
                $("input[name='Spec']").val(data.Spec);
                $("input[name='Num']").val(data.Num);
                $("input[name='Price']").val(data.Price);
                $("input[name='Factory']").val(data.Factory);
                $("input[name='cangku']").val(data.cangku);
                $("#id").html(data.id);
            }else{
				alert("开盘状态无法修改！");
			}

		}
	});
}
//删除
function delmgkcsl(id,type) {
	if (confirm('请确认是否删除！')) {
		jQuery.ajax({
			url : 'mg_resource.php?action=ajaxdelmgkcsl',
			data : "id=" + id +"&type="+type+"&time=" + new Date().getTime(), // 从表单中获取数据
			type : 'POST', // 设置请求类型为 ‘POST’，默认为 ‘GET’
			dataType : "json",
			success : function (data) {
				if (data.data == 1) {
					//$("#dt"+id).hide();
					$("#dt" + id).remove();
					alert(data.datainfo);
				} else {
					alert("删除失败");
				}
			}
		});
	}
}
//保存明细
function mgkcslpostData(type) {
    if (document.zyform2.varietyName.value == "") {
        alert("品种名称不能为空");
        return false;
    }
    if (document.zyform2.Material.value == "") {
        alert("材质不能为空");
        return false;
    }
    if (document.zyform2.Spec.value == "") {
        alert("规格不能为空");
        return false;
    }
	if(type=='jgwh'){
	    if (document.zyform2.Price.value == "") {
		    alert("价格不能为空");
			return false;
		}
	}else{
		if (document.zyform2.Num.value == "") {
			alert("数量不能为空");
			return false;
		}
	}
	submitRequest(document.zyform2, "post", "text", outplzy, null); //使用自定义myProcess
}

var del_id = '';
var del_type = '';
function delmgzy(id,type) {
	// var id = document.getElementById("zyid").value;
	var param1 = "action=GetUserState";
	del_id = id;
	del_type = type;
	var ajax = new Ajax("mg_resource.php", Del_GetStatemgzy, param1);
}
function Del_GetStatemgzy(returnstr) {
	//var par = returnstr.split(";");
	//var pos = par[0].indexOf("1");
	//if ((parseInt(par[1]) == 1) || pos >= 0) {
		if (window.confirm("是否确认删除此条信息?")) {
			var param = "action=ajaxdelmgkcsl&id=" + del_id+"&type="+del_type+"&mode=2";
			var ajax = new Ajax("mg_resource.php", setdelmgzy, param);
		}
	//} else {
		//alert("对不起，您没有操作权限");
		//window.location.reload();
	//}
}
// Added for quanxian sheding(leftmenu)  by hxy ended  2015/1/30

function setdelmgzy(str) {
	//var str = returnstr;
	if (str == "1") {
		alert("删除成功");
		//location.href="mg_resource.php?view=newzylist";
	} else if(str == "2") {
		alert("开盘状态下无法删除资源！"); //location.href="member.php?view=newzylist";
	} else {
		alert("删除失败"); //location.href="member.php?view=newzylist";
	}
	location.reload();
}
//竞价资源删除
function delmgjjzy(id) {
		if (window.confirm("是否确认删除打包资源信息?")) {
			var param = "action=ajaxdelmgjjzy&id=" + id;
			var ajax = new Ajax("mg_resource.php", setdelmgjjzy, param);
		}
}

function setdelmgjjzy(str) {
	//var str = returnstr;
	if (str == "1") {
		alert("删除成功");
		location.href="mg_resource.php?view=jingjia_list";
	}else if(str == "2") {
        alert("已审核资源无法删除！");
    }else{
	    alert("删除失败");
	}
	//location.reload();
}

//add by zfy ended 马钢资源库存数量发布
function getpz2(val1, val2) {
	$("#VarietyCode").empty();
	jQuery.ajax({
		url : 'member.php',
		data : "action=ajaxgetpz&id=" + val1 + "&time=" + new Date().getTime(), // 从表单中获取数据
		type : 'POST', // 设置请求类型为 ‘POST’，默认为 ‘GET’
		dataType : "json",
		success : function (data) {
			$.each(data, function (i, item) {
				$("<option></option>")
				.val(item["VarietyCode"])
				.text(item["VarietyName"])
				.appendTo($("#VarietyCode"));
			});

			$("#VarietyCode").val(val2);
		}
	});
}

function deleplzy(id) {
	if (confirm('请确认是否删除！')) {
		jQuery.ajax({
			url : 'member.php?action=ajaxdeleplzy',
			data : "id=" + id + "&time=" + new Date().getTime(), // 从表单中获取数据
			type : 'POST', // 设置请求类型为 ‘POST’，默认为 ‘GET’
			dataType : "json",
			success : function (data) {
				if (data.data == 1) {
					//$("#dt"+id).hide();
					$("#dt" + id).remove();
				} else {
					alert("删除失败");
				}
			}
		});
	}
}

function pljgpostData() {

	/* if(document.jgform.PickUpCity.value == ""){
	alert("交割城市不能为空");
	return false;
	}*/
	var array = document.getElementsByName("plzyid");
	var id = "";

	for (var i = 0; i < array.length; i++) {
		id += array[i].value + ",";
	}
	if (document.jgform.IsTalk.checked == false) {
		document.jgform.IsTalk.value = 0;
	}
	document.jgform.flag.value = id;

	submitRequest(document.jgform, "post", "text", pljgoutWrite, null); //使用自定义myProcess
}

//AJAX出现结果，处理变化
function pljgoutWrite(count) {
	//alert(count);
	var tmp = count.split("|-|");
	document.getElementById("jgtype").innerHTML = tmp[0];
	document.getElementById("jgdate").innerHTML = tmp[1];
	document.getElementById("jgcity").innerHTML = tmp[2];
	document.getElementById("jgadress").innerHTML = tmp[3];
	document.getElementById("jgfkfs").innerHTML = tmp[5];
	document.getElementById("jgDelivery").innerHTML = tmp[6];
	document.getElementById("jgjhck").innerHTML = tmp[7];
	document.getElementById("jgStoreCity").innerHTML = tmp[8];
	document.getElementById("LyMoney").innerHTML = tmp[9] + "%";

	if (tmp[10] == 1) {
		document.getElementById("IsTalk").innerHTML = '<input type="checkbox" value="1" name="IsTalk" disabled checked >付款及交割可洽谈';
	} else {
		document.getElementById("IsTalk").innerHTML = '<input type="checkbox" value="1" name="IsTalk" disabled >付款及交割可洽谈';
	}

	document.forms['jgform'].flag.value = tmp[4];
	document.getElementById("zyflag").value = tmp[4];
	document.getElementById("zyid").value = tmp[4];

	document.getElementById("jgdiv").style.display = "none"; //隐藏
	// var div = document.getElementById( "show" );
	document.getElementById("show").style.display = "block";
	//  div.innerHTML =ht;

	//显示联系人相关信息
	//document.getElementById("showfh").style.display="none";
	//document.getElementById("fhdiv").style.display="block";

	////显示附件相关信息
	//document.getElementById("fjdiv").style.display="block";
	//document.getElementById("showfj").style.display="none";

}

function pladduser() {
	if (document.userform22.ContactMan.value == "") {
		alert("联系人不能为空");
		return false;
	}
	if (document.userform22.ContactMobile.value == "" && document.userform22.ContactPhone.value == "") {
		alert("固定电话或者手机其中一项不能为空");
		return false;
	}
	var array = document.getElementsByName("plzyid");
	var id = "";

	for (var i = 0; i < array.length; i++) {
		id += array[i].value + ",";
	}

	document.userform22.usid.value = id;
	submitRequest(document.userform22, "post", "text", pladduseroutWrite, null); //使用自定义myProcess
}

function pladduseroutWrite(count) {

	var tmp = count.split("|-|");

	document.getElementById("ContactMan2").innerHTML = tmp[0];
	document.getElementById("ContactPhone2").innerHTML = tmp[1];
	document.getElementById("ContactMobile2").innerHTML = tmp[2];
	document.getElementById("ContactFax2").innerHTML = tmp[3];
	document.getElementById("ContactEmail2").innerHTML = tmp[4];
	document.getElementById("QQNum2").innerHTML = tmp[5];

	document.getElementById("showfh").style.display = "block";
	document.getElementById("fhdiv").style.display = "none";

	document.zyform2.pllxr.value = tmp[6];

	//显示附件相关信息
	//document.getElementById("fjdiv").style.display="block";
	//document.getElementById("showfj").style.display="none";

}

function zyjbpostData() {

	var zytype = document.zyform.salety.value;
	var typedif = document.zyform.typedif.value;
	//var flag = document.zyform.aedit.value;

	var myDate = new Date();
	var sysdate = myDate.format('yyyy-MM-dd hh:mm:ss');

	strDate1 = document.zyform.SalesEndDate.value;
	strDate2 = document.zyform.SalesStartDate.value;
	endDate = Date.parse(strDate1.replace(/\-/g, "/"));
	startDate = Date.parse(strDate2.replace(/\-/g, "/"));
	var sjc = (endDate - startDate) / (60 * 60 * 1000);

	var dj = "";

	if (document.zyform.SalesStartDate.value == "") {
		alert("交易开始日期不能为空");
		return false;
	}
	if (document.zyform.SalesEndDate.value == "") {
		alert("交易结束日期不能为空");
		return false;
	}
	if (document.zyform.SalesEndDate.value < document.zyform.SalesStartDate.value) {
		alert("交易结束日期不能早于开始日期");
		return false;
	}
	if (document.zyform.SalesEndDate.value < sysdate) {
		alert("交易结束日期已过期，请修改销售日期");
		return false;
	}
	if (sjc < 2) {
		alert("交易时间必须大于2小时，请修改销售日期");
		return false;
	}
	if (sjc > 720) {
		alert("资源有效时间应在30天以内");
		return false;
	}
	
	//定向采购检查
	//Added by quanjw start at 2015/7/24
	if (document.zyform.Isdxcg) {
		if (document.zyform.Isdxcg.value == 1 && document.zyform.dxcggc.value == 0) {
			alert("定向采购必须选择定向采购厂家");
			document.zyform.dxcggc.focus();
			return false;
		}
	}
	
	var array = document.getElementsByName("plzyid");
	var ids = "";

	for (var i = 0; i < array.length; i++) {
		ids += array[i].value + ",";
	}

	document.zyform.jbplid.value = ids;

	submitRequest(document.zyform, "post", "text", outplzy2, null); //使用自定义myProcess
}

function outplzy2(data) {
	if(data=='F'){
		alert("竞价结束日期应大于开始日期4小时以上");exit;
	}
	if(document.zyform.zyflag.value > 0){
	closediv("jbzy");

		data = eval('(' + data + ')');

		$("#OriginCode").html(data.OriginCode);

		$("#SalesUnit").html(data.SalesUnit);

		//$("#Delivery").html(data.Delivery);

		$("#Standard").html(data.Standard);

		$("#SalesMinPrice").html(data.SalesMinPrice);
		$("#QuantitySales").html(data.QuantitySales);
		$("#WeightPerOne").html(data.WeightPerOne);
		$("#PerNumber").html(data.PerNumber);
		$("#MfgDate").html(data.MfgDate);

		$("#QuantityMin").html(data.QuantityMin);
		$("#QuantitySalesed").html(data.QuantitySalesed);
		$("#SalesStartDate").html(data.SalesStartDate);

		$("#SalesEndDate").html(data.SalesEndDate);
		//	$("#LadderPrice").html(data.LadderPrice);


		//$("#jhck").html(data.jhck);
		$("#bmzk").html(data.bmzk);

		$("#bzqk").html(data.bzqk);
		$("#rclzk").html(data.rclzk);

		$("#xyjgtypes").html(data.xyjgtypes);
		$("#EmpowerType").html(data.EmpowerType);

		//	$("#AssureGuarpayMoney").html(data.AssureGuarpayMoney);

		//	$("#AssureGuarpayType2").html(data.AssureGuarpayType);
		//	$("#bzjflag2").html(data.bzjflag);


		/*$("#bzjflag").html(data.bzjflag);

		$("#LyMoney").html(data.LyMoney);
		//alert(data.IsTalk);

		var qt="<input type=\"checkbox\"  disabled>付款及交割可洽谈";
		if(data.IsTalk==1){
		qt="<input type=\"checkbox\" checked disabled>付款及交割可洽谈";
		}

		$("#qt").html(qt);*/

		var Isdxcg = "<input type=\"checkbox\"  disabled>是定向采购";
		if (data.Isdxcg == 1) {
			Isdxcg = "<input type=\"checkbox\" checked disabled>是定向采购";
		}

		$("#Isdxcgss").html(Isdxcg);

		var Isgcwlht = "<input type=\"checkbox\"  disabled>有工程物流供应合同";
		if (data.Isgcwlht == 1) {
			Isgcwlht = "<input type=\"checkbox\" checked disabled>有工程物流供应合同";
		}

		$("#Isgcwlhtss").html(Isgcwlht);

		$("#dxcggcss").html(data.dxcggc);
		$("#gcwlhtss").html(data.gcwlht);
		$("#ResourceNum").html(data.ResourceNum);
				$("#FuKuanXingShi2").html(data.FuKuanXingShi);
				$("#KaiPiaoFangShi").html(data.KaiPiaoFangShi);
				$("#JiLiangFangShi").html(data.JiLiangFangShi);
				document.getElementById('jjqr0').checked=false;
				document.getElementById('jjqr1').checked=false;
				document.getElementById('jjqr'+data.jjqr).checked=true;

		//$("#IsStaMoney").html(data.IsStaMoney);


		//$("#IsMaxMoney").html(data.IsMaxMoney);
		//$("#EmpowerType").html(data.EmpowerType);


	} else {
		if (document.zyform.tourl.value == "excelzy")
			location.href = "member.php?view=excelzy&id=" + data;
		else
			location.href = "member.php?view=plzy&id=" + data;
	}

	//if($("#aedit").val()!=""){
	//$("#dt"+$("#aedit").val()).html(count);

	//alert("修改成功");
	//}else{
	//$("#detailtablist tbody").append(count);
	//}
	//closediv("zy");

}

function editjg2() {

	//$(document).ready(function() {$("input[name=PickUpType]:eq(0)").attr("checked",'checked'); });

	document.getElementById("show").style.display = "none"; //隐藏
	document.getElementById("jgdiv").style.display = "block"; //隐藏

}

function ctzyjbpostData() {

	/*var SCDate=document.getElementById("MfgDate2").value;

	SCDate=SCDate+" 00:00:00";

	var FBDate=document.getElementById("CreateDate").innerHTML;

	var SCDate = new Date(SCDate.replace("-", "/").replace("-", "/"));
	var FBDate = new Date(FBDate.replace("-", "/").replace("-", "/"));
	if(SCDate>FBDate){
	alert("生产日期不能大于发布日期,请重新填写！");
	return fales;
	}
	 */
	var zytype = document.zyform.salety.value;
	var typedif = document.zyform.typedif.value;
	//var flag = document.zyform.aedit.value;

	var myDate = new Date();
	var sysdate = myDate.format('yyyy-MM-dd hh:mm:ss');

	strDate1 = document.zyform.SalesEndDate.value;
	strDate2 = document.zyform.SalesStartDate.value;
	endDate = Date.parse(strDate1.replace(/\-/g, "/"));
	startDate = Date.parse(strDate2.replace(/\-/g, "/"));
	var sjc = (endDate - startDate) / (60 * 60 * 1000);

	/*if(document.zyform.StoreCityCode.value == "" && document.zyform.StoreCity.value == ""){
	alert("所在城市不能为空");
	return false;
	}*/

	var dj = "";
	//if(document.zyform.IsStaMoney){
	//var rlistdj = document.getElementsByName("IsStaMoney");
	//
	//for(var m=0;m<rlistdj.length;m++)
	//{
	//if(rlistdj[m].checked){dj =  rlistdj[m].value;}
	//}
	//
	//}

	/*if(typedif == "2" || zytype =="1"){
	if(document.zyform.jhck.value == ""){
	alert("交货仓库不能为空");
	return false;
	}
	}*/

	if (document.zyform.SalesStartDate.value == "") {
		alert("交易开始日期不能为空");
		return false;
	}
	if (document.zyform.SalesEndDate.value == "") {
		alert("交易结束日期不能为空");
		return false;
	}

	if (document.zyform.SalesEndDate.value < sysdate) {
		alert("交易结束日期已过期，请修改销售日期");
		return false;
	}
	if (sjc < 2) {
		alert("交易时间必须大于2小时，请修改销售日期");
		return false;
	}
	if (sjc > 720) {
		alert("资源有效时间应在30天以内");
		return false;
	}
	if (document.zyform.SalesEndDate.value < document.zyform.SalesStartDate.value) {
		alert("交易结束日期不能早于开始日期");
		return false;
	}
	
	//定向采购检查
	//Added by quanjw start at 2015/7/24
	if (document.zyform.Isdxcg) {
		if (document.zyform.Isdxcg.value == 1 && document.zyform.dxcggc.value == 0) {
			alert("定向采购必须选择定向采购厂家");
			document.zyform.dxcggc.focus();
			return false;
		}
	}

	var array = document.getElementsByName("plzyid");
	var ids = "";

	for (var i = 0; i < array.length; i++) {
		ids += array[i].value + ",";
	}

	document.zyform.jbplid.value = ids;

	submitRequest(document.zyform, "post", "text", outplzytc, null); //使用自定义myProcess
}

function outplzytc(data) {

//document.write(data);
		//alert(data);
	if(data=='F'){
		alert("竞价结束日期应大于开始日期4小时以上");exit;
	}
	if(document.zyform.zyflag.value > 0){
	closediv("jbzy");

		data = eval('(' + data + ')');

		$("#StoreCityCode").html(data.StoreCityCode);

		$("#OriginCode").html(data.OriginCode);

		$("#SalesUnit").html(data.SalesUnit);

		$("#Delivery").html(data.Delivery);

		$("#Standard").html(data.Standard);

		$("#SalesMinPrice").html(data.SalesMinPrice);
		$("#QuantitySales").html(data.QuantitySales);
		$("#WeightPerOne").html(data.WeightPerOne);
		$("#PerNumber").html(data.PerNumber);
		$("#MfgDate").html(data.MfgDate);

		$("#QuantityMin").html(data.QuantityMin);
		$("#QuantitySalesed").html(data.QuantitySalesed);
		$("#SalesStartDate").html(data.SalesStartDate);

		$("#SalesEndDate").html(data.SalesEndDate);
		//		$("#LadderPrice").html(data.LadderPrice);

		$("#EmpowerType").html(data.EmpowerType);

		$("#jhck").html(data.jhck);
		$("#bmzk").html(data.bmzk);

		$("#bzqk").html(data.bzqk);
		$("#rclzk").html(data.rclzk);

		$("#xyjgtypes").html(data.xyjgtypes);

		//	$("#AssureGuarpayMoney").html(data.AssureGuarpayMoney);

		//	$("#AssureGuarpayType2").html(data.AssureGuarpayType);
		$("#bzjflag2").html(data.bzjflag);

		$("#fkfs").html(data.fkfs);
		$("#dbDesc").html(data.dbDesc);
		$("#Yijia").html(data.Yijia);

		/*		$("#bzjflag").html(data.bzjflag);


		$("#LyMoney").html(data.LyMoney);
		//	$("#IsStaMoney").html(data.IsStaMoney);
		var qt="<input type=\"checkbox\"  disabled>付款及交割可洽谈";
		if(data.IsTalk==1){
		qt="<input type=\"checkbox\" checked disabled>付款及交割可洽谈";
		}

		$("#qt").html(qt);*/

		var Isdxcg = "<input type=\"checkbox\"  disabled>是定向采购";
		if (data.Isdxcg == 1) {
			Isdxcg = "<input type=\"checkbox\" checked disabled>是定向采购";
		}

		$("#Isdxcgss").html(Isdxcg);

		var Isgcwlht = "<input type=\"checkbox\"  disabled>有工程物流供应合同";
		if (data.Isgcwlht == 1) {
			Isgcwlht = "<input type=\"checkbox\" checked disabled>有工程物流供应合同";
		}

		$("#Isgcwlhtss").html(Isgcwlht);

		$("#dxcggcss").html(data.dxcggc);
		$("#gcwlhtss").html(data.gcwlht);

		//$("#IsMaxMoney").html(data.IsMaxMoney);
		$("#jjtype").html(data.jjtype);
				$("#FuKuanXingShi2").html(data.FuKuanXingShi);
				$("#KaiPiaoFangShi").html(data.KaiPiaoFangShi);
				$("#JiLiangFangShi").html(data.JiLiangFangShi);
				document.getElementById('jjqr0').checked=false;
				document.getElementById('jjqr1').checked=false;
				document.getElementById('jjqr'+data.jjqr).checked=true;
	} else {
		data = data.split("|");
		//Updated by quanjw for meijiao start 2015/2/2 根据后来传进来的第三位参数bigpz，在链接后面显示Vid
		//location.href="member.php?view=tczy&id="+data[0]+"&type="+data[1]
		location.href = "member.php?view=tczy&id=" + data[0] + "&type=" + data[1] + "&Vid=" + data[2];
		//Updated by quanjw for meijiao end 2015/2/2
	}

	//if($("#aedit").val()!=""){
	//$("#dt"+$("#aedit").val()).html(count);

	//alert("修改成功");
	//}else{
	//$("#detailtablist tbody").append(count);
	//}
	//closediv("zy");

}

function tczypostData() {

	if (document.zyform2.VarietyName.value == "") {
		alert("资源名称不能为空");
		return false;
	}

	//
	//if(document.zyform2.VarietyCode.value == ""){
	//alert("品种不能为空");
	//return false;
	//}


	if (document.zyform2.MaterialCode.value == "") {
		alert("材质不能为空");
		return false;
	}

	//if(document.zyform2.SpecCode.value == ""){
	//alert("规格不能为空");
	//return false;
	//}

	if (document.zyform2.SpecCode.value == "") { // && document.zyform2.hd.value == "" && document.zyform2.kd.value == ""&& document.zyform2.cd.value == ""
		alert("规格不能为空");
		return false;
	}

var trade = $("input[name=TradeType]:checked").val();
var zytype = document.getElementById("SalesType2").value;
if((zytype=='2'|| zytype=='8') && trade=='2'){
}else{
if(document.zyform2.OriginCode.value == "" ){
		alert("钢厂不能为空");
		return false;
	}
}
	if (document.zyform2.QuantitySales.value == "") {

		alert("数量不能为空");
		return false;
	}

	var dj = "";
	//if(document.zyform2.IsStaMoney){
	//var rlistdj = document.getElementsByName("IsStaMoney");
	//
	//for(var m=0;m<rlistdj.length;m++)
	//{
	//if(rlistdj[m].checked){dj =  rlistdj[m].value;}
	//}
	//
	//}

	submitRequest(document.zyform2, "post", "text", outplzytc2, null); //使用自定义myProcess
}
//Added by quanjw for jinshuzhipin start 2015/2/17
function tczypostData_jinshu(type) {

	if (document.zyform2.VarietyName.value == "") {
		alert("资源名称不能为空");
		return false;
	}

	//
	//if(document.zyform2.VarietyCode.value == ""){
	//alert("品种不能为空");
	//return false;
	//}


	if (document.zyform2.MaterialCode.value == "") {
		alert("材质不能为空");
		return false;
	}

	//if(document.zyform2.SpecCode.value == ""){
	//alert("规格不能为空");
	//return false;
	//}

	if (document.zyform2.SpecCode.value == "") { // && document.zyform2.hd.value == "" && document.zyform2.kd.value == ""&& document.zyform2.cd.value == ""
		alert("规格不能为空");
		return false;
	}

var trade = $("input[name=TradeType]:checked").val();
var zytype = document.getElementById("SalesType2").value;
if((zytype=='2'|| zytype=='8') && trade=='2'){
}else{
if(document.zyform2.OriginCode.value == "" ){
//if(document.zyform2.OriginCode.value == ""){
		alert("钢厂不能为空");
		return false;
	}
}
	if (document.zyform2.QuantitySales.value == "") {

		alert("数量不能为空");
		return false;
	}

    //update by zfy started 2019/09/26 增加焊网
    if ("undefined" == typeof type) {
        type = 'jinshu';
    }
    if (type=='hw'){
        if (document.zyform2.yongtu.value == "") {
            alert("抗拉不能为空");
            return false;
        }

        if (document.zyform2.strength.value == "") {
            alert("屈服不能为空");
            return false;
        }
    }else {
        if (document.zyform2.yongtu.value == "") {
            alert("用途不能为空");
            return false;
        }

        if (document.zyform2.strength.value == "") {
            alert("强度不能为空");
            return false;
        }
    }
    //update by zfy ended 2019/09/26 增加焊网

	var dj = "";
	//if(document.zyform2.IsStaMoney){
	//var rlistdj = document.getElementsByName("IsStaMoney");
	//
	//for(var m=0;m<rlistdj.length;m++)
	//{
	//if(rlistdj[m].checked){dj =  rlistdj[m].value;}
	//}
	//
	//}

	submitRequest(document.zyform2, "post", "text", outplzytc2, null); //使用自定义myProcess
}
//Added by quanjw for jinshuzhipin end 2015/2/17

//Added by quanjw for meijiao start 2015/2/10
function tczypostData_shuini() {

	if (document.zyform2.VarietyName.value == "") {
		alert("资源名称不能为空");
		return false;
	}

	//
	//if(document.zyform2.VarietyCode.value == ""){
	//alert("品种不能为空");
	//return false;
	//}


	//if(document.zyform2.SpecCode.value == ""){
	//alert("规格不能为空");
	//return false;
	//}

	if (document.zyform2.SpecCode.value == "") { // && document.zyform2.hd.value == "" && document.zyform2.kd.value == ""&& document.zyform2.cd.value == ""
		alert("规格不能为空");
		return false;
	}

var trade = $("input[name=TradeType]:checked").val();
var zytype = document.getElementById("SalesType2").value;
if((zytype=='2'|| zytype=='8') && trade=='2'){
}else{
if(document.zyform2.OriginCode.value == "" ){
//if(document.zyform2.OriginCode.value == ""){
		alert("钢厂不能为空");
		return false;
	}
}
	if (document.zyform2.QuantitySales.value == "") {

		alert("数量不能为空");
		return false;
	}

	var dj = "";
	//if(document.zyform2.IsStaMoney){
	//var rlistdj = document.getElementsByName("IsStaMoney");
	//
	//for(var m=0;m<rlistdj.length;m++)
	//{
	//if(rlistdj[m].checked){dj =  rlistdj[m].value;}
	//}
	//
	//}

	submitRequest(document.zyform2, "post", "text", outplzytc2, null); //使用自定义myProcess
}
//Added by quanjw for meijiao end 2015/2/10

function tczypostData_tks() {

	if (document.zyform2.VarietyName.value == "") {
		alert("资源名称不能为空");
		return false;
	}


	if (document.zyform2.tks_fe.value == "") {
		alert("Fe%不能为空");
		return false;
	}
    
    if ( ( document.zyform2.tks_fe.value.indexOf("%") > -1) || 
         ( document.zyform2.tks_si.value.indexOf("%") > -1) || 
         ( document.zyform2.tks_al.value.indexOf("%") > -1) || 
         ( document.zyform2.attr_b.value.indexOf("%") > -1) || 
         ( document.zyform2.attr_c.value.indexOf("%") > -1) || 
         ( document.zyform2.attr_d.value.indexOf("%") > -1)  
         ) {
		alert("Fe,SiO2,AL2O3,P,S,H2O,不能含%");
		return false;
	}
	
	if( document.zyform2.tks_si.value == "" || document.zyform2.tks_al.value == "" || document.zyform2.attr_b.value == "" || document.zyform2.attr_c.value == "" ){
		if( ! window.confirm('SiO2、AL2O3、P、S有空值，您确定要继续吗？')){
			return false;
		}
	}
var trade = $("input[name=TradeType]:checked").val();
var zytype = document.getElementById("SalesType2").value;
if((zytype=='2'|| zytype=='8') && trade=='2'){
}else{
if(document.zyform2.OriginCode.value == "" ){
	//if (document.zyform2.OriginCode.value == "") {
		alert("产地不能为空");
		return false;
	}
	}
	if (document.zyform2.QuantitySales.value == "") {

		alert("数量不能为空");
		return false;
	}

	var dj = "";

	submitRequest(document.zyform2, "post", "text", outplzytc2, null); //使用自定义myProcess
}

function outplzytc2(count) {

	if ($("#aedit").val() != "") {
		$("#dt" + $("#aedit").val()).html(count);
		//$('#aedit').val(data.id);
		alert("修改成功");
	} else {

		$("#detailtablist tbody").append(count);
	}
	closediv("zy");
	//var tmp = count.split("|-|");
	//location.href="member.php?view=jymanage&id="+tmp[31]+"&jg=1";
}

//Added by quanjw for meijiao start 2015/1/29 煤炭资源 打包 保存
function tczypostData_meitan() {

	if (document.zyform2.VarietyName.value == "") {
		alert("资源名称不能为空");
		return false;
	}

	//
	//if(document.zyform2.VarietyCode.value == ""){
	//alert("品种不能为空");
	//return false;
	//}


	if (document.zyform2.MaterialCode.value == "") {
		alert("挥发分不能为空");
		return false;
	}

	//if(document.zyform2.SpecCode.value == ""){
	//alert("规格不能为空");
	//return false;
	//}

	if (document.zyform2.SpecCode.value == "") { // && document.zyform2.hd.value == "" && document.zyform2.kd.value == ""&& document.zyform2.cd.value == ""
		alert("灰分不能为空");
		return false;
	}
	if (document.zyform2.cd.value == "") {
		alert("全硫分不能为空");
		return false;
	}
var trade = $("input[name=TradeType]:checked").val();
var zytype = document.getElementById("SalesType2").value;
if((zytype=='2'|| zytype=='8') && trade=='2'){
}else{
if(document.zyform2.OriginCode.value == "" ){
//if(document.zyform2.OriginCode.value == ""){
		alert("产地不能为空");
		return false;
	}
}
	if (document.zyform2.QuantitySales.value == "") {

		alert("数量不能为空");
		return false;
	}

	var dj = "";
	//if(document.zyform2.IsStaMoney){
	//var rlistdj = document.getElementsByName("IsStaMoney");
	//
	//for(var m=0;m<rlistdj.length;m++)
	//{
	//if(rlistdj[m].checked){dj =  rlistdj[m].value;}
	//}
	//
	//}

	submitRequest(document.zyform2, "post", "text", outplzytc_meitan, null); //使用自定义myProcess
}

function outplzytc_meitan(count) {

	if ($("#aedit").val() != "") {
		$("#dt" + $("#aedit").val()).html(count);
		//$('#aedit').val(data.id);
		alert("修改成功");
	} else {

		$("#detailtablist tbody").append(count);
	}
	closediv("zy");
	//var tmp = count.split("|-|");
	//location.href="member.php?view=jymanage&id="+tmp[31]+"&jg=1";
}
//Added by quanjw for meijiao end 2015/1/29

//Added by quanjw for meijiao start 2015/2/2
function tczypostData_jiaotan() {

	if (document.zyform2.VarietyName.value == "") {
		alert("资源名称不能为空");
		return false;
	}

	//
	//if(document.zyform2.VarietyCode.value == ""){
	//alert("品种不能为空");
	//return false;
	//}


	if (document.zyform2.MaterialCode.value == "") {
		alert("全硫分不能为空");
		return false;
	}

	if (document.zyform2.SpecCode.value == "") { // && document.zyform2.hd.value == "" && document.zyform2.kd.value == ""&& document.zyform2.cd.value == ""
		alert("灰分不能为空");
		return false;
	}

	if (document.zyform2.cd.value == "") {
		alert("CSR不能为空");
		return false;
	}
var trade = $("input[name=TradeType]:checked").val();
var zytype = document.getElementById("SalesType2").value;
if((zytype=='2'|| zytype=='8') && trade=='2'){
}else{
if(document.zyform2.OriginCode.value == "" ){
//if(document.zyform2.OriginCode.value == ""){
		alert("产地不能为空");
		return false;
	}
}
	if (document.zyform2.QuantitySales.value == "") {

		alert("数量不能为空");
		return false;
	}

	var dj = "";
	//if(document.zyform2.IsStaMoney){
	//var rlistdj = document.getElementsByName("IsStaMoney");
	//
	//for(var m=0;m<rlistdj.length;m++)
	//{
	//if(rlistdj[m].checked){dj =  rlistdj[m].value;}
	//}
	//
	//}

	submitRequest(document.zyform2, "post", "text", outplzytc_jiaotan, null); //使用自定义myProcess
}

function outplzytc_jiaotan(count) {

	if ($("#aedit").val() != "") {
		$("#dt" + $("#aedit").val()).html(count);
		//$('#aedit').val(data.id);
		alert("修改成功");
	} else {

		$("#detailtablist tbody").append(count);
	}
	closediv("zy");
	//var tmp = count.split("|-|");
	//location.href="member.php?view=jymanage&id="+tmp[31]+"&jg=1";
}
//Added by quanjw for meijia end 2015/2/2

function showtdfkxs(t,id){
	if($(t).html()=="+展开"){
		$(t).html("-收缩");
		$("#tdmx"+id).show();
	}
	else{
		$(t).html("+展开");
		$("#tdmx"+id).hide();
	}
}

function showtdmx(t, id) {
	if ($(t).html() == "+展开") {
		$(t).html("-收缩");
		$("#showalltdmx").html("-全部收缩");
		$("#tdmx" + id).show();
	} else {
		$(t).html("+展开");
		$("#showalltdmx").html("+全部展开");
		$("#tdmx" + id).hide();
	}
}

function showalltdmx(t) {
	if ($(t).html() == "+全部展开") {
		$(t).html("-全部收缩");
		$("a[id^='stdmxshow']").html("-收缩");
		$("tr[id^='tdmx']").show();
	} else {
		$(t).html("+全部展开");
		$("a[id^='stdmxshow']").html("+展开");
		$("tr[id^='tdmx']").hide();
	}
}

function edittczy(id) {

	jQuery.ajax({
		url : 'member.php?action=ajaxtczyedit',
		data : "id=" + id + "&time=" + new Date().getTime(), // 从表单中获取数据
		type : 'POST', // 设置请求类型为 ‘POST’，默认为 ‘GET’
		dataType : "json",
		success : function (data) {

			$("input[name='aedit']").val(id);
			$("input[name='hidd_attrid']").val("");
			$("input[name='hidd_attrid2']").val("");
			$("input[name='hidd_attrid3']").val("");

			editdiv("zy");

			$("input[name='StoreCityCode']").val(data.StoreCityCode);
			$("input[name='VarietyName']").val(data.VarietyName);
			//$("input[name='VarietyCode2']").val(data.biglei);
			//$("#VarietyCode2").val(data.biglei);
			//$("input[name='VarietyCode']").val(data.VarietyCode);

			$("input[name='MaterialCode']").val(data.MaterialCode);
			$("input[name='SpecCode']").val(data.SpecCode);
			$("input[name='OriginCode']").val(data.OriginCode);

			$("select[name=SalesUnit]").val(data.SalesUnit);

			//	getpz2(data.biglei,data.VarietyCode);

			// 2012年8月21日16:16:19

			$("input[name=SalesMinPrice]").val(data.SalesMinPrice);
			$("input[name='QuantitySales']").val(data.QuantitySales);
			$("input[name='WeightPerOne']").val(data.WeightPerOne);
			//$("input[name='PerNumber']").val(data.PerNumber);
			$("input[name='MfgDate']").val(data.MfgDate);

			$("input[name='QuantityMin']").val(data.QuantityMin);
			$("input[name='QuantitySalesed']").val(data.QuantitySalesed);
			$("input[name='SalesStartDate']").val(data.SalesStartDate);

			$("input[name='SalesEndDate']").val(data.SalesEndDate);
			//		$("input[name='LadderPrice']").val(data.LadderPrice);


			//2012年8月21日16:16:28


			//$("select[name='bzjflag']").val(data.bzjflag);

			//$("select[name='Delivery']").val(data.Delivery);


			$("input[name='jhck']").val(data.jhck);
			$("input[name='bmzk']").val(data.bmzk);

			$("input[name='bzqk']").val(data.bzqk);
			$("input[name='rclzk']").val(data.rclzk);
			$("input[name='bz']").val(data.bz);

			$("input[name='hd']").val(data.hd);
			$("input[name='kd']").val(data.kd);
			$("input[name='cd']").val(data.cd);
			$("input[name='cxname']").val(data.cxname);

			$("input[name='xyjgtypes']").val(data.xyjgtypes);

			//	$("input[name='AssureGuarpayMoney']").val(data.AssureGuarpayMoney);

			//	$("input[type=radio][name=AssureGuarpayType][value="+data.AssureGuarpayType+"]").attr("checked","checked");
			$("input[type=radio][name=bzjflag][value=" + data.bzjflag + "]").attr("checked", "checked");

			$("input[type=radio][name=Delivery][value=" + data.Delivery + "]").attr("checked", "checked");

			//	$("input[type=radio][name=IsStaMoney][value="+data.IsStaMoney+"]").attr("checked","checked");


			$("input[name='LyMoney']").val(data.LyMoney);
			//$("input[name='IsStaMoney']").val(data.IsStaMoney);
			if (data.IsMaxMoney == "1") {
				// $("[name='IsMaxMoney2']").attr("checked",'true');
			}

			//$("input[name='IsMaxMoney']").val(data.IsMaxMoney);


			//$("#div_Result").html(data.attrstr1);
			//$("#div_Result2").html(data.attrstr2);
			//$("#div_Result3").html(data.attrstr3);

			//Added by quanjw for meijiao start 2015/1/29
			//$("input[name='cd']").val(data.cd);
			$("input[name='attr_a']").val(data.attr_a);
			$("input[name='attr_b']").val(data.attr_b);
			$("input[name='attr_c']").val(data.attr_c);
			$("input[name='attr_d']").val(data.attr_d);
			$("input[name='attr_e']").val(data.attr_e);
			$("input[name='attr_f']").val(data.attr_f);
			$("input[name='cangku']").val(data.cangku);
			$("input[name='kuweihao']").val(data.kuweihao);
			$("input[name='bz']").val(data.bz);
			$("input[name='validDate']").val(data.validDate);
			//$("input[name='yongtu']").val(data.yongtu);
			//用途select 根据value值,选中
			$("select option[value='" + data.yongtu + "']").attr("selected", "selected");
			$("input[name='strength']").val(data.strength);
			$("input[name='xincengWeight']").val(data.xincengWeight);
			//Added by quanjw for meijiao end 2015/1/29
			
			$("input[name='tks_fe']").val(data.tks_fe);
			$("input[name='tks_si']").val(data.tks_si);
			$("input[name='tks_al']").val(data.tks_al);

		}
	});
}

function deletczy(id) {
	if (confirm('请确认是否删除！')) {
		jQuery.ajax({
			url : 'member.php?action=ajaxdeletczy',
			data : "id=" + id + "&time=" + new Date().getTime(), // 从表单中获取数据
			type : 'POST', // 设置请求类型为 ‘POST’，默认为 ‘GET’
			dataType : "json",
			success : function (data) {
				if (data.data == 1) {
					//$("#dt"+id).hide();
					$("#dt" + id).remove();
				} else {
					alert("删除失败");
				}
			}
		});
	}
}

function xypostData() {

	var f = document.xyform;

	if (f.JyBzj.value == '') {
		alert("交易保证金不能为空");
		return false;
	}
	if (f.PayDays.value == '') {
		alert("乙方付款期限不能为空");
		return false;
	}
	if (f.AccountName.value == '') {
		alert("账户名不能为空");
		return false;
	}
	if (f.AccountBank.value == '') {
		alert("开户行不能为空");
		return false;
	}
	if (f.PayAccount.value == '') {
		alert("账号不能为空");
		return false;
	}
	if (f.JhDays.value == '') {
		alert("交货期限不能为空");
		return false;
	}
	if (f.KpDays.value == '') {
		alert("开票期限不能为空");
		return false;
	}

	submitRequest(document.xyform, "post", "text", outWritexy, null); //使用自定义myProcess
}

//AJAX出现结果，处理变化
function outWritexy(count) {
	var tmp = count.split("|-|");

	getdiv("JyBzj2").innerHTML = tmp[1];
	getdiv("PayDays2").innerHTML = tmp[2];
	getdiv("AccountName2").innerHTML = tmp[3];
	getdiv("AccountBank2").innerHTML = tmp[4];
	getdiv("PayAccount2").innerHTML = tmp[5];
	getdiv("JhDays2").innerHTML = tmp[6];
	getdiv("KpDays2").innerHTML = tmp[7];

	getdiv("jjxyid").value = tmp[0];
	getdiv("xid").value = tmp[0];

	closediv("xy");
	document.getElementById("zydiv").style.display = "block";
	getdiv("isjjshow").style.display = "block";

}

function getdiv(id) {
	if (document.getElementById(id)) {
		return document.getElementById(id);
	}
}

function deledd(id) {
	// var id = document.getElementById("zyid").value;
	if (window.confirm("是否申请取消此订单?")) {
		var param = "action=delzydd&id=" + id;
		var ajax = new Ajax("member.php", setdeldd, param);
	}
}

function setdeldd(returnstr) {

	var str = returnstr;
	if (str == "1") {
		alert("申请成功");
		//location.href="member.php?view=newzylist";
	} else {
		alert("申请失败"); //location.href="member.php?view=newzylist";
	}
	location.reload();
}

function editbj() {

	document.getElementById("showbj").style.display = "none"; //隐藏
	document.getElementById("bjdiv").style.display = "block"; //隐藏

}

function closebj() {

	document.getElementById("showbj").style.display = "block";
	document.getElementById("bjdiv").style.display = "none";
}

//支付方式
function postbj(item) {

	var i = 0;
	for (i; i <= item; i++) {

		var sl = "sl" + item;
		//alert(document.getElementById(sl).value);
		if (document.getElementById(sl).value == "") {
			alert("数量不能为空");
			break;
		}
	}

	submitRequest(document.bjform, "post", "text", outWritebj, null); //使用自定义myProcess
}

function outWritebj(count) {
	//alert(count);

	document.getElementById("showbj").style.display = "block";
	document.getElementById("bjdiv").style.display = "none";

	document.getElementById("showbj").innerHTML = count;

}

function editcj() {

	document.getElementById("showcj").style.display = "none"; //隐藏
	document.getElementById("cjdiv").style.display = "block"; //隐藏

}

function closecj() {

	document.getElementById("showcj").style.display = "block";
	document.getElementById("cjdiv").style.display = "none";

}

function jgxxPay() {
	var f = document.jgxxform;
	if (checkradio("PickUpType") == "") {
		alert("请选择交割类型");
		return false;
	}

	if (checkradio("Delivery") == "") {
		alert("请选择提货方式");
		return false;
	}

	if (document.jgxxform.PickUpCity.value == "") {
		alert("交割城市不能为空");
		return false;
	}

	if (checkradio("ysfy") == "") {
		alert("运费承担方不能为空");
		return false;
	}
	submitRequest(document.jgxxform, "post", "text", outWritepaya, null); //使用自定义myProcess
}

function outWritepaya(count) {
	//alert(count);
	location.reload();
	var tmp = count.split("|-|");
	document.getElementById("PickUpType2").innerHTML = tmp[0];
	document.getElementById("PickUpDate2").innerHTML = tmp[1];
	document.getElementById("PickUpCity2").innerHTML = tmp[2];
	document.getElementById("PickUpAddress2").innerHTML = tmp[3];
	document.getElementById("Delivery2").innerHTML = tmp[4];
	document.getElementById("ysfy2").innerHTML = tmp[5];
	document.getElementById("jhck2").innerHTML = tmp[6];

	document.getElementById("PickUpCity4").innerHTML = tmp[2];
	document.getElementById("PickUpAddress4").innerHTML = tmp[3];
	document.getElementById("jhck4").innerHTML = tmp[6];

	document.getElementById("PickUpCity3").innerHTML = tmp[2];
	document.getElementById("PickUpAddress3").innerHTML = tmp[3];
	document.getElementById("jhck3").innerHTML = tmp[6];

	document.getElementById("IsDefault3").checked = true;

	document.getElementById("showjgxx").style.display = "block";
	document.getElementById("jgxxdiv").style.display = "none";

}

function editfhxx(id) {

	document.getElementById("showfhxx_" + id).style.display = "none";
	document.getElementById("fhxxdiv_" + id).style.display = "block";

	document.getElementById("qrdiv0").style.display = "none";
	document.getElementById("qrdiv").style.display = "none";
	document.getElementById("qrdiv2").style.display = "";
	document.getElementById("qrdiv3").style.display = "";

}
function closefhxx(id) {
	document.getElementById("showfhxx_" + id).style.display = "block";
	document.getElementById("fhxxdiv_" + id).style.display = "none";

	document.getElementById("qrdiv0").style.display = "";
	document.getElementById("qrdiv").style.display = "";
	document.getElementById("qrdiv2").style.display = "none";
	document.getElementById("qrdiv3").style.display = "none";

}

function fhxxPay(id) {

	if (document.getElementById("Delivery").value != "1") {
		//alert("IsDefault"+id);
		var ysid = 0;
		var temp = document.getElementsByName("IsDefault" + id);
		for (var i = 0; i < temp.length; i++) {
			if (temp[i].checked) {
				ysid = temp[i].value;
			}
		}

		if (ysid > 0) {
			document.getElementById("Yscom2" + id).value = ysid;
			//alert(document.getElementById( "IsDefaultTp"+ysid ).innerHTML);
			//alert(document.getElementById( "IsDefaultTm"+ysid ).innerHTML);
	 	document.getElementById( "Fhphone2"+id ).value = document.getElementById( "IsDefaultTp"+ysid ).innerHTML ;
	 	document.getElementById( "Fhlxr2"+id ).value = document.getElementById( "IsDefaultTm"+ysid ).innerHTML ;
		}
	}

	//var fhxxform = "document.fhxxform" + id;
	//alert(id);

	//	var param = "action=fhxx&id="+id;
	//   var ajax = new Ajax("bizorder.php", outWritefh5, param );

	//alert(document.getElementById( "Fhjs2"+id ).value);

	//alert(checkradio( "Yscom"+id ));
	//if( checkradio( "Yscom"+id ) == "" ){
	//	alert("请选择运输公司");
	//	return false;
	//}
	/*
	if(fhxx(id)=="1"){
	alert("请填写件重");return false;
	}else if(fhxx(id)=="2"){
	alert("请填写件数");return false;
	}else if(fhxx(id)=="3"){
	alert("请填写数量");return false;
	}*/

	if (fhxx(id) == "3") {
		alert("请填写数量");
		return false;
	}
	//return false;
	if (document.getElementById("Delivery").value != "1") {
		if (document.getElementById("Yscom2" + id).value == "" || document.getElementById("Yscom2" + id).value == "0") {
			alert("请选择运输公司");
			//alert("请选择运输公司["+"Yscom2"+id+"]["+document.getElementById( "Yscom2"+id ).value+"]");
			return false;
		}
	}
//	if(document.getElementById( "Fhsl2"+id ).value == "" || document.getElementById( "Fhsl2"+id ).value == "0" ){
//		alert("请填写数量");
//		return false;
//	}
//	if(document.getElementById( "Fhjs2"+id ).value == "" || document.getElementById( "Fhjs2"+id ).value == "0" ){
//		alert("请填写件数");
//		return false;
//	}

//alert(id);
	
	 document.getElementById("fhxxform").action="bizorder.php?action=fhxx&id="+id;
	 submitRequest(document.fhxxform,"post","text",outWritefh5,null);//使用自定义myProcess

}


// Added for fhxx by Zhu Dahua started  2015/09/16
function fhxxPay2(id){
	
	if(document.getElementById("Delivery").value!="1"){
	//alert("IsDefault"+id);
     var ysid = 0;
	 var temp = document.getElementsByName("IsDefault"+id);
	 for(var i=0;i<temp.length;i++)
	 {
	    if(temp[i].checked)
	    {
	         ysid = temp[i].value;
	    }
	 }
	 
	 if(ysid>0)
	 {
	 	document.getElementById( "Yscom2"+id ).value = ysid;
	 	//alert(document.getElementById( "IsDefaultTp"+ysid ).innerHTML);
	 	//alert(document.getElementById( "IsDefaultTm"+ysid ).innerHTML);
	 	document.getElementById( "Fhphone2"+id ).value = document.getElementById( "IsDefaultTp"+ysid ).innerHTML ;
	 	document.getElementById( "Fhlxr2"+id ).value = document.getElementById( "IsDefaultTm"+ysid ).innerHTML ;
	 }
	}
	
//var fhxxform = "document.fhxxform" + id;
	//alert(id);

	//	var param = "action=fhxx&id="+id;
	  //   var ajax = new Ajax("bizorder.php", outWritefh5, param );

//alert(document.getElementById( "Fhjs2"+id ).value);

	//alert(checkradio( "Yscom"+id ));
	//if( checkradio( "Yscom"+id ) == "" ){
	//	alert("请选择运输公司");
	//	return false;
	//}	
/*
if(fhxx(id)=="1"){
alert("请填写件重");return false;
}else if(fhxx(id)=="2"){
alert("请填写件数");return false;
}else if(fhxx(id)=="3"){
alert("请填写数量");return false;
}*/
	
	if(fhxx(id)=="3"){
	alert("请填写数量");return false;
	}
//return false;
	if(document.getElementById("Delivery").value!="1"){
		if(document.getElementById( "Yscom2"+id ).value == "" || document.getElementById( "Yscom2"+id ).value == "0" ){
			alert("请选择运输公司");
			//alert("请选择运输公司["+"Yscom2"+id+"]["+document.getElementById( "Yscom2"+id ).value+"]");
			return false;
		}
	}
//	if(document.getElementById( "Fhsl2"+id ).value == "" || document.getElementById( "Fhsl2"+id ).value == "0" ){
//		alert("请填写数量");
//		return false;
//	}
//	if(document.getElementById( "Fhjs2"+id ).value == "" || document.getElementById( "Fhjs2"+id ).value == "0" ){
//		alert("请填写件数");
//		return false;
//	}

//alert(id);
	 
	 document.getElementById("fhxxform").action="bizorder.php?action=fhxx2&id="+id;

	 submitRequest(document.fhxxform,"post","text",outWritefh5,null);//使用自定义myProcess
	 //alert(document.getElementById("fhxxform").action);

}
// Added for fhxx by Zhu Dahua ended  2015/09/16

function outWritefh5(count) {
	//alert(count);
	if (count == "1") {
		alert("资源发货数量已达到订购数量");
		return false;
	} else if (count == "9") {
		alert("发货单中发货数量需大于0！");
	}else if(count=="99"){
		alert("发货单中发货数量不能全部为0！");
	}
	window.location.reload();
	var tmp = count.split("|-|");
	document.getElementById("Fhsl" + tmp[8]).innerHTML = tmp[0];
	document.getElementById("Fhjz" + tmp[8]).innerHTML = tmp[1];
	document.getElementById("Fhjs" + tmp[8]).innerHTML = tmp[2];
	document.getElementById("Yscom" + tmp[8]).innerHTML = tmp[3];
	document.getElementById("Fhlxr" + tmp[8]).innerHTML = tmp[4];
	document.getElementById("Fhphone" + tmp[8]).innerHTML = tmp[5];
	document.getElementById("Carnum" + tmp[8]).innerHTML = tmp[6];
	document.getElementById("Fhbz" + tmp[8]).innerHTML = tmp[7];

	document.getElementById("showfhxx_" + tmp[8]).style.display = "block";
	document.getElementById("fhxxdiv_" + tmp[8]).style.display = "none";

}

function editshxx(id) {

	document.getElementById("showshxx_" + id).style.display = "none";
	document.getElementById("shxxdiv_" + id).style.display = "block";
}
function closeshxx(id) {
	document.getElementById("showshxx_" + id).style.display = "block";
	document.getElementById("shxxdiv_" + id).style.display = "none";
}
//added by hzp for ksdj started 2015/09/07
function ksdj_paytype(id){
	 //alert(id);
	  var temp = document.getElementsByName("PayType");
	  for(var i=0;i<temp.length;i++)
	  {
		 if(temp[i].checked)
			   var intHot = temp[i].value;
	  }
	  //alert(intHot);
	  var param1 = "action=ksdj_paytype&intHot="+intHot+"&id="+id;
	  //alert(param1);
      var ajax = new Ajax("bizorder.php",ksdj_State,param1);
  
 	
}
function ksdj_State(returnstr){
	//alert(returnstr);
	  if(returnstr=='1'){
		  alert("该客户集团评级为C级，所签合同付款方式不能为先款后货");
	  }
}
//added by hzp for ksdj end 2015/09/07
function shxxPay(id){

	//
	//	if(document.getElementById( "Shsl2"+id ).value == "" || document.getElementById( "Shsl2"+id ).value == "0" ){
	//		alert("请填写数量");
	//		return false;
	//	}
	//	if(document.getElementById( "Shjs2"+id ).value == "" || document.getElementById( "Shjs2"+id ).value == "0" ){
	//		alert("请填写件数");
	//		return false;
	//	}


	//alert(id);

	/*if(fhxx(id)=="1"){
	alert("请填写件重");return false;
	}else if(fhxx(id)=="2"){
	alert("请填写件数");return false;
	}else if(fhxx(id)=="3"){
	alert("请填写数量");return false;
	}
	 */
	if (fhxx(id) == "3") {
		alert("请填写数量");
		return false;
	}

	document.getElementById("shxxform").action = "bizorder.php?action=shxx&id=" + id;
	submitRequest(document.shxxform, "post", "text", outWritefh6, null); //使用自定义myProcess

}

function outWritefh6(count) {
	//alert(count);
	location.reload();
	//     var tmp = count.split("|-|");
	//	 document.getElementById( "Shsl"+tmp[6] ).innerHTML = tmp[0];
	//	 document.getElementById( "Shjz"+tmp[6] ).innerHTML = tmp[1];
	//	 document.getElementById( "Shjs"+tmp[6] ).innerHTML = tmp[2];
	//	 document.getElementById( "Shlxr"+tmp[6] ).innerHTML = tmp[3];
	//	 document.getElementById( "Shphone"+tmp[6] ).innerHTML = tmp[4];
	//	 document.getElementById( "Shbz"+tmp[6] ).innerHTML = tmp[5];

	document.getElementById("showshxx_" + tmp[6]).style.display = "block";
	document.getElementById("shxxdiv_" + tmp[6]).style.display = "none";
}

function editjsxx() {

	document.getElementById("btn_qrjs").src = "images/btn_qrjs_gray.png";
	//document.getElementById("btn_qrjs").onclick="";
	document.getElementById("showjsxx").style.display = "none";
	document.getElementById("jsxxdiv").style.display = "block";
}
function closejsxx() {
	var obj = document.getElementById("btn_qrjs");
	document.getElementById("btn_qrjs").src = "images/btn_qrjs.png";
	//document.getElementById("btn_qrjs").onclick="qrjg(obj);";
	document.getElementById("showjsxx").style.display = "block";
	document.getElementById("jsxxdiv").style.display = "none";
}

function editqtfy() {
	document.getElementById("btn_qrjs").src = "images/btn_qrjs_gray.png";
	// document.getElementById("btn_qrjs").onclick="";
	document.getElementById("showqtfy").style.display = "none";
	document.getElementById("qtfydiv").style.display = "block";
}
function closeqtfy() {
	var obj = document.getElementById("btn_qrjs");
	document.getElementById("btn_qrjs").src = "images/btn_qrjs.png";
	//document.getElementById("btn_qrjs").onclick="qrjg(obj);";
	document.getElementById("showqtfy").style.display = "block";
	document.getElementById("qtfydiv").style.display = "none";
}



//支付尾款
// Added for quanxian sheding  by hxy started  2015/1/13
function zfwk(obj){

	var id = document.getElementById("htid").value;
	var mima = document.getElementById("mima").value;
	var mima2 = document.getElementById("mima2").value;
	//addby xiakang started 2016/02/15
	var Mid = document.getElementById("Mid").value;
	var Dmid = document.getElementById("Dmid").value;
	//addby xiakang ended 2016/02/15
	  zfwk_id=id;
	  zfwk_mima=mima;
if(mima==""){
	mima = mima2;
	zfwk_mima=mima2;
}
	 if(mima == ""){
		alert("交易密码不能为空");
		return false;
	 }

	 //add by xiakang for pingan started 2016/01/13
	 var ApprovalStatus1 = document.getElementById("ApprovalStatus1").value;
	 var ApprovalStatus2 = document.getElementById("ApprovalStatus2").value;
	 var ApprovalMode = document.getElementById("ApprovalMode").value;
	 var Mid = document.getElementById("Mid").value;
	 var Dmid = document.getElementById("Dmid").value;
	 if(ApprovalStatus2=="30" && (ApprovalMode=="1" || ApprovalMode=="2")){
	     var shly = document.getElementById('shly').value;
	     
         var A = document.getElementById('agree').checked;
	     var B = document.getElementById('unagree').checked;
	     var isagree = "";
	     if(A ==false && B== false){
		     alert("请选择是否通过");return false;
	     }
		 if(B==true && shly==""){
	         alert("请填写备注原因");return false;
	     }
	 }
	 if(ApprovalMode!=""){
		if(ApprovalStatus1=="-1"){
		  alert("初次审核未完成，耐心等待");
		  return false;
		}else if((ApprovalStatus2!="30" && ApprovalStatus2!="2" && ApprovalStatus2!="0") && (ApprovalMode=="1" || ApprovalMode=="2")){
          alert("融资审核未完成，请您等待审批结果");
		  return false;
        }else if(ApprovalStatus2=="2" && (ApprovalMode=="1" || ApprovalMode=="2")){
          alert("您已支付，请您耐心等待平安银行出账");
		  return false;
        }

	 }
   //add by xiakang for pingan ended 2016/01/13

	obj.onclick = function () {};
	//update by xiakang started 2016/02/15
	 //obj.src="images/btn_zfwk_gray.png";
     if(Mid==Dmid){
		obj.src="images/btn_zfwk_gray.png";
	 }else{
		obj.src="images/btn_qrsk_gray.png";
     }
   //update by xiakang ended 2016/02/15
	 obj.onclick="";
  
		document.getElementById("loading").style.display="";
		// var param = "action=zfwk&id="+id+"&mima="+mima;
	     //var ajax = new Ajax("bizorder.php", setWk, param );
		 var param1 = "action=GetUserState";
      var ajax = new Ajax("member.php",GetState7,param1);
 }
var zfwk_id='';
var zfwk_mima='';
function GetState7(returnstr){
	 var ApprovalStatus1 = document.getElementById("ApprovalStatus1").value;
     var ApprovalMode = document.getElementById("ApprovalMode").value;
     if(ApprovalMode!=""){
		 var shly = document.getElementById("shly").value;
         var A = document.getElementById('agree').checked;
         var B = document.getElementById('unagree').checked;
	     var isagree = "";
	     if(A ==true){
		     var isagree="0";
	     }
		 if(B ==true){
		     var isagree="1";
	     }
	 }
		var par=returnstr.split(";");
         if((parseInt(par[1])==1)||(parseInt(par[2])==1)){
		   if(ApprovalMode!=""){
			 var param = "action=zfwk&id="+zfwk_id+"&mima="+zfwk_mima+"&ApprovalStatus1="+ApprovalStatus1+"&ApprovalMode="+ApprovalMode+"&isagree="+isagree+"&ApprovalRemark2="+shly;
	         var ajax = new Ajax("bizorder.php", setWk, param );
		   }else{
		     var param = "action=zfwk&id="+zfwk_id+"&mima="+zfwk_mima;
	         var ajax = new Ajax("bizorder.php", setWk, param );
		   }
		 }
		 else{
		  alert("对不起，您没有操作权限");
		 document.getElementById("loading").style.display="none";
		 window.location.reload();
		 }
	        
	 }
//Added for quanxian sheding  by hxy ended  2015/1/30
function setWk(returnstr) {
	//alert(returnstr);
	document.getElementById("loading").style.display = "none";
	//alert(returnstr);
	var id = document.getElementById("ddid").value;
	//Add by xiakang for zhifu started 2015/04/27
	String.prototype.trim = function () {

			return this.replace(/(^\s*)|(\s*$)/g,'');
		}
	 var str = returnstr.trim();
	//add by xiakang for pingan started 2016/1/20
    var ApprovalMode = document.getElementById("ApprovalMode").value;
    if(ApprovalMode=="2"){
        var str=str.split("|");
	    if(str[0]=="1"){
	       alert(str[1]+"请继续点击支付。");
	    }
	    if(str[0]=="2"){
	       alert("支付成功，请等待平安出账");
	    }
	    if(str[0]=="3"){
	       //alert("选择拒绝，您已取消平安融资支付方式,请等待平安返回信息");
	       //alert("返回代码"+str[1]+"（"+str[2]+"）");
	       alert(str[2]);
	    }
        if(str[0]=="4"){
	       //alert("选择拒绝，发送信息给平安失败，请继续选择");
		   //alert("返回代码"+str[1]+"（"+str[2]+"）");
		   alert(str[2]);
	    }
	    location.reload() ;
    }
 //add by xiakang for pingan ended 2016/1/20

	if (str == "9") {
		alert("支付密码错误");
		location.reload();
	} else if (str == "1") {
		alert("支付尾款成功");
		location.href = "bizorder.php?view=orderdetail&id=" + id;
		document.getElementById("qrdiv").style.display = "none";

	} else if (str == "2") {
		alert("确认收尾款成功");
		location.href = "bizorder.php?view=orderdetail&id=" + id;
		document.getElementById("qrdiv").style.display = "none";
	} else {
		location.href = "bizorder.php?view=orderdetail&id=" + id;
		document.getElementById("qrdiv").style.display = "none";
	}
	//Added by quanjw for quanxian sheding by quanjw start 2015/4/1
	//少括号
}
//Added by quanjw for quanxian sheding by quanjw end 2015/4/1
//Added by quanjw for jinshuzhipin start 2015/2/14
// 获取URL的参数
var $_GET = (function () {
	var url = window.document.location.href.toString();
	var u = url.split("?");
	if (typeof(u[1]) == "string") {
		u = u[1].split("&");
		var get = {};
		for (var i in u) {
			var j = u[i].split("=");
			get[j[0]] = j[1];
		}
		return get;
	} else {
		return {};
	}
})();
//Added by quanjw for jinshuzhipin end 2015/2/14

//Added by quanjw for meijiao start 2015/8/12
//首页搜索按钮提交时获取ZiyuType的值
function check(){
	var value=$('input[name="ZiyuType"]:checked').val();
	document.reqForm.ZiyuType.value = value;
}
//Added by quanjw for meijiao start 2015/8/12